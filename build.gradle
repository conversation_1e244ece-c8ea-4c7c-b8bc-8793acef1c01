/*
 * This build file was auto generated by running the Gradle 'init' task
 * by 'sharma' at '3/21/17 10:27 AM' with Gradle 2.9
 *
 * This generated file contains a sample Java project to get you started.
 * For more details take a look at the Java Quickstart chapter in the Gradle
 * user guide available at - https://docs.gradle.org/2.9/userguide/tutorial_java_projects.html
 */


// Apply the java plugin to add support for Java
apply plugin: 'java'
apply plugin: 'eclipse'
apply plugin: 'idea'
apply plugin: 'com.github.johnrengelman.shadow'
apply plugin: 'jacoco'
apply from: rootProject.file('jmh.gradle')


version = '1.5'
    
// In this section you declare where to find the dependencies of your project
buildscript {
    repositories {
        jcenter()
    }
    dependencies {
       // classpath 'com.github.jengelman.gradle.plugins:shadow:1.2.2'
        classpath 'com.github.jengelman.gradle.plugins:shadow:2.0.2'
        classpath "org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:3.3"
    }
}

apply plugin: "org.sonarqube"

eclipse {
    classpath {
       downloadSources = true
    }
}

jacoco {
    toolVersion = "0.8.2"
}

repositories {
    // Use 'jcenter' for resolving your dependencies.
    // You can declare any Maven/Ivy/file repository here.
    maven {
            url "http://nexus.sca.dc.integral.net:8081/repository/integral-modules"
    }
    maven {
            url "http://nexus.sca.dc.integral.net:8081/repository/3rdParty"
    }
    jcenter()
}

sourceSets {
    main {
	   resources{
			srcDir 'conf'
	   }
	}	
	test {        		
		resources{
			srcDir file('test/resources')
		}
	}
	
	jacocoTestReport {
        reports {
            html.enabled = true
            xml.enabled = false
            csv.enabled = false
        }
    }
}

compileJava {
	sourceCompatibility = "1.8"
	targetCompatibility = "1.8"
}


test {
	ignoreFailures = true
    exclude '**/RDSBaseTest.class'
    exclude '**/*TestPriceBookSink.class'
}

// In this section you declare the dependencies for your production and test code
dependencies {

    //compile files('C:/svn_views/integral-modules/model/build/libs/model.jar')

	compile 'org.agrona:agrona:0.9.6'
    compile "com.integral:log:1.0.2"
	compile "com.integral:util:1.0"
	compile "com.integral:serializer:1.3"
	compile "com.integral:model:5.5"
	compile "com.integral:messaging:1.0.2"
	compile "com.integral:rds:2.3"
	compile 'com.integral:clustering:1.0.1'
	compile "org.jctools:jctools-core:2.1.2"
	compile "org.openjdk.jol:jol-core:0.9"
    compile group: 'org.hdrhistogram', name: 'HdrHistogram', version: '2.1.10'

    compile 'io.micrometer:micrometer-registry-prometheus:1.5.2'
    compile 'com.orbitz.consul:consul-client:1.4.0'

    compile group: 'com.hazelcast', name: 'hazelcast', version: '4.1'
    compile group: 'com.integral', name: 'multicast', version: '1.0'

    //Define all the run time dependencies here 
	runtime "org.eclipse.jetty.aggregate:jetty-all-server:8.1.15.v20140411" 
	runtime "com.google.guava:guava:15.0"
	 

    testCompile 'junit:junit:4.11'
    testCompile "org.mockito:mockito-all:2.0.2-beta"
	testCompile "com.integral:rds:2.3:tests"
    testCompile "org.openjdk.jmh:jmh-core:1.18"
    testCompile "org.openjdk.jmh:jmh-generator-annprocess:1.18"

	testRuntime "org.mongodb:mongo-java-driver:1.0.0"
//	testRuntime "io.netty:netty-all:4.1.0.CR3"

	testRuntime "com.integral:spaces:2.0"
	testRuntime "com.integral:serializer:1.3"
}


task copyJarToLib(type: Copy) {
    from "${buildDir}/libs/mdf-1.0.jar"
    into "$rootDir/lib"
}

task copyDependencies(type: Copy){
	//clean up lib and re-create 
	def binDir = new File("$rootDir/lib")
    binDir.deleteDir()
	into "$rootDir/lib" from configurations.runtime 
}

shadowJar {
   manifest {
        attributes "Main-Class": "com.integral.mdg.Main"
        attributes "Build-Version": version		
        attributes 'Build-Time-ISO-8601': new Date().format("yyyy-MM-dd'T'HH:mm:ssZ")
		attributes 'Built-By': System.getProperty('user.name')
        attributes 'Built-JDK': System.getProperty('java.version')		
		attributes 'Source-Compatibility': project.sourceCompatibility
    }	  
}


jacocoTestReport() {
    group = "Reporting"
    description = "Generate Jacoco coverage reports after running tests."
	reports {
		html.enabled = true
        xml.enabled=false
        csv.enabled=false
        html.destination= file("${buildDir}/reports/jacoco/html")
    }    
}

build.finalizedBy(copyDependencies)

copyDependencies.dependsOn copyJarToLib
