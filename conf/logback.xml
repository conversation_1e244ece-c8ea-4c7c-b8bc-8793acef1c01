<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="2 minutes">

    <!-- Send debug messages to System.out -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- By default, encoders are assigned the type ch.qos.logback.classic.encoder.PatternLayoutEncoder -->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} %t %msg%n
            </pattern>
        </encoder>
    </appender>

	<appender name="AlertMessages" class="ch.qos.logback.core.rolling.RollingFileAppender">
           <File>@integral.logs.root@/@integral.app.name@/FXIAlert.log</File>
           <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>@integral.logs.root@/@integral.app.name@/FXIAlert.log.%d{yyyy-MM-dd-HH}</FileNamePattern>
           </rollingPolicy>
           <encoder>
             <Pattern>%d %5p %t %m%n</Pattern>
           </encoder>
    </appender>

	<appender name="MetricsLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>@integral.logs.root@/@integral.app.name@/metrics.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>@integral.logs.root@/@integral.app.name@/metrics.log.%d{yyyy-MM-dd-HH}</FileNamePattern>
		</rollingPolicy>
		<encoder>
			<Pattern>%d %5p %t %m%n</Pattern>
		</encoder>
	</appender>

	<appender name="RateLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>@integral.logs.root@/@integral.app.name@/rateinput.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>@integral.logs.root@/@integral.app.name@/rateinput.log.%d{yyyy-MM-dd-HH}</FileNamePattern>
		</rollingPolicy>
		<encoder>
			<Pattern>%d %5p %t %m%n</Pattern>
		</encoder>
	</appender>

	<!--
        This appender is used to log alert messages
    -->
	<logger name="com.integral.alert.message" additivity="false" level="INFO">
	   <appender-ref ref="AlertMessages"/>
	</logger>	

	<logger name="org.apache.zookeeper.ClientCnxn" additivity="false" level="INFO">
		<appender-ref ref="STDOUT"/>
		<!-- <appender-ref ref="STDOUT_1"/> -->
	</logger>
	
	<logger name="com.integral.mdf.data.FullBookPriceBookV2" additivity="false" level="DEBUG">
		<appender-ref ref="STDOUT"/>
	</logger>

	<logger name="com.integral.mdf.data.ArrayRawPriceBook" additivity="false" level="DEBUG">
		<appender-ref ref="STDOUT"/>
	</logger>	
	
	<logger name="com.integral.mdf.data.BestPriceBook" additivity="false" level="DEBUG">
		<appender-ref ref="STDOUT"/>
	</logger>

	<logger name="com.integral.mdf.data.MultiTierFOKBook" additivity="false" level="DEBUG">
		<appender-ref ref="STDOUT"/>
	</logger>

	<logger name="com.integral.mdf.data.RawDirectBook" additivity="false" level="DEBUG">
		<appender-ref ref="STDOUT"/>
	</logger>

	<logger name="com.integral.mdf.data.VWAPBook" additivity="false" level="INFO">
		<appender-ref ref="STDOUT"/>
	</logger>

	<logger name="com.integral.mdf.data.MTFABook" additivity="false" level="DEBUG">
		<appender-ref ref="STDOUT"/>
	</logger>
	
	<logger name="com.integral.mdf.rate.provisioning.ProvisioningCalculator" additivity="false" level="INFO">
		<appender-ref ref="STDOUT"/>
	</logger>

	<logger name="com.integral.mdf.rate.TimeSlicedRateBook" additivity="false" level="INFO">
		<appender-ref ref="STDOUT"/>
	</logger>

	<logger name="com.integral.metrics" additivity="false" level="INFO">
		<appender-ref ref="MetricsLog"/>
	</logger>

	<logger name="rates.inactive" additivity="false" level="INFO">
		<appender-ref ref="RateLog"/>
	</logger>

    <!-- By default, the level of the root level is set to DEBUG -->
    <root level="INFO">
        <appender-ref ref="STDOUT" />
    </root>
</configuration>