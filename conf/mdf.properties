Idc.RDS.Server.url = http://localhost:9085
# The client API version to be used
Idc.RDS.Client.API.Version = 6.5
#This should be userPrefix + "_" + virtualServerName
Idc.RDS.Client.Id = DEFAULT_ISFXI_DEFAULT_MDF1
IDC.IS.AsyncDB.LOG.ROOTPATH = .
dataCenterName = NEWYORK
virtualServerName = DEFAULT_MDF1
userPrefix = DEFAULT_ISFXI
#The Port should be in sync with publisher (MDF server) and receiver (MDG server)
multicastPort = 9271

provision.currency.page.size = 50
provision.rate.convention.page.size = 100
#Rds client time out in milliseconds mdg provisioning
rds.client.timeout = 15000

Idc.RDS.Connect.Timeout = 15000
Idc.RDS.Read.Timeout= 15000

provision.rate.conventions = STDQOTCNV
maximumNoOfRateProcessors = 2
#Rate Processor Thread idle interval time 
processorThreadIdleInterval = 100
#Live Rate message size
liveRateMessageSize = 2048
useSuperLPStreamIndex = true

clusterNamePrefix=MDFCluster-
ondemandClusterNamePrefix=MDF-OnDemand-

Idc.RDS.Client.Connector.properties=minThreads=1,maxThreads=5

ratesFromMachingEngine = true

ratesListenerFromProperties = true

pricebookMaxDepth=5

# if true then quote's state will be dependent of valuedate's value. If false then value date on
# quote will be ignored if not set.
ValidateValueDate=true

# Node's operation mode
# 0 - Clustered  - The organizations served by this node will be decided by cluster. Supports fail-over
# 1 - Standalone - This will not be part of cluster. the 'Tenants' property will decide organizations served by this node. No fail over. Run alone die alone.
OperationMode=0

Tenants=

maxAggregationAttempts=10