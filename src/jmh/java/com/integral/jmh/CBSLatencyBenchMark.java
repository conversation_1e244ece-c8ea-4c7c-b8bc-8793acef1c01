package com.integral.jmh;

import com.integral.mdf.rate.ConcurrentBitSet;
import org.agrona.hints.ThreadHints;
import org.openjdk.jmh.annotations.*;

import java.util.concurrent.TimeUnit;

@State(Scope.Thread)
@BenchmarkMode(Mode.AverageTime)
@OutputTimeUnit(TimeUnit.NANOSECONDS)
@Warmup(iterations = 10, time = 1, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 10, time = 1, timeUnit = TimeUnit.SECONDS)
@SuppressWarnings("serial")
public class CBSLatencyBenchMark {

    static final ConcurrentBitSet cbs = new ConcurrentBitSet(512);

    private ConcurrentBitSet.ClearingBitIterator bitIt = cbs.iterator();

    @Setup(Level.Trial)
    public void setupConcurrentBitSet() {
        cbs.set(25);
        cbs.set(50);
        cbs.set(50);
        cbs.set(70);
        cbs.set(150);
        cbs.set(350);
        cbs.set(511);
    }

    @Benchmark
    public void clearingCost() {
        bitIt.reset();
        try {
            //
            bitIt.reset();
            for (int currIndex = bitIt.nextSetBit(0); currIndex >= 0;
                 currIndex = bitIt.nextSetBit(currIndex + 1)) {
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

}

