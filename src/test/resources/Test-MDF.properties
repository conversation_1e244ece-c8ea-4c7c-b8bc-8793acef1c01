# mongo to be used for jen<PERSON>
cluster.clusterone.url=mvmongo2.sca.dc.integral.net:60000
cluster.clusterone.connectionSize=1
cluster.clusterone.writeConnectionSize=3

metaspace.TEST-RDS.readOnly=false
metaspace.TEST-RDS.cluster=clusterone
metaspace.TEST-RDS.reliabilityLevel=PRIMARY_RELIABILITY
metaspace.TEST-RDS.serializationSize=1048576
metaspace.TEST-RDS.enableNotifications=false
metaspace.TEST-RDS.bufferSize=8
metaspace.TEST-RDS.concurrencyFactor=1
metaspace.TEST-RDS.txnLoggingEnabled=false
metaspace.TEST-RDS.metaspacePrefix=Test

mongodb.prefix=Test
spacesWorkflowEnabled=true
maximumNoOfRateProcessors=1
virtualServerName=MDF1-Test
dataCenterName=Test
IDC.Server.URL=http://localhost:9085
txLogLocation=.
#The Port should be in sync with publisher (MDF server) and receiver (MDG server)
multicastPort=9272
userPrefix=DEFAULT_ISFXI
currency.provision.page.size=50
rate.convention.provision.page.size=25
#Rds client time out in milliseconds
rds.client.timeout=15000

# Test environment configurations
Idc.RDS.Server.Port=9085
Idc.RDS.Server.url=http://localhost:9085
Idc.RDS.AutoID.SEQ=TEST-RDS_SEQ
Idc.RDS.Client.API.Version=6.4
Idc.RDS.Client.Id=Test_RDS_MDF1

test.config.metaspace.collection=TEST-RDS
test.config.metaspace.metaSpaceName=TEST-RDS
test.config.metaspace.cleanup.spaces=MOCKUSER,SEQ,CONTACT,USERPROVISION,VERSIONEDENTITY,DUMMYORACLEENTITY,MDFENTITY,MULTICASTADDRESS

Idc.RDS.AMQP.url=mvsonic.integral.com:5672
