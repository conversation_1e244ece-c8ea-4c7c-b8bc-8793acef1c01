package com.integral.mdf.proivision.builder;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.assertFalse;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.Properties;
import java.util.Set;

import org.junit.BeforeClass;
import org.junit.Test;

import com.integral.log.LogRingBufferMgr;
import com.integral.mdf.RDSBaseTest;
import com.integral.mdf.ClientInitializer;
import com.integral.mdf.Util;
import com.integral.mdf.data.FIProvision;
import com.integral.mdf.data.FIProvisionImpl;
import com.integral.mdf.data.ServerProvision;
import com.integral.mdf.data.ServerProvisionImpl;
import com.integral.mdf.proivision.MDFProvisionDataService;
import com.integral.provision.CurrencyPairProvision;
import com.integral.provision.CurrencyProvision;
import com.integral.provision.SpreadRuleParameterProvision;
import com.integral.virtualserver.MDFEntity;

public class RemoteProvisionBuilderTest extends RDSBaseTest {

	private static final String EUR_USD = "EUR/USD";

	private static final String AUD = "AUD";

	private static final String USD = "USD";

	private static final String EUR = "EUR";

	private static final String TEST_CONFIG_PROPS = "Test-MDF.properties";

	private RemoteProvisionBuilder builder;

	@BeforeClass
	public static void startUp() {
		LogRingBufferMgr bufferMgr = new LogRingBufferMgr(Util.housekeeper);
		LogRingBufferMgr.setInstance(bufferMgr);
	}

	@Test
	public void testProvisionBuildingFromRDS() throws Exception {
		buildMDFEntityDataInRDS();

		buildServerRunTimeData();

		buildOrgProvisionData();

		buildCcyProvisionData();
		
		buildCcyPairProvisionData();

		Properties properties = new ClientInitializer(TEST_CONFIG_PROPS)
				.initRDSClient(null, null, null);

		builder = new RemoteProvisionBuilder(properties);

		MDFProvisionDataService service = MDFProvisionDataService.getInstance();
		service.init(builder);

		ServerProvision serverProvision = service.getServerProvision();

		MDFEntity entity = service.getEntity();

		assertNotNull(entity);

		assertEquals(1, serverProvision.getMaxRateProcessorCount());

		assertEquals(1000, serverProvision.getThreadIdleTimeInterval());

		assertEquals(entity.getTradingVenue(), serverProvision.getVenueName());

		assertEquals(PRICE_BOOK_PORT, serverProvision.getPriceBookMulticastPort());

		assertEquals(PA_MC_GROUP, serverProvision.getRateMulticastgroup().get().getHostAddress());

		assertEquals(PA_MC_PORT, serverProvision.getRateMulticastPort());
		
		assertEquals(CREDIT_MC_PORT, serverProvision.getCreditMulticastPort());

		assertEquals(CREDIT_MC_GROUP, serverProvision.getCreditMulticastGroup().get().getHostAddress());

		assertEquals(LR_MC_PORT, serverProvision.getLiveRateMulticastPort());

		assertEquals(LR_MC_GROUP, serverProvision.getLiveRateMulticastgroup().get().getHostAddress());

		assertEquals(2048, serverProvision.getLiveRateMessageSize());

		assertFalse(serverProvision.isUseSuperLPStreamIndex());

		assertEquals(107, serverProvision.getMaxCurrencyIndex());

		// /Currency provision assertions
		assertEquals(Optional.of(92), serverProvision.getCcyIndex(EUR));

		assertEquals(Optional.of(USD), serverProvision.getCcyName(75));

		ServerProvisionImpl serverProvisionImpl = (ServerProvisionImpl) serverProvision;

		assertEquals(107, serverProvisionImpl.getCcyIndexVsNames().size());
		assertEquals(107, serverProvisionImpl.getCcyNamesVsIndex().size());

	}

	@Test
	public void testProvisionBuildingFromRDSForFIProvisioning()
			throws Exception {
		buildMDFEntityDataInRDS();

		buildServerRunTimeData();

		buildOrgProvisionData();

		buildCcyProvisionData();

		buildCcyPairProvisionData();

		buildLPProvisionData();

		Properties properties = new ClientInitializer(TEST_CONFIG_PROPS)
				.initRDSClient(null, null, null);
		
		properties.setProperty("ratesFromMachingEngine", Boolean.TRUE.toString());

		builder = new RemoteProvisionBuilder(properties);

		MDFProvisionDataService service = MDFProvisionDataService.getInstance();
		service.init(builder);

		ServerProvision serverProvision = service.getServerProvision();
		
		assertEquals(VENUE_MC_ADDRESS, serverProvision.getRateMulticastgroup().get().getHostAddress());
		assertEquals(VENUE_PORT, serverProvision.getRateMulticastPort());

		assertNotNull(serverProvision);

		service.provision(TESTFI1);

		int orgIndex = serverProvision.getOrgIndex(TESTFI1).get();

		assertEquals(1001, orgIndex);

		assertEquals("************", serverProvision
				.getPriceBookMulticastgroup(orgIndex).get().getHostAddress());

		FIProvision fiProvision = serverProvision.getFIProvision(1001);
		
		assertNotNull(fiProvision);
		
		Optional<CurrencyPairProvision> ccyPairProvision = serverProvision.getCcyPairProvision(EUR_USD);
		assertEquals(4,ccyPairProvision.get().getSpotPrecision());
		
		Integer eurIndex = serverProvision.getCcyIndex(EUR).get();
		CurrencyProvision currencyProvision = serverProvision.getCcyProvision(eurIndex).get();
		assertNotNull(currencyProvision);
		
		assertEquals(0.01d, currencyProvision.getTickValue(),0.01d);
		assertEquals(BigDecimal.ROUND_DOWN, currencyProvision.getRoundingType());
		
		Set<Integer> supportedCcyPairs = fiProvision.getSupportedCcyPairs();
		assertEquals(8, supportedCcyPairs.size());

		// standard quote conventions
		FIProvisionImpl fiProvisionImpl = (FIProvisionImpl) fiProvision;
		assertEquals(44, fiProvisionImpl.getRateConventions().size());

	//	assertEquals(4, fiProvisionImpl.getSpreadProvisionMap().size());

		List<SpreadRuleParameterProvision> spreads = fiProvision.getSpreads(1,eurIndex,
				serverProvision.getCcyIndex(USD).get());
	//	assertEquals(2,spreads.size());

	//	spreads = fiProvision.getSpreads(1,serverProvision.getCcyIndex(AUD).get(), serverProvision.getCcyIndex(USD).get());

	//	assertTrue(spreads.isEmpty());
		
		assertEquals(2, fiProvisionImpl.getMarketDepth());
	}

	@Override
	protected String getConfigFileName() {
		return TEST_CONFIG_PROPS;
	}

}
