package com.integral.mdf.proivision;

import org.junit.Assert;
import org.junit.Test;

import com.integral.mdf.data.ServerProvisionImpl;
import com.integral.mdf.proivision.ProvisionValidator;

public class ProvisionValidatorTest {
	
	@Test
	public void testValidateRuntimeShouldValidateTheVariousMulticastPort(){
		
		ProvisionValidator instance = ProvisionValidator.getInstance();
		
		ServerProvisionImpl serverProvision = new ServerProvisionImpl();
		serverProvision.setRateMulticastPort(-1);
		
		try{
			instance.validateRuntime(serverProvision);
			Assert.fail("should throw exception");
		}catch(IllegalArgumentException iae){
			Assert.assertEquals("Invalid multicast port for receiving rates,value:-1",iae.getMessage());
		}
		serverProvision.setRateMulticastPort(1024);
		
		try{
			instance.validateRuntime(serverProvision);
			Assert.fail("should throw exception");
		}catch(IllegalArgumentException iae){
			Assert.assertEquals("Invalid multicast port for receiving live mds rates,value:0",iae.getMessage());
		}
		serverProvision.setLiveRateMulticastPort(4056);
		serverProvision.setCreditMulticastPort(-78);
		
		try{
			instance.validateRuntime(serverProvision);
			Assert.fail("should throw exception");
		}catch(IllegalArgumentException iae){
			Assert.assertEquals("Invalid multicast port for receiving credit updates ,value:-78",iae.getMessage());
		}
		
		serverProvision.setCreditMulticastPort(5068);
		instance.validateRuntime(serverProvision);
	}

	
}
