package com.integral.mdf;

import java.util.Properties;

import org.junit.Assert;
import org.junit.Test;

import com.integral.commons.pool.MPMCObjectPool;
import com.integral.commons.pool.ObjectPool;
import com.integral.mdf.data.QuoteC;
import com.integral.mdf.data.QuotePoolFactory;
import com.integral.mdf.data.ServerProvision;
import com.integral.mdf.proivision.MDFProvisionDataService;
import com.integral.mdf.proivision.builder.RemoteProvisionBuilder;

public class DefaultProvisioningTest extends RDSBaseTest {

	protected static final String TEST_CONFIG_PROPS = "Test-MDF.properties";
	
	protected RemoteProvisionBuilder builder;
	
	protected MDFProvisionDataService service = MDFProvisionDataService.getInstance();
	
	protected final ObjectPool<QuoteC> rawRatePool = new MPMCObjectPool<QuoteC>(new QuotePoolFactory(), 100, 100, true);
	
	protected ServerProvision doDefaultProvision() throws Exception {
		buildMDFEntityDataInRDS();

		buildServerRunTimeData();

		buildOrgProvisionData();

		buildCcyProvisionData();

		buildCcyPairProvisionData();

		buildLPProvisionData();
		
		buildOrgIndexMappingData();

		buildVenueConfigurationData();

		buildMulticastAddressData();

		ServerProvision serverProvision = getServerProvision();
		return serverProvision;
	}

	protected ServerProvision getServerProvision() throws Exception {
		Properties properties = new ClientInitializer(TEST_CONFIG_PROPS)
				.initRDSClient(null, null, null);

		builder = new RemoteProvisionBuilder(properties);

		service.init(builder);

		ServerProvision serverProvision = service.getServerProvision();

		Assert.assertNotNull(serverProvision);

		return serverProvision;
	}
	
	
	@Test
	public void testDefaultProvision() throws Exception{
		doDefaultProvision();
		
	}

	@Override
	protected String getConfigFileName() {
		return TEST_CONFIG_PROPS;
	}
}
