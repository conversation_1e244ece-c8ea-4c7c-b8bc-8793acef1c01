package com.integral.mdf.cache.subscription;

import com.integral.mdf.cache.CacheClientC;
import com.integral.mdf.data.ServerProvision;
import com.integral.mdf.rate.PriceAggregationTest;
import com.integral.notifications.mdf.SubscriptionRequest;
import com.integral.notifications.mdf.SubscriptionType;
import com.integral.provision.MDFAggregationType;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

public class SubscriptionCacheManagerTest extends PriceAggregationTest {

    @Before
    public void init() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();
        /*
         * Initialize distributed cache
         */
        if (serverProvision.getDistributedCacheHeartbeatAddress() != null) {
            CacheClientC.init(serverProvision.getDistributedCacheHeartbeatAddress());
            SubscriptionCacheManagerC.init(CacheClientC.getInstance().getCacheInstance());
        }
    }

//unique ids

    @Test
    public void subscriptionAddTest() throws Exception {

        SubscriptionCacheManager scm = SubscriptionCacheManagerC.getInstance();
        SubscriptionRequest sr = new SubscriptionRequest();
        sr.setId("1");
        sr.setType(SubscriptionType.SUBSCRIBE);
        sr.setAggregationType(MDFAggregationType.FULL_BOOK);
        sr.setOrgIndex(123);
        sr.setCurrencyPairIndex(456);
        sr.setTiers(new long[]{1,2});

        scm.addSubscription("1", sr, "uig1");
        scm.addSubscription("1", sr, "uig2");

        List<SubscriptionCache> l = scm.getSubscriptions("1");
        Assert.assertEquals(2, l.size());

        l.clear();
    }

    @Test
    public void subscriptionRemoveTest() throws Exception {

        SubscriptionCacheManager scm = SubscriptionCacheManagerC.getInstance();
        SubscriptionRequest subscription = new SubscriptionRequest();
        subscription.setId("21");
        subscription.setType(SubscriptionType.SUBSCRIBE);
        subscription.setAggregationType(MDFAggregationType.FULL_BOOK);
        subscription.setOrgIndex(123);
        subscription.setCurrencyPairIndex(456);
        subscription.setTiers(new long[]{1,2});

        scm.addSubscription("21", subscription, "uig1");
        scm.addSubscription("21", subscription, "uig2");

        SubscriptionRequest subscriptionRemove = new SubscriptionRequest();
        subscriptionRemove.setId("21");
        subscriptionRemove.setType(SubscriptionType.SUBSCRIBE);
        subscriptionRemove.setAggregationType(MDFAggregationType.FULL_BOOK);
        subscriptionRemove.setOrgIndex(123);
        subscriptionRemove.setCurrencyPairIndex(456);
        subscriptionRemove.setTiers(new long[]{1});

        scm.removeSubscription("21", subscriptionRemove, "uig1");

        List<SubscriptionCache> l = scm.getSubscriptions("21");
        Assert.assertEquals(2, l.size());

        scm.removeSubscription("21", subscriptionRemove, "uig2");

        l = scm.getSubscriptions("21");
        Assert.assertEquals(1, l.size());

        l.clear();
    }

    //Different agg methods,2 subscriptions, different tiers,
    @Test
    public void TwosubscriptionRemoveTest() throws Exception {

        SubscriptionCacheManager scm = SubscriptionCacheManagerC.getInstance();
        SubscriptionRequest subscription = new SubscriptionRequest();
        subscription.setId("31");
        subscription.setType(SubscriptionType.SUBSCRIBE);
        subscription.setAggregationType(MDFAggregationType.FULL_BOOK);
        subscription.setOrgIndex(123);
        subscription.setCurrencyPairIndex(456);
        subscription.setTiers(new long[]{1,2});

        SubscriptionRequest subscription2 = new SubscriptionRequest();
        subscription2.setId("32");
        subscription2.setType(SubscriptionType.SUBSCRIBE);
        subscription2.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        subscription2.setOrgIndex(234);
        subscription2.setCurrencyPairIndex(456);
        subscription2.setTiers(new long[]{1,2,3});

        scm.addSubscription("31", subscription, "uig1");
        scm.addSubscription("32", subscription2, "uig2");

        List<SubscriptionCache> l = scm.getSubscriptions("31");
        System.out.println("\n Subscription manager contains this data for key 1,..."+l.toString());
        Assert.assertEquals(2, l.size());  //2 tiers with key 1
        l = scm.getSubscriptions("32");
        Assert.assertEquals(3, l.size());   //3 tiers with key 2

        scm.removeSubscription("31", subscription, "uig1");
        l = scm.getSubscriptions("31");
        Assert.assertEquals(0, l.size());
        l = scm.getSubscriptions("32");
        Assert.assertEquals(3, l.size());  // still has 3 tiers

        scm.removeSubscription("32", subscription2, "uig2");
        System.out.println("\n Subscription manager contains this data for key 2,..."+l.toString());
        l = scm.getSubscriptions("31");
        Assert.assertEquals(0, l.size());
        System.out.println("\n Subscription manager contains this data for key 1 after removing,..."+l.toString());
        l = scm.getSubscriptions("32");
        Assert.assertEquals(0, l.size());
        System.out.println("\n Subscription manager contains this data for key 2 after removing,..."+l.toString());

        l.clear();
    }

    //VWAP and BPA together
    @Test
    public void TwosubscriptionVWAPBPARemoveTest() throws Exception {

        SubscriptionCacheManager scm = SubscriptionCacheManagerC.getInstance();
        SubscriptionRequest subscription = new SubscriptionRequest();
        subscription.setId("41");
        subscription.setType(SubscriptionType.SUBSCRIBE);
        subscription.setAggregationType(MDFAggregationType.BEST_PRICE);
        subscription.setOrgIndex(123);
        subscription.setCurrencyPairIndex(456);
        subscription.setTiers(new long[]{1});

        SubscriptionRequest subscription2 = new SubscriptionRequest();
        subscription2.setId("42");
        subscription2.setType(SubscriptionType.SUBSCRIBE);
        subscription2.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        subscription2.setOrgIndex(234);
        subscription2.setCurrencyPairIndex(456);
        subscription2.setTiers(new long[]{1,2,3});

        scm.addSubscription("41", subscription, "uig1");
        scm.addSubscription("42", subscription2, "uig2");

        List<SubscriptionCache> l = scm.getSubscriptions("41");
        System.out.println("\n Subscription manager contains this data for key 1,..."+l.toString());
        Assert.assertEquals(1, l.size());  //2 tiers with key 1
        l = scm.getSubscriptions("42");
        Assert.assertEquals(3, l.size());   //3 tiers with key 2

        scm.removeSubscription("42", subscription, "uig1");
        l = scm.getSubscriptions("41");
        Assert.assertEquals(1, l.size());
        l = scm.getSubscriptions("42");
        Assert.assertEquals(3, l.size());  // still has 3 tiers, since subscription sentby for uig1 is null

        scm.removeSubscription("42", subscription, "uig2"); //  //remove again, used different subscription req id
        l = scm.getSubscriptions("42");
        Assert.assertEquals(3, l.size());

        scm.removeSubscription("41", subscription, "uig1");
        l = scm.getSubscriptions("41");
        Assert.assertEquals(0, l.size());

        scm.removeSubscription("42", subscription2, "uig2");
        System.out.println("\n Subscription manager contains this data for key 2,..."+l.toString());
        l = scm.getSubscriptions("41");
        Assert.assertEquals(0, l.size());
        System.out.println("\n Subscription manager contains this data for key 1 after removing,..."+l.toString());
        l = scm.getSubscriptions("42");
        Assert.assertEquals(0, l.size());
        System.out.println("\n Subscription manager contains this data for key 2 after removing,..."+l.toString());
    }

    //Same agg method, 2 subscriptions, different tiers
    @Test
    public void TwosubscriptionMTFOKRemoveTest() throws Exception {

        SubscriptionCacheManager scm = SubscriptionCacheManagerC.getInstance();
        SubscriptionRequest subscription = new SubscriptionRequest();
        subscription.setId("51");
        subscription.setType(SubscriptionType.SUBSCRIBE);
        subscription.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);
        subscription.setOrgIndex(123);
        subscription.setCurrencyPairIndex(456);
        subscription.setTiers(new long[]{1});

        SubscriptionRequest subscription2 = new SubscriptionRequest();
        subscription2.setId("52");
        subscription2.setType(SubscriptionType.SUBSCRIBE);
        subscription2.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);
        subscription2.setOrgIndex(234);
        subscription2.setCurrencyPairIndex(456);
        subscription2.setTiers(new long[]{1,2,3});

        scm.addSubscription("51", subscription, "uig1");
        scm.addSubscription("52", subscription2, "uig2");

        List<SubscriptionCache> l = scm.getSubscriptions("51");
        System.out.println("\n Subscription manager contains this data for key 1,..."+l.toString());
        Assert.assertEquals(1, l.size());  //1 tier with key 1
        l = scm.getSubscriptions("52");
        Assert.assertEquals(3, l.size());   //3 tiers with key 2

        scm.removeSubscription("51", subscription, "uig1");
        l = scm.getSubscriptions("51");
        Assert.assertEquals(0, l.size());
        l = scm.getSubscriptions("52");
        Assert.assertEquals(3, l.size());  // still has 3 tiers

        scm.removeSubscription("52", subscription2, "uig2");
        System.out.println("\n Subscription manager contains this data for key 2,..."+l.toString());
        l = scm.getSubscriptions("51");
        Assert.assertEquals(0, l.size());
        System.out.println("\n Subscription manager contains this data for key 1 after removing,..."+l.toString());
        l = scm.getSubscriptions("52");
        Assert.assertEquals(0, l.size());
        System.out.println("\n Subscription manager contains this data for key 2 after removing,..."+l.toString());
    }

    //Same agg method, 2 subscriptions, same tiers BPA
    //BPA shud set only 1 tier - API supports multi-tier in one request, Internally, each of the tiers are stored separately
    @Test
    public void TwosubscriptionBPARemoveTest() throws Exception {

        SubscriptionCacheManager scm = SubscriptionCacheManagerC.getInstance();
        SubscriptionRequest subscription = new SubscriptionRequest();
        subscription.setId("61");
        subscription.setType(SubscriptionType.SUBSCRIBE);
        subscription.setAggregationType(MDFAggregationType.BEST_PRICE);
        subscription.setOrgIndex(123);
        subscription.setCurrencyPairIndex(456);
        subscription.setTiers(new long[]{1,2,3,4,5,6});

        SubscriptionRequest subscription2 = new SubscriptionRequest();
        subscription2.setId("62");
        subscription2.setType(SubscriptionType.SUBSCRIBE);
        subscription2.setAggregationType(MDFAggregationType.BEST_PRICE);
        subscription2.setOrgIndex(234);
        subscription2.setCurrencyPairIndex(456);
        subscription2.setTiers(new long[]{1,2,3,4,5,6});

        scm.addSubscription("61", subscription, "uig1");
        scm.addSubscription("62", subscription2, "uig2");

        List<SubscriptionCache> l = scm.getSubscriptions("61");
        System.out.println("\n Subscription manager contains this data for key 1,..."+l.toString());
        Assert.assertEquals(6, l.size());
        l = scm.getSubscriptions("62");
        Assert.assertEquals(6, l.size());

        scm.removeSubscription("61", subscription, "uig1");
        l = scm.getSubscriptions("61");
        Assert.assertEquals(0, l.size());
        l = scm.getSubscriptions("62");
        Assert.assertEquals(6, l.size());

        scm.removeSubscription("62", subscription2, "uig2");
        System.out.println("\n Subscription manager contains this data for key 2,..."+l.toString());
        l = scm.getSubscriptions("61");
        Assert.assertEquals(0, l.size());
        System.out.println("\n Subscription manager contains this data for key 1 after removing,..."+l.toString());
        l = scm.getSubscriptions("62");
        Assert.assertEquals(0, l.size());
        System.out.println("\n Subscription manager contains this data for key 2 after removing,..."+l.toString());

        l.clear();
            }

    @Test
    public void subscriptionAdd0tierTest() throws Exception {

        SubscriptionCacheManager scm = SubscriptionCacheManagerC.getInstance();
        SubscriptionRequest sr = new SubscriptionRequest();
        sr.setId("71");
        sr.setType(SubscriptionType.SUBSCRIBE);
        sr.setAggregationType(MDFAggregationType.FULL_BOOK);
        sr.setOrgIndex(123);
        sr.setCurrencyPairIndex(456);
        sr.setTiers(new long[]{});

        scm.addSubscription("71", sr, "uig1");
        scm.addSubscription("71", sr, "uig2");

        List<SubscriptionCache> l = scm.getSubscriptions("71");
        Assert.assertEquals(0, l.size());
        System.out.println("\n Subscription manager contains this data for key 1 with 0 tiers,..."+l.toString());

    }

    //Single add command, adds both tiers - API supports multi-tier in one request,
    //Internally, each of the tiers are stored separately
    @Test
    public void NosubscriptionAddtierTest() throws Exception {

        SubscriptionCacheManager scm = SubscriptionCacheManagerC.getInstance();
        SubscriptionRequest sr = new SubscriptionRequest();
        sr.setId("81");
        sr.setType(SubscriptionType.SUBSCRIBE);
        sr.setAggregationType(MDFAggregationType.FULL_BOOK);
        sr.setOrgIndex(123);
        sr.setCurrencyPairIndex(456);
        sr.setTiers(new long[]{1,2});

        List<SubscriptionCache> l = scm.getSubscriptions("81");
        Assert.assertEquals(0, l.size());

        scm.addSubscription("81", sr, "uig1");
        scm.addSubscription("81", sr, "uig2");
        l = scm.getSubscriptions("81");
        Assert.assertEquals(2, l.size());
        System.out.println("\n Subscription manager contains this data for key 1 with 0 tiers,..."+l.toString());

        l.clear();
    }

    //subscribe, unsubscribe, then Resubscribe to a different agg method
    @Test
    public void ResubscriptionRemoveTest() throws Exception {

        SubscriptionCacheManager scm = SubscriptionCacheManagerC.getInstance();
        SubscriptionRequest subscription = new SubscriptionRequest();
        subscription.setId("91");
        subscription.setType(SubscriptionType.SUBSCRIBE);
        subscription.setAggregationType(MDFAggregationType.FULL_BOOK);
        subscription.setOrgIndex(123);
        subscription.setCurrencyPairIndex(456);
        subscription.setTiers(new long[]{1,2});

        scm.addSubscription("91", subscription, "uig1");
        scm.addSubscription("91", subscription, "uig2");

        SubscriptionRequest subscriptionRemove = new SubscriptionRequest();
        subscriptionRemove.setId("92");
        subscriptionRemove.setType(SubscriptionType.SUBSCRIBE);
        subscriptionRemove.setAggregationType(MDFAggregationType.FULL_BOOK);
        subscriptionRemove.setOrgIndex(123);
        subscriptionRemove.setCurrencyPairIndex(456);
        subscriptionRemove.setTiers(new long[]{1});

        scm.removeSubscription("92", subscriptionRemove, "uig1");
        List<SubscriptionCache> l = scm.getSubscriptions("92");
        Assert.assertEquals(0, l.size());

        scm.removeSubscription("92", subscriptionRemove, "uig2");
        l = scm.getSubscriptions("92");
        Assert.assertEquals(0, l.size());

        scm.removeSubscription("91", subscription, "uig1");
        scm.removeSubscription("91", subscription, "uig2");
        l = scm.getSubscriptions("91");
        Assert.assertEquals(0, l.size());

        //Re-subscribe
        subscription.setId("91");
        subscription.setType(SubscriptionType.SUBSCRIBE);
        subscription.setAggregationType(MDFAggregationType.RAW_BOOK);
        subscription.setOrgIndex(123);
        subscription.setCurrencyPairIndex(456);
        subscription.setTiers(new long[]{1,2,3,4});

        scm.addSubscription("91", subscription, "uig2");
        l = scm.getSubscriptions("91");
        Assert.assertEquals(4, l.size());
    }

    @Test
    public void subscriptionAddTestSentby() throws Exception {

        SubscriptionCacheManager scm = SubscriptionCacheManagerC.getInstance();
        SubscriptionRequest sr = new SubscriptionRequest();
        sr.setId("101");
        sr.setType(SubscriptionType.SUBSCRIBE);
        sr.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        sr.setOrgIndex(123);
        sr.setCurrencyPairIndex(456);
        sr.setTiers(new long[]{1,2});

        scm.addSubscription("101", sr, "uig1");

        List<SubscriptionCache> l = scm.getSubscriptions("101");
        Assert.assertEquals(2, l.size());
        System.out.println("\n Subscription manager contains this data for key 1,..."+l.toString());
        Assert.assertEquals("[uig1]", l.get(0).getAllSentBy().toString());

        scm.addSubscription("101", sr, "uig2");
        l = scm.getSubscriptions("101");
        System.out.println("\n Subscription manager contains this scm for key 1,..."+l.get(0).getAllSentBy());
        Assert.assertEquals("[uig1, uig2]", l.get(0).getAllSentBy().toString());

        l.clear();
    }

    //subscribe, unsubscribe, then Resubscribe to a different agg method
    @Test
    public void ResubscriptionRemoveTestTierMax() throws Exception {

        SubscriptionCacheManager scm = SubscriptionCacheManagerC.getInstance();
        SubscriptionRequest subscription = new SubscriptionRequest();
        subscription.setId("121");
        subscription.setType(SubscriptionType.SUBSCRIBE);
        subscription.setAggregationType(MDFAggregationType.FULL_BOOK);
        subscription.setOrgIndex(123);
        subscription.setCurrencyPairIndex(456);
        subscription.setTiers(new long[]{1,2,3,4});

        scm.addSubscription("121", subscription, "uig1");
        scm.addSubscription("121", subscription, "uig2");

        SubscriptionRequest subscriptionRemove = new SubscriptionRequest();
        subscriptionRemove.setId("122");
        subscriptionRemove.setType(SubscriptionType.SUBSCRIBE);
        subscriptionRemove.setAggregationType(MDFAggregationType.FULL_BOOK);
        subscriptionRemove.setOrgIndex(123);
        subscriptionRemove.setCurrencyPairIndex(456);
        subscriptionRemove.setTiers(new long[]{1});

        scm.removeSubscription("122", subscriptionRemove, "uig1");
        List<SubscriptionCache> l = scm.getSubscriptions("122");
        Assert.assertEquals(0, l.size());

        scm.removeSubscription("122", subscriptionRemove, "uig2");
        l = scm.getSubscriptions("122");
        Assert.assertEquals(0, l.size());

        scm.removeSubscription("121", subscription, "uig1");
        scm.removeSubscription("121", subscription, "uig2");
        l = scm.getSubscriptions("121");
        Assert.assertEquals(0, l.size());

        //Re-subscribe
        subscription.setId("121");
        subscription.setType(SubscriptionType.SUBSCRIBE);
        subscription.setAggregationType(MDFAggregationType.RAW_BOOK);
        subscription.setOrgIndex(123);
        subscription.setCurrencyPairIndex(456);
        subscription.setTiers(new long[]{1,2});

        scm.addSubscription("121", subscription, "uig2");
        l = scm.getSubscriptions("121");
        Assert.assertEquals(2, l.size());
    }

    //Testing tier-size in vwap
    @Test
    public void subscriptionAddTestTierValues() throws Exception {

        SubscriptionCacheManager scm = SubscriptionCacheManagerC.getInstance();
        SubscriptionRequest sr = new SubscriptionRequest();
        sr.setId("111");
        sr.setType(SubscriptionType.SUBSCRIBE);
        sr.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        sr.setOrgIndex(123);
        sr.setCurrencyPairIndex(456);
        sr.setTiers(new long[]{10001,20002});

        scm.addSubscription("111", sr, "uig1");
        scm.addSubscription("111", sr, "uig2");

        List<SubscriptionCache> l = scm.getSubscriptions("111");
        System.out.println("\n Subscription manager contains this data for key 1,..."+l.toString());
        String actual = l.get(0).toString().substring(l.get(0).toString().indexOf("tiers"), l.get(0).toString().indexOf("],")+1);
        System.out.println("\n Subscription manager contains this data for key 1 tier 1,..."+actual);
        Assert.assertEquals(2, l.size());
        Assert.assertEquals(actual, "tiers=[10001]");

        actual = l.get(1).toString().substring(l.get(1).toString().indexOf("tiers"), l.get(0).toString().indexOf("],")+1);
        System.out.println("\n Subscription manager contains this data for key 1 tier 2,..."+actual);
        Assert.assertEquals(actual, "tiers=[20002]");

        scm.addSubscription("111", sr, "uig1");
        scm.addSubscription("111", sr, "uig2");
        l = scm.getSubscriptions("111");
        System.out.println("\n Subscription manager contains this data after adding both,..."+l.toString());

        l.clear();
    }



}
