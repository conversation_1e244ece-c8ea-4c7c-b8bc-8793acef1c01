package com.integral.mdf.subscriptions;

import com.google.common.collect.Sets;
import com.integral.mdf.MessageGateway;
import com.integral.mdf.Util;
import com.integral.mdf.data.*;
import com.integral.mdf.rate.PriceAggregationTest;
import com.integral.mdf.rate.RateBook;
import com.integral.mdf.rate.RateDistributionManager;
import com.integral.notifications.Status;
import com.integral.notifications.mdf.SubscriptionRequest;
import com.integral.notifications.mdf.SubscriptionResponse;
import com.integral.notifications.mdf.SubscriptionType;
import com.integral.provision.CurrencyPairProvision;
import com.integral.provision.MDFAggregationType;
import com.integral.virtualserver.MDFEntity;
import org.jctools.maps.NonBlockingHashMapLong;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.TestRule;
import org.junit.rules.TestWatcher;
import org.junit.runner.Description;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.integral.mdf.Util.getBaseCurrencyIndex;
import static com.integral.mdf.Util.getVarCurrencyIndex;
import static org.junit.Assert.*;

public class TestSubscriptionManager extends PriceAggregationTest {

    public static final String LP22 = "lp2";
    public static final String LP12 = "lp1";
    public static final String EUR_USD = "EUR/USD";

    @Rule
    public TestRule watcher = new TestWatcher() {
        protected void starting(Description description) {
            System.out.println("Starting test: " + description.getMethodName());
        }
        protected void finished(Description description) {
            System.out.println("Finished test: " + description.getMethodName());
        }
    };

    /**Single Org, Single user, subscribe and unsubscribe for user1, Best Price agg
     * */
    @Test
    public void testSubscribeUnsubscribeBase() throws Exception {

        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        //service.provision(TESTFI1);
        //FIProvision fi1 = serverProvision.getFIProvision(1001);
        Optional<Integer> EUR = serverProvision.getCcyIndex("EUR");
        Optional<Integer> USD = serverProvision.getCcyIndex("USD");
        int eurusdIdx = Util.getCurrencyPairIndex(EUR.get(), USD.get());

        RateDistributionManager rdm = getNoHopManager(serverProvision);
        MessageGateway gateway = new NoOpMessageGateway();

        SubscriptionManager sm = new SubscriptionManager(serverProvision, rdm, service);
        sm.setGateway(gateway);

        SubscriptionRequest request = new SubscriptionRequest();
        request.setType(SubscriptionType.SUBSCRIBE);
        request.setId("10001");
        request.setAggregationType(MDFAggregationType.BEST_PRICE);
        request.setOrgIndex(1001);// fi1's index
        request.setCurrencyPairIndex(eurusdIdx);

        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        SubscriptionResponse response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());
        Set<String> servers = tiers.values().iterator().next();
        assertTrue(servers.contains("uig1"));
        System.out.println("Tiers before unsubscribe ,.."+tiers);

        //convert request into unsubscribe request
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());
        System.out.println("Tiers after unsubscribe ,.."+tiers);

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        assertFalse(servers.contains("uig1"));
        assertNoSubscribers(sm);

    }

    /**Single Org, multiple subscriptions for uig1, uig2, uig3, BEST PRICE
     * Tiers only for VWAP and MTFOK
     * 0 is considered as default tier value for all other agg types
     * */
    @Test
    public void testMultipleSubscribersBPA() throws Exception {

        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        Optional<Integer> EUR = serverProvision.getCcyIndex("EUR");
        Optional<Integer> USD = serverProvision.getCcyIndex("USD");
        int eurusdIdx = Util.getCurrencyPairIndex(EUR.get(), USD.get());

        RateDistributionManager rdm = getNoHopManager(serverProvision);
        MessageGateway gateway = new NoOpMessageGateway();

        SubscriptionManager sm = new SubscriptionManager(serverProvision, rdm, service);
        sm.setGateway(gateway);

        SubscriptionRequest request = new SubscriptionRequest();
        request.setType(SubscriptionType.SUBSCRIBE);
        request.setId("10001");
        request.setAggregationType(MDFAggregationType.BEST_PRICE);
        request.setOrgIndex(1001);// fi1's index
        request.setCurrencyPairIndex(eurusdIdx);

        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        SubscriptionResponse response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        request.setId("20001");
        sm.handleSubscriptionRequest(request, "uig2", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        request.setId("30001");
        sm.handleSubscriptionRequest(request, "uig3", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());
        assertEquals(1,tiers.size()); //only BPA has no tiers
        Set<String> servers = tiers.values().iterator().next();
        assertTrue(servers.contains("uig1"));
        assertTrue(servers.contains("uig2"));
        assertTrue(servers.contains("uig3"));
        System.out.println("Tiers before unsubscribe ,.."+tiers);

        //convert request into unsubscribe request
        request.setId("10001");
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());

        tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());
        servers = tiers.values().iterator().next();
        System.out.println("Tiers after unsubscribe ,.."+tiers);

        assertFalse(servers.contains("uig1"));
        assertTrue(servers.contains("uig2"));
        assertTrue(servers.contains("uig3"));
     }

    /**        WEIGHTED_AVERAGE same org, differnt tier values
     **/
    @Test
    public void testMultipleSubscribersVWAP() throws Exception
    {
        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        Optional<Integer> EUR = serverProvision.getCcyIndex("EUR");
        Optional<Integer> USD = serverProvision.getCcyIndex("USD");
        int eurusdIdx = Util.getCurrencyPairIndex(EUR.get(), USD.get());

        RateDistributionManager rdm = getNoHopManager(serverProvision);
        MessageGateway gateway = new NoOpMessageGateway();

        SubscriptionManager sm = new SubscriptionManager(serverProvision, rdm, service);
        sm.setGateway(gateway);

        long[] defTiers = {2000};
        SubscriptionRequest request = new SubscriptionRequest();
        request.setType(SubscriptionType.SUBSCRIBE);
        request.setId("10001");
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        request.setOrgIndex(1001);// fi1's index
        request.setCurrencyPairIndex(eurusdIdx);
        request.setTiers(defTiers);

        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        SubscriptionResponse response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        request.setId("20001");
        long[] defTiers2 = {3000};
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        request.setTiers(defTiers2);
        sm.handleSubscriptionRequest(request, "uig2", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        request.setId("30001");
        long[] defTiers3 = {1000};
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        request.setTiers(defTiers3);
        sm.handleSubscriptionRequest(request, "uig3", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());
        assertEquals(3,tiers.size()); //only BPA has no tiers

        System.out.println("tiers before unsubscribe->"+tiers);
        System.out.println("tiers.get(1000l) ->"+tiers.get(1000l));
        System.out.println("tiers.get(2000l) ->"+tiers.get(2000l));
        System.out.println("tiers.get(3000l) ->"+tiers.get(3000l));

        assertTrue(Sets.difference(tiers.get(1000l), Sets.newHashSet("uig3")).isEmpty());
        assertTrue(Sets.difference(tiers.get(2000l), Sets.newHashSet("uig1")).isEmpty());
        assertTrue(Sets.difference(tiers.get(3000l), Sets.newHashSet("uig2")).isEmpty());

        //convert request into unsubscribe request
        request.setId("10001");
        request.setTiers(defTiers);
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());

        tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());
        System.out.println("tiers after unsubscribe->"+tiers);
        System.out.println("tiers.get(3000l) "+tiers.get(3000l));
        System.out.println("tiers.get(2000l) "+tiers.get(2000l));
        System.out.println("tiers.get(1000l) "+tiers.get(1000l));

        assertTrue(Sets.difference(tiers.get(1000l), Sets.newHashSet("uig3")).isEmpty());
        assertTrue(Sets.difference(tiers.get(3000l), Sets.newHashSet("uig2")).isEmpty());
        assertNull(tiers.get(2000l));

        /**Sample results--
         * tiers before unsubscribe->{2000=[uig1], 3000=[uig2], 1000=[uig3]}
         * tiers.get(1000l) ->[uig3]
         * tiers.get(2000l) ->[uig1]
         * tiers.get(3000l) ->[uig2]
         * tiers after unsubscribe->{3000=[uig2], 1000=[uig3]}
         */
    }

    /** PLT 3784
     * Same org, same tier values
     * TO DO - add ratebook and quotes
     * **/
    @Test
    public void testMultipleSubscribersVWAP_sametiers() throws Exception
    {
        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        Optional<Integer> EUR = serverProvision.getCcyIndex("EUR");
        Optional<Integer> USD = serverProvision.getCcyIndex("USD");
        int eurusdIdx = Util.getCurrencyPairIndex(EUR.get(), USD.get());

        RateDistributionManager rdm = getNoHopManager(serverProvision);
        MessageGateway gateway = new NoOpMessageGateway();

        SubscriptionManager sm = new SubscriptionManager(serverProvision, rdm, service);
        sm.setGateway(gateway);

        long[] defTiers = {1000};
        SubscriptionRequest request = new SubscriptionRequest();
        request.setType(SubscriptionType.SUBSCRIBE);
        request.setId("10001");
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        request.setOrgIndex(1001);// fi1's index
        request.setCurrencyPairIndex(eurusdIdx);
        request.setTiers(defTiers);

        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        SubscriptionResponse response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers1 = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());
        assertEquals(1,tiers1.size()); //only BPA has no tiers
        System.out.println("tiers 2->"+tiers1);
        assertTrue(Sets.difference(tiers1.get(1000l), Sets.newHashSet("uig1")).isEmpty());
        assertNull(tiers1.get(2000l));
        assertNull(tiers1.get(3000l));

        request.setId("20001");
        long[] defTiers2 = {1000};
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        request.setTiers(defTiers2);
        sm.handleSubscriptionRequest(request, "uig2", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers2 = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());
        assertEquals(1,tiers2.size()); //only BPA has no tiers

        System.out.println("tiers 2->"+tiers2);
        assertTrue(Sets.difference(tiers2.get(1000l), Sets.newHashSet("uig2","uig1")).isEmpty());
        assertNull(tiers2.get(2000l));
        assertNull(tiers2.get(3000l));

        request.setId("30001");
        long[] defTiers3 = {1000};
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        request.setTiers(defTiers3);
        sm.handleSubscriptionRequest(request, "uig3", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());
        assertEquals(1,tiers.size()); //only BPA has no tiers

        System.out.println("tiers 3 ->"+tiers);
        System.out.println("tiers.get(1000l) ->"+tiers.get(1000l));
        System.out.println("tiers.get(2000l) ->"+tiers.get(2000l));
        System.out.println("tiers.get(3000l) ->"+tiers.get(3000l));

        assertTrue(Sets.difference(tiers.get(1000l), Sets.newHashSet("uig3","uig2","uig1")).isEmpty());
        assertNull(tiers.get(2000l));
        assertNull(tiers.get(3000l));

        //Rate book not present,, add rates and check
        int vIdx = getVarCurrencyIndex(eurusdIdx);
        int bIdx = getBaseCurrencyIndex(eurusdIdx);
        Optional<RateBook> rateBook = rdm.getRateBook(10001,bIdx,vIdx,request.getAggregationType().getIndex());
        System.out.println("Rate book is,, " +rateBook.isPresent());
        System.out.println("Rate book is,, " +rateBook);

        //convert request into unsubscribe request
        request.setId("10001");
        request.setTiers(defTiers);
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());

        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers4 = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());
        assertEquals(1,tiers4.size()); //only BPA has no tiers

        System.out.println("tiers 4->"+tiers4);
        assertTrue(Sets.difference(tiers4.get(1000l), Sets.newHashSet("uig3","uig2")).isEmpty());
        assertNull(tiers4.get(2000l));
        assertNull(tiers4.get(3000l));



    }


    /**
     * PLT-3873
     * WEIGHTED_AVERAGE
     *         uig1 = 1000, 2000
     *         uig2 =  2000, 3000
     *         uig3 = 1000, 3000
     */
    @Test
    public void testMultipleSubscribersVWAP_DifferentTiers_SameOrganization() throws Exception
    {
        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        //service.provision(TESTFI1);
        //FIProvision fi1 = serverProvision.getFIProvision(1001);
        Optional<Integer> EUR = serverProvision.getCcyIndex("EUR");
        Optional<Integer> USD = serverProvision.getCcyIndex("USD");
        int eurusdIdx = Util.getCurrencyPairIndex(EUR.get(), USD.get());

        RateDistributionManager rdm = getNoHopManager(serverProvision);
        MessageGateway gateway = new NoOpMessageGateway();

        SubscriptionManager sm = new SubscriptionManager(serverProvision, rdm, service);
        sm.setGateway(gateway);

        SubscriptionRequest request = new SubscriptionRequest();
        request.setType(SubscriptionType.SUBSCRIBE);
        request.setId("10001");
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        request.setOrgIndex(1001);// fi1's index
        request.setCurrencyPairIndex(eurusdIdx);
        long[] uig1Tiers = new long[]{1000,2000};
        request.setTiers(uig1Tiers);

        //UIG1 request
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        SubscriptionResponse response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        //UIG2 request
        request.setId("20001");
        long[] uig2Tiers = new long[]{2000,3000};
        request.setTiers(uig2Tiers);
        sm.handleSubscriptionRequest(request, "uig2", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        //UIG3 request
        request.setId("30001");
        long[] uig3Tiers = new long[]{1000,3000};
        request.setTiers(uig3Tiers);
        sm.handleSubscriptionRequest(request, "uig3", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());
        assertEquals(3,tiers.size()); //only BPA has no tiers
        System.out.println("tiers->"+tiers);

        assertTrue(Sets.difference(tiers.get(1000l), Sets.newHashSet("uig1","uig3")).isEmpty());
        assertTrue(Sets.difference(tiers.get(2000l), Sets.newHashSet("uig1","uig2")).isEmpty());
        assertTrue(Sets.difference(tiers.get(3000l), Sets.newHashSet("uig2","uig3")).isEmpty());

        //convert request into unsubscribe request
        request.setId("10001");
        request.setTiers(uig1Tiers);
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());

        //validate tiers
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());
        System.out.println("tiers -> " + tiers );

        //validate remaining tiers and there subscribers
        assertTrue(Sets.difference(tiers.get(1000l), Sets.newHashSet("uig3")).isEmpty());
        assertTrue(Sets.difference(tiers.get(2000l), Sets.newHashSet("uig2")).isEmpty());
        assertTrue(Sets.difference(tiers.get(3000l), Sets.newHashSet("uig2","uig3")).isEmpty());
    }

    /**
     *  PLT-3874
     * MultiQuote-MultiQuote VWAP aggregation TC with subscription and unsubscription
     * @throws Exception
    */
    @Test
    public void testMultipleSubscribersVWAP_SameTiers_SameOrganization() throws Exception
    {
        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        //int EUR = Util.getBaseCurrencyIndex(commonCcyPairIdx);
        //int USD = Util.getVarCurrencyIndex(commonCcyPairIdx);
        Optional<Integer> EUR = serverProvision.getCcyIndex("EUR");
        Optional<Integer> USD = serverProvision.getCcyIndex("USD");
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000, 2000, 3000});
        impl.setIndex(1001);
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager rdm = getNoHopManager(serverProvision);
        rdm.createRateBooks(fiProvision);

        RateBook vwapbook = getBook(serverProvision, fiProvision, rdm);

        //Sunscription manager validations
        int eurusdIdx = Util.getCurrencyPairIndex(EUR.get(), USD.get());
        MessageGateway gateway = new NoOpMessageGateway();

        SubscriptionManager sm = new SubscriptionManager(serverProvision, rdm, service);
        sm.setGateway(gateway);

        SubscriptionRequest request = new SubscriptionRequest();
        request.setType(SubscriptionType.SUBSCRIBE);
        request.setId("10001");
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        request.setOrgIndex(1001);// fi1's index
        request.setCurrencyPairIndex(eurusdIdx);
        long[] uig1Tiers = new long[]{1000,2000,3000};
        request.setTiers(uig1Tiers);

        //UIG1 request
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        SubscriptionResponse response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        //UIG2 request
        request.setId("20001");
        long[] uig2Tiers = new long[]{1000,2000,3000};
        request.setTiers(uig2Tiers);
        sm.handleSubscriptionRequest(request, "uig2", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());

        assertEquals(3,tiers.size()); //only BPA has no tiers
        System.out.println("tiers->"+tiers);

        assertTrue(Sets.difference(tiers.get(1000l), Sets.newHashSet("uig1","uig2")).isEmpty());
        assertTrue(Sets.difference(tiers.get(2000l), Sets.newHashSet("uig1","uig2")).isEmpty());


        //simulate rate updates from LP! and LP@
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2120, 1.2119);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2123, 1.2124);

        rdm.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2119, 1.2118);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2125, 1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        rdm.handleRate(quote);
        System.out.println("=====LP2 quote===" + quote.toString());

        //Simulate aggregation
        PriceBook vwapaggregate = vwapbook.aggregate();
        System.out.println("=====#1 vwapaggregate quote before unsubscription  ===" + vwapaggregate.toString());
        assertEquals(1.2121, vwapaggregate.getBidPrices()[0], 0.00001);

        //convert request into unsubscribe request
        request.setId("10001");
        request.setTiers(uig1Tiers);
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());

        //validate tiers
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());
        System.out.println("tiers -> " + tiers );

        //validate remaining tiers and there subscribers
        assertTrue(Sets.difference(tiers.get(2000l), Sets.newHashSet("uig2")).isEmpty());
        assertTrue(Sets.difference(tiers.get(2000l), Sets.newHashSet("uig2")).isEmpty());

        ((FIProvisionImpl) fiProvision).setIndex(20001);
        //simulate rate updates from LP! and LP@
         quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2120, 1.2119);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2123, 1.2124);

        rdm.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2119, 1.2118);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2125, 1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        rdm.handleRate(quote);
        System.out.println("=====LP2 quote===" + quote.toString());

        //Simulate aggregation
        vwapaggregate = vwapbook.aggregate();
        System.out.println("=====#1 vwapaggregate quote after unsubscription from uig2  ===" + vwapaggregate.toString());
        assertEquals(1.2121, vwapaggregate.getBidPrices()[0], 0.00001);

        //unsubscrbe aga and check agg in null
        request.setId("10001");
        request.setTiers(uig1Tiers);
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());

        ((FIProvisionImpl) fiProvision).setIndex(10001);
        //simulate rate updates from LP! and LP@
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2120, 1.2119);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2123, 1.2124);

        rdm.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2119, 1.2118);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2125, 1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        rdm.handleRate(quote);
        System.out.println("=====LP2 quote===" + quote.toString());

        //Simulate aggregation
        vwapaggregate = vwapbook.aggregate();
        System.out.println("=====#1 vwapaggregate quote after unsubscription from both ===" + vwapaggregate.toString());
        assertEquals(1.2121, vwapaggregate.getBidPrices()[0], 0.001);

    }


    protected void set3TierRates(QuoteC quote, byte buyOrSell, double p1, double p2, double p3) {
        //tier 1
        int tieridx = quote.tierOffset(buyOrSell, 0);
        quote.setPrice(tieridx, p1);
        //tier 2
        tieridx = quote.tierOffset(buyOrSell, 1);
        quote.setPrice(tieridx, p2);
        //tier 3
        tieridx = quote.tierOffset(buyOrSell, 2);
        quote.setPrice(tieridx, p3);
    }


    /**        WEIGHTED_AVERAGE same org, differnt tier values and sizes-
     *         works fine, but check and add how to validate the tiers variable
     *         2000:[uig1, uig3]
     *         1000:[uig2, uig3]
     *         3000:[uig2]
     */
    @Test
    public void testMultipleSubscribersVWAP_differnt_tiervalues() throws Exception
    {
        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        Optional<Integer> EUR = serverProvision.getCcyIndex("EUR");
        Optional<Integer> USD = serverProvision.getCcyIndex("USD");
        int eurusdIdx = Util.getCurrencyPairIndex(EUR.get(), USD.get());

        RateDistributionManager rdm = getNoHopManager(serverProvision);
        MessageGateway gateway = new NoOpMessageGateway();

        SubscriptionManager sm = new SubscriptionManager(serverProvision, rdm, service);
        sm.setGateway(gateway);

        long[] defTiers = {2000};
        SubscriptionRequest request = new SubscriptionRequest();
        request.setType(SubscriptionType.SUBSCRIBE);
        request.setId("10001");
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        request.setOrgIndex(1001);// fi1's index
        request.setCurrencyPairIndex(eurusdIdx);
        request.setTiers(defTiers);

        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        SubscriptionResponse response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        request.setId("20001");
        long[] defTiers2 = {1000,3000};
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        request.setTiers(defTiers2);
        sm.handleSubscriptionRequest(request, "uig2", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        request.setId("30001");
        long[] defTiers3 = {1000,2000};
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        request.setTiers(defTiers3);
        sm.handleSubscriptionRequest(request, "uig3", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());
        //assertEquals(4,tiers.size()); //only BPA has no tiers

        System.out.println("tiers->"+tiers);
        System.out.println("tiers.get(1000l) ->"+tiers.get(10001));
        System.out.println("tiers.get(2000l) ->"+tiers.get(20001));
        System.out.println("tiers.get(3000l) ->"+tiers.get(30001));
        //Sample result -- tiers->{2000=[uig1, uig3], 1000=[uig2, uig3], 3000=[uig2]}

        assertTrue(Sets.difference(tiers.get(1000l), Sets.newHashSet("uig2","uig3")).isEmpty());
        assertTrue(Sets.difference(tiers.get(2000l), Sets.newHashSet("uig1","uig3")).isEmpty());
        assertTrue(Sets.difference(tiers.get(3000l), Sets.newHashSet("uig2")).isEmpty());

//        assertTrue(Sets.difference(tiers.get(2000l), Sets.newHashSet("uig1","uig3")).isEmpty());
//        assertTrue(Sets.difference(tiers.get(3000l), Sets.newHashSet("uig2")).isEmpty());
//        assertTrue(Sets.difference(tiers.get(40001), Sets.newHashSet("uig3")).isEmpty());

//        Set<Map.Entry<Long,Set<String>>> st = tiers.entrySet();  //returns Set view
//        for(Map.Entry<Long,Set<String>> me:st)
//        {
//            System.out.print(me.getKey()+":");
//            System.out.println(me.getValue());
//            //Set<String> expectedtiers1 = new HashSet<String>();
//            //expectedtiers1.add("uig1");
//            //expectedtiers1.add("uig2");
//            //me.getValue().containsAll(Arrays.asList(expectedtiers1));
//        }


        //convert request into unsubscribe request
        request.setId("20001");
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig3", "routing_key");

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());

        assertTrue(Sets.difference(tiers.get(1000l), Sets.newHashSet("uig2","uig3")).isEmpty());
        assertTrue(Sets.difference(tiers.get(2000l), Sets.newHashSet("uig1")).isEmpty());
        assertTrue(Sets.difference(tiers.get(3000l), Sets.newHashSet("uig2")).isEmpty());

    }



    /**
     * uig - BPA, FBA, RBA
     * unsubscribe from RBA, others should remain subscribed
     * @throws Exception
     */
    @Test
    public void testSameSubscriberMultipleAggregation() throws Exception {

        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        //service.provision(TESTFI1);
        //FIProvision fi1 = serverProvision.getFIProvision(1001);
        Optional<Integer> EUR = serverProvision.getCcyIndex("EUR");
        Optional<Integer> USD = serverProvision.getCcyIndex("USD");
        int eurusdIdx = Util.getCurrencyPairIndex(EUR.get(), USD.get());

        RateDistributionManager rdm = getNoHopManager(serverProvision);
        MessageGateway gateway = new NoOpMessageGateway();

        SubscriptionManager sm = new SubscriptionManager(serverProvision, rdm, service);
        sm.setGateway(gateway);

        SubscriptionRequest request = new SubscriptionRequest();
        request.setType(SubscriptionType.SUBSCRIBE);
        request.setId("10001");
        request.setAggregationType(MDFAggregationType.BEST_PRICE);
        request.setOrgIndex(1001);// fi1's index
        request.setCurrencyPairIndex(eurusdIdx);

        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        SubscriptionResponse response = ((NoOpMessageGateway) gateway).getLastResponse();
       assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers1 = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),MDFAggregationType.BEST_PRICE);
        Set<String> servers1 = tiers1.values().iterator().next();
        assertTrue(servers1.contains("uig1"));
        System.out.println("tiers 1--> " +tiers1);


        request.setId("20001");
        request.setAggregationType(MDFAggregationType.FULL_BOOK);
        sm.handleSubscriptionRequest(request, "uig2", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers2 = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.FULL_BOOK);
        servers1 = tiers2.values().iterator().next();
        assertTrue(servers1.contains("uig2"));
        System.out.println("tiers 2--> " +tiers2);

        request.setId("30001");
        request.setAggregationType(MDFAggregationType.RAW_BOOK);
        sm.handleSubscriptionRequest(request, "uig3", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers3  = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.RAW_BOOK);
        servers1 = tiers3.values().iterator().next();
        assertTrue(servers1.contains("uig3"));
        System.out.println("tiers 3--> " +tiers3);

        request.setId("10001");
        request.setType(SubscriptionType.UNSUBSCRIBE);
        request.setAggregationType(MDFAggregationType.RAW_BOOK);
        sm.handleSubscriptionRequest(request, "uig3", "routing_key");
        Map<Long,Set<String>> tiers4  = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.RAW_BOOK);

        System.out.println("tiers after unsubscribe--> " +tiers4);

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());

        //make sure "uig1" is no longer subscriber
        Map<Long,Set<String>> tiers = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.RAW_BOOK);
        System.out.println("tiers after unsubscribe RB--> " +tiers);
        assertTrue(tiers.isEmpty());

        request.setId("20001");
        request.setAggregationType(MDFAggregationType.FULL_BOOK);
        //"uig1" should remain subscribed in other aggregation types
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.FULL_BOOK);
        Set<String> servers = tiers.values().iterator().next();
        System.out.println("tiers after unsubscribe FB--> " +tiers);
        assertTrue(servers.contains("uig2"));


        request.setId("30001");
        request.setAggregationType(MDFAggregationType.BEST_PRICE);
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.BEST_PRICE);
        servers = tiers.values().iterator().next();
        System.out.println("tiers after unsubscribe BP--> " +tiers);
        assertTrue(servers.contains("uig1"));

    }

    /**
      uig1 - MTFOK, uig2 - RBA, uig3 - FBA
     Check - get(20001) - fetches value for key with liquidity 2000
     get(30001) - fetches value for key with liquidity 3000
     */
    @Test
    public void testMultipleSubscribersMultipleAgg()throws Exception {

        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        //service.provision(TESTFI1);
        //FIProvision fi1 = serverProvision.getFIProvision(1001);
        Optional<Integer> EUR = serverProvision.getCcyIndex("EUR");
        Optional<Integer> USD = serverProvision.getCcyIndex("USD");
        int eurusdIdx = Util.getCurrencyPairIndex(EUR.get(), USD.get());

        RateDistributionManager rdm = getNoHopManager(serverProvision);
        MessageGateway gateway = new NoOpMessageGateway();

        SubscriptionManager sm = new SubscriptionManager(serverProvision, rdm, service);
        sm.setGateway(gateway);

        long [] defTiers ={2000,3000};
        SubscriptionRequest request = new SubscriptionRequest();
        request.setType(SubscriptionType.SUBSCRIBE);
        request.setId("10001");
        request.setTiers(defTiers);
        request.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);
        request.setOrgIndex(1001);// fi1's index
        request.setCurrencyPairIndex(eurusdIdx);

        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        SubscriptionResponse response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),MDFAggregationType.MULTI_TIER_FOK);
        System.out.println("tiers->"+tiers);
        //Sample result -- tiers->{2000=[uig1], 3000=[uig1]}
        assertTrue(Sets.difference(tiers.get(2000l), Sets.newHashSet("uig1")).isEmpty());
        assertTrue(Sets.difference(tiers.get(3000l), Sets.newHashSet("uig1")).isEmpty());
        //assertTrue(tiers.containsValue("uig1"));

        request.setId("20001");
        request.setAggregationType(MDFAggregationType.BEST_PRICE);
        sm.handleSubscriptionRequest(request, "uig2", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.BEST_PRICE);
        System.out.println("Tiers BEST_PRICE" +tiers.toString());
        Set<String> servers = tiers.values().iterator().next();
        assertTrue(servers.contains("uig2"));

        request.setId("30001");
        request.setAggregationType(MDFAggregationType.RAW_BOOK);
        sm.handleSubscriptionRequest(request, "uig3", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.RAW_BOOK);
        System.out.println("Tiers RAW_BOOK" +tiers.entrySet());
        servers = tiers.values().iterator().next();
        assertTrue(servers.contains("uig3"));

        //convert request into unsubscribe request
        request.setId("10001");
        request.setTiers(defTiers);
        request.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.MULTI_TIER_FOK);
        //assertFalse(sm.subscriptions.isEmpty());
        //make sure "uig1" is no longer subscriber
        //assertFalse(servers.contains("uig1"));

        System.out.println("tiers after unsubscprption for MTFOK->"+tiers);
        assertTrue(tiers.isEmpty());

        tiers = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.BEST_PRICE);
        servers = tiers.values().iterator().next();
        System.out.println("tiers BP after unsubscrption->"+tiers);
        assertTrue(servers.contains("uig2"));

        tiers = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.RAW_BOOK);
        servers = tiers.values().iterator().next();
        System.out.println("tiers RB after unsubscrption->"+tiers);
        assertTrue(servers.contains("uig3"));

    }

    //FULL BOOK, Multiple subscribers
    @Test
    public void testMultipleSubscribersSameAgg()throws Exception {

        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        Optional<Integer> EUR = serverProvision.getCcyIndex("EUR");
        Optional<Integer> USD = serverProvision.getCcyIndex("USD");
        int eurusdIdx = Util.getCurrencyPairIndex(EUR.get(), USD.get());

        RateDistributionManager rdm = getNoHopManager(serverProvision);
        MessageGateway gateway = new NoOpMessageGateway();

        SubscriptionManager sm = new SubscriptionManager(serverProvision, rdm, service);
        sm.setGateway(gateway);

        SubscriptionRequest request = new SubscriptionRequest();
        request.setType(SubscriptionType.SUBSCRIBE);
        request.setId("10001");
        request.setAggregationType(MDFAggregationType.FULL_BOOK);
        request.setOrgIndex(1001);// fi1's index
        request.setCurrencyPairIndex(eurusdIdx);

        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        SubscriptionResponse response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),MDFAggregationType.FULL_BOOK);
        Set<String> servers = tiers.values().iterator().next();
        assertTrue(servers.contains("uig1"));

        request.setId("20001");
        request.setAggregationType(MDFAggregationType.FULL_BOOK);
        sm.handleSubscriptionRequest(request, "uig2", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.FULL_BOOK);
        servers = tiers.values().iterator().next();
        assertTrue(servers.contains("uig2"));

        //convert request into unsubscribe request
        request.setId("10001");
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.FULL_BOOK);
        //make sure "uig1" is no longer subscriber
        servers = tiers.values().iterator().next();
        assertTrue(servers.contains("uig2"));
        assertFalse(servers.contains("uig1"));
    }


    @Test
    public void testMultipleSubscribersSameAgg2TiersforBoth()throws Exception {

        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        //service.provision(TESTFI1);
        //FIProvision fi1 = serverProvision.getFIProvision(1001);
        Optional<Integer> EUR = serverProvision.getCcyIndex("EUR");
        Optional<Integer> USD = serverProvision.getCcyIndex("USD");
        int eurusdIdx = Util.getCurrencyPairIndex(EUR.get(), USD.get());

        RateDistributionManager rdm = getNoHopManager(serverProvision);
        MessageGateway gateway = new NoOpMessageGateway();

        SubscriptionManager sm = new SubscriptionManager(serverProvision, rdm, service);
        sm.setGateway(gateway);

        long[] tiersL = {1000,2000};
        SubscriptionRequest request = new SubscriptionRequest();
        request.setType(SubscriptionType.SUBSCRIBE);
        request.setId("10001");
        request.setTiers(tiersL);
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        request.setOrgIndex(1001);// fi1's index
        request.setCurrencyPairIndex(eurusdIdx);

        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        SubscriptionResponse response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),MDFAggregationType.WEIGHTED_AVERAGE);
        assertEquals(tiersL.length,tiers.size()); //only BPA has no tiers
        assertTrue(Sets.difference(tiers.get(1000l), Sets.newHashSet("uig1")).isEmpty());

        long[] tiersL2 = {1000,2000};
        request.setTiers(tiersL2);
        request.setId("20001");
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        sm.handleSubscriptionRequest(request, "uig2", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.WEIGHTED_AVERAGE);
        assertEquals(tiersL2.length,tiers.size()); //only FBA has no tiers
        assertTrue(Sets.difference(tiers.get(2000l), Sets.newHashSet("uig1","uig2")).isEmpty());
        System.out.println("Tiers --> "+tiers);

        //convert request into unsubscribe request
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.WEIGHTED_AVERAGE);
        //make sure "uig1" is no longer subscriber
        assertFalse(Sets.difference(tiers.get(1000l), Sets.newHashSet("uig1")).isEmpty());
        assertTrue(Sets.difference(tiers.get(2000l), Sets.newHashSet("uig2")).isEmpty());

    }


    @Test
    public void testMultipleSubscribersSameAggMisMatchTiers()throws Exception {

        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        Optional<Integer> EUR = serverProvision.getCcyIndex("EUR");
        Optional<Integer> USD = serverProvision.getCcyIndex("USD");
        int eurusdIdx = Util.getCurrencyPairIndex(EUR.get(), USD.get());

        RateDistributionManager rdm = getNoHopManager(serverProvision);
        MessageGateway gateway = new NoOpMessageGateway();

        SubscriptionManager sm = new SubscriptionManager(serverProvision, rdm, service);
        sm.setGateway(gateway);

        long[] tiersL = {1000}; //,2000,3000
        SubscriptionRequest request = new SubscriptionRequest();
        request.setType(SubscriptionType.SUBSCRIBE);
        request.setId("10001");
        request.setTiers(tiersL);
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        request.setOrgIndex(1001);// fi1's index
        request.setCurrencyPairIndex(eurusdIdx);

        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        SubscriptionResponse response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),MDFAggregationType.WEIGHTED_AVERAGE);
        assertEquals(tiersL.length,tiers.size()); //only BPA has no tiers
        assertTrue(Sets.difference(tiers.get(1000l), Sets.newHashSet("uig1")).isEmpty());
        System.out.println("tiers->"+tiers);

        long[] tiersL2 = {1000,2000};
        request.setTiers(tiersL2);
        request.setId("20001");
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        sm.handleSubscriptionRequest(request, "uig2", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers2 = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.WEIGHTED_AVERAGE);
        assertEquals(tiersL2.length,tiers2.size()); //only FBA has no tiers
        assertTrue(Sets.difference(tiers2.get(2000l), Sets.newHashSet("uig1","uig2")).isEmpty());
        System.out.println("Tiers --> "+tiers2);


        //convert request into unsubscribe request
        request.setId("20001");
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig2", "routing_key");
         tiers2 = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.WEIGHTED_AVERAGE);
        System.out.println("Tiers after unsubscribe--> "+tiers2);

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        //make sure "uig1" is no longer subscriber
        assertTrue(Sets.difference(tiers2.get(1000l), Sets.newHashSet("uig1")).isEmpty());


    }


    @Test
    public void testMultipleSubscribersSameAggMisMatchTiers_Unsubscribe()throws Exception {

        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        Optional<Integer> EUR = serverProvision.getCcyIndex("EUR");
        Optional<Integer> USD = serverProvision.getCcyIndex("USD");
        int eurusdIdx = Util.getCurrencyPairIndex(EUR.get(), USD.get());

        RateDistributionManager rdm = getNoHopManager(serverProvision);
        MessageGateway gateway = new NoOpMessageGateway();

        SubscriptionManager sm = new SubscriptionManager(serverProvision, rdm, service);
        sm.setGateway(gateway);

        long[] tiersL = {1000}; //,2000,3000
        SubscriptionRequest request = new SubscriptionRequest();
        request.setType(SubscriptionType.SUBSCRIBE);
        request.setId("10001");
        request.setTiers(tiersL);
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        request.setOrgIndex(1001);// fi1's index
        request.setCurrencyPairIndex(eurusdIdx);

        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        SubscriptionResponse response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),MDFAggregationType.WEIGHTED_AVERAGE);
        assertEquals(tiersL.length,tiers.size()); //only BPA has no tiers
        assertTrue(Sets.difference(tiers.get(1000l), Sets.newHashSet("uig1")).isEmpty());
        System.out.println("tiers->"+tiers);

        long[] tiersL2 = {1000,2000};
        request.setTiers(tiersL2);
        request.setId("20001");
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        sm.handleSubscriptionRequest(request, "uig2", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers2 = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.WEIGHTED_AVERAGE);
        assertEquals(tiersL2.length,tiers2.size()); //only FBA has no tiers
        assertTrue(Sets.difference(tiers2.get(2000l), Sets.newHashSet("uig1","uig2")).isEmpty());
        System.out.println("Tiers --> "+tiers2);


        //convert request into unsubscribe request
        request.setId("20001");
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        tiers2 = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.WEIGHTED_AVERAGE);
        System.out.println("Tiers after unsubscribe--> "+tiers2);

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        //make sure "uig1" is no longer subscriber
        assertTrue(Sets.difference(tiers2.get(2000l), Sets.newHashSet("uig2")).isEmpty());
        assertTrue(Sets.difference(tiers2.get(1000l), Sets.newHashSet("uig2")).isEmpty());

        /**Sample result--
         * tiers->{1000=[uig1]}
         * Tiers --> {2000=[uig2], 1000=[uig1, uig2]}
         * Tiers after unsubscribe--> {2000=[uig2], 1000=[uig2]}
         */

    }

    @Test
    public void testMultipleSubscribersSameAggMisMatchTiers2()throws Exception {

        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        Optional<Integer> EUR = serverProvision.getCcyIndex("EUR");
        Optional<Integer> USD = serverProvision.getCcyIndex("USD");
        int eurusdIdx = Util.getCurrencyPairIndex(EUR.get(), USD.get());

        RateDistributionManager rdm = getNoHopManager(serverProvision);
        MessageGateway gateway = new NoOpMessageGateway();

        SubscriptionManager sm = new SubscriptionManager(serverProvision, rdm, service);
        sm.setGateway(gateway);

        long[] tiersL = {1000,2000};
        SubscriptionRequest request = new SubscriptionRequest();
        request.setType(SubscriptionType.SUBSCRIBE);
        request.setId("10001");
        request.setTiers(tiersL);
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        request.setOrgIndex(1001);// fi1's index
        request.setCurrencyPairIndex(eurusdIdx);

        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        SubscriptionResponse response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),MDFAggregationType.WEIGHTED_AVERAGE);
        assertEquals(tiersL.length,tiers.size()); //only BPA has no tiers
        assertTrue(Sets.difference(tiers.get(1000l), Sets.newHashSet("uig1")).isEmpty());


        long[] tiersL2 = {1000};
        request.setTiers(tiersL2);
        request.setOrgIndex(1002);// fi1's index
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        sm.handleSubscriptionRequest(request, "uig2", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers2 = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.WEIGHTED_AVERAGE);
        assertEquals(tiersL2.length,tiers2.size()); //only FBA has no tiers
        System.out.println("Tiers---> "+tiers2);
        assertTrue(Sets.difference(tiers2.get(1000l), Sets.newHashSet("uig2")).isEmpty());

        //convert request into unsubscribe request
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        tiers2 = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.WEIGHTED_AVERAGE);
        System.out.println("Tiers---> "+tiers2);
        assertTrue(Sets.difference(tiers2.get(1000l), Sets.newHashSet("uig2")).isEmpty());

    }



    /**
      uig1 - MTFOK, RBA, uig2  - FBA, BPA
      unsubscribe from uig1 MTFOK, uig2 RBA, others  should remain subscribed
     */
    @Test
    public void testMultipleSubscribersMultipleUnsubscription()throws Exception {

        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        Optional<Integer> EUR = serverProvision.getCcyIndex("EUR");
        Optional<Integer> USD = serverProvision.getCcyIndex("USD");
        int eurusdIdx = Util.getCurrencyPairIndex(EUR.get(), USD.get());

        RateDistributionManager rdm = getNoHopManager(serverProvision);
        MessageGateway gateway = new NoOpMessageGateway();

        SubscriptionManager sm = new SubscriptionManager(serverProvision, rdm, service);
        sm.setGateway(gateway);

        long[] tiersL = {1000,2000,3000,4000,5000};
        SubscriptionRequest request = new SubscriptionRequest();
        request.setType(SubscriptionType.SUBSCRIBE);
        request.setId("10001");
        request.setTiers(tiersL);
        request.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);
        request.setOrgIndex(1001);// fi1's index
        request.setCurrencyPairIndex(eurusdIdx);

        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        SubscriptionResponse response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),MDFAggregationType.MULTI_TIER_FOK);
        assertEquals(tiersL.length,tiers.size()); //only BPA has no tiers
        System.out.println("tiers->"+tiers);
        assertTrue(Sets.difference(tiers.get(1000l), Sets.newHashSet("uig1")).isEmpty());

        request.setId("20001");
        request.setAggregationType(MDFAggregationType.BEST_PRICE);
        sm.handleSubscriptionRequest(request, "uig2", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.BEST_PRICE);
        System.out.println("tiers 2->"+tiers);
        Set<String> servers = tiers.values().iterator().next();
        assertTrue(servers.contains("uig2"));

        request.setId("30001");
        request.setAggregationType(MDFAggregationType.RAW_BOOK);
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.RAW_BOOK);
        System.out.println("Tiers RAW_BOOK" +tiers.entrySet());
        System.out.println("tiers->"+tiers);
        servers = tiers.values().iterator().next();
        assertTrue(servers.contains("uig1"));

        request.setId("40001");
        request.setAggregationType(MDFAggregationType.FULL_BOOK);
        sm.handleSubscriptionRequest(request, "uig2", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.FULL_BOOK);
        System.out.println("Tiers BEST_PRICE" +tiers.toString());
        System.out.println("tiers 4 before unsubscribe->"+tiers);
        servers = tiers.values().iterator().next();
        assertTrue(servers.contains("uig2"));

        //convert request into unsubscribe request
        request.setId("20001");
        request.setAggregationType(MDFAggregationType.BEST_PRICE);
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig2", "routing_key");

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        System.out.println("unubscription response is,,, "+((NoOpMessageGateway) gateway).lastresponse);
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.BEST_PRICE);
        System.out.println("tiers  after unsubscribe BP->"+tiers);
        assertTrue(tiers.isEmpty());

        //convert request into unsubscribe request
        request.setId("10001");
        request.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        System.out.println("unubscription response is,,, "+((NoOpMessageGateway) gateway).lastresponse);
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),EUR.get(),USD.get(),MDFAggregationType.MULTI_TIER_FOK);
        System.out.println("tiers  after unsubscribe MTFOK->"+tiers);
        assertTrue(tiers.isEmpty());

    }


    /**
     * Subscribe first for BEST price unsubscribe and,
     * then second time for MULTI-TIERFOK using same subscriber
     * @throws Exception
     */
    @Test
    public void testReSubscribe() throws Exception {

        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        //service.provision(TESTFI1);
        //FIProvision fi1 = serverProvision.getFIProvision(1001);
        Optional<Integer> EUR = serverProvision.getCcyIndex("EUR");
        Optional<Integer> USD = serverProvision.getCcyIndex("USD");
        int eurusdIdx = Util.getCurrencyPairIndex(EUR.get(), USD.get());

        RateDistributionManager rdm = getNoHopManager(serverProvision);
        MessageGateway gateway = new NoOpMessageGateway();

        SubscriptionManager sm = new SubscriptionManager(serverProvision, rdm, service);
        sm.setGateway(gateway);

        SubscriptionRequest request = new SubscriptionRequest();
        request.setType(SubscriptionType.SUBSCRIBE);
        request.setId("10001");
        request.setAggregationType(MDFAggregationType.BEST_PRICE);
        request.setOrgIndex(1001);// fi1's index
        request.setCurrencyPairIndex(eurusdIdx);

        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        SubscriptionResponse response = ((NoOpMessageGateway) gateway).getLastResponse();

        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());

        //convert request into unsubscribe request
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");

        //re-subscribe for MULTI_TIER_FOK
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertNoSubscribers(sm);

        request.setType(SubscriptionType.SUBSCRIBE);
        request.setId("10001");
        request.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);
        request.setOrgIndex(1001);// fi1's index
        request.setCurrencyPairIndex(eurusdIdx);

        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();

        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());

        //convert request into unsubscribe request
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertNoSubscribers(sm);
    }



    /**
     * Test the number of tiers subscribed Vwap aggregation
      * @throws Exception
     */
    @Test
    public void testSubscribegetTiersVwap() throws Exception {

        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        //service.provision(TESTFI1);
        Optional<Integer> EUR = serverProvision.getCcyIndex("EUR");
        Optional<Integer> USD = serverProvision.getCcyIndex("USD");
        int eurusdIdx = Util.getCurrencyPairIndex(EUR.get(), USD.get());

//        FIProvision fiProvision = serverProvision.getFIProvision(1001);
//        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
//        impl.setAggregationTiers(new double[]{1000, 2000, 3000});
//        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
//        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager rdm = getNoHopManager(serverProvision);
        MessageGateway gateway = new NoOpMessageGateway();

        SubscriptionManager sm = new SubscriptionManager(serverProvision, rdm, service);
        sm.setGateway(gateway);

        //noOfTiers=5;
        long[] tiersL = {1000,2000,3000,4000,5000};
        SubscriptionRequest request = new SubscriptionRequest();
        request.setType(SubscriptionType.SUBSCRIBE);
        request.setId("10001");
        request.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        request.setOrgIndex(1001);// fi1's index
        request.setCurrencyPairIndex(eurusdIdx);
        request.setTiers(tiersL);

        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        SubscriptionResponse response = ((NoOpMessageGateway) gateway).getLastResponse();

        String aggtype = request.getAggregationType().toString();
        assertEquals(aggtype, "WEIGHTED_AVERAGE");
        System.out.println("request.gettiers   \n" +request.getTiers());

        Map<Long,Set<String>> tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),MDFAggregationType.WEIGHTED_AVERAGE);
        System.out.println("request.tiers.size()   \n" +tiers.toString());
        assertEquals(tiersL.length,tiers.size()); //only BPA has no tiers

        System.out.println("tiers->"+tiers);
        assertTrue(Sets.difference(tiers.get(1000l), Sets.newHashSet("uig1")).isEmpty());

        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertFalse(sm.subscriptions.isEmpty());

        //convert request into unsubscribe request
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        assertNoSubscribers(sm);

    }



   /**
     * Trying to unsubscribe from a non-existing subscriber,
     * wont fail as per the update received, (its  unsubscription from uigdoesnotexist is also returining sucess)
     * check--does not happen in real time, unsubscription from a non-existing subscriber
     */
    @Test
    public void testUnSubscribe() throws Exception {

        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        Optional<Integer> EUR = serverProvision.getCcyIndex("EUR");
        Optional<Integer> USD = serverProvision.getCcyIndex("USD");
        int eurusdIdx = Util.getCurrencyPairIndex(EUR.get(), USD.get());

        RateDistributionManager rdm = getNoHopManager(serverProvision);
        MessageGateway gateway = new NoOpMessageGateway();

        SubscriptionManager sm = new SubscriptionManager(serverProvision, rdm, service);
        sm.setGateway(gateway);

        SubscriptionRequest request = new SubscriptionRequest();
        request.setType(SubscriptionType.SUBSCRIBE);
        request.setId("10001");
        request.setAggregationType(MDFAggregationType.FULL_BOOK);
        request.setOrgIndex(1001);// fi1's index
        request.setCurrencyPairIndex(eurusdIdx);

        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        SubscriptionResponse response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        request.setId("20001");
        sm.handleSubscriptionRequest(request, "uig2", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        request.setId("30001");
        sm.handleSubscriptionRequest(request, "uig3", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        assertFalse(sm.subscriptions.isEmpty());
        Map<Long,Set<String>> tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());
        assertEquals(1,tiers.size()); //only BPA has no tiers
        System.out.println("Tiers,..." +tiers);
        Set<String> servers = tiers.values().iterator().next();

        assertTrue(servers.contains("uig1"));
        assertTrue(servers.contains("uig2"));
        assertTrue(servers.contains("uig3"));

        //convert request into unsubscribe requestw
        request.setId("10001");
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uig1", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());
        System.out.println("Tiers after unsubscribe 1,..." +tiers);
         servers = tiers.values().iterator().next();
        assertFalse(servers.contains("uig1"));
        assertTrue(servers.contains("uig2"));
        assertTrue(servers.contains("uig3"));

        request.setId("20001");
        sm.handleSubscriptionRequest(request, "uig2", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());
        System.out.println("Tiers after unsubscribe 2,..." +tiers);
        assertFalse(servers.contains("uig1"));
        assertFalse(servers.contains("uig2"));
        assertTrue(servers.contains("uig3"));

        request.setId("30001");
        sm.handleSubscriptionRequest(request, "uig3", "routing_key");
        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());
        System.out.println("Tiers after unsubscribe 3,..." +tiers);
        assertFalse(servers.contains("uig1"));
        assertFalse(servers.contains("uig2"));
        assertFalse(servers.contains("uig3"));


        //convert request into unsubscribe request
        request.setType(SubscriptionType.UNSUBSCRIBE);
        sm.handleSubscriptionRequest(request, "uigdoesnotexist", "routing_key");

        response = ((NoOpMessageGateway) gateway).getLastResponse();
        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        //assertEquals(Status.FAILURE, response.getStatus());

        assertFalse(sm.subscriptions.isEmpty());
        tiers = sm.getSubscribedTiers(request.getOrgIndex(),
                EUR.get(),USD.get(),request.getAggregationType());
        System.out.println("Tiers after unsubscribe failure..." +tiers);
        assertFalse(tiers.containsValue("uig1"));

    }


    static void assertNoSubscribers(SubscriptionManager sm){
        //make sure none of tiers have any subscribers
        for(NonBlockingHashMapLong<Set<String>> tiersmap : sm.subscriptions.values()){
            for( Set<String> subscribers : tiersmap.values() ){
                assertTrue(subscribers.isEmpty());
            }
        }
    }

    static void assertNoSubscribers(SubscriptionManager sm, long key){
        //make sure none of tiers have any subscribers
        NonBlockingHashMapLong<Set<String>> tiersmap = sm.subscriptions.get(key);
        if(tiersmap!=null) {
            for (Set<String> subscribers : tiersmap.values()) {
                assertTrue(subscribers.isEmpty());
            }
        }
    }

    static void assertNoSubscribers(SubscriptionManager sm, long key,long tier){
        //make sure none of tiers have any subscribers
        NonBlockingHashMapLong<Set<String>> tiersmap = sm.subscriptions.get(key);
        if(tiersmap!=null) {
            Set<String> subscribers = tiersmap.get(tier);
            assertTrue(subscribers.isEmpty());
        }
    }



}
