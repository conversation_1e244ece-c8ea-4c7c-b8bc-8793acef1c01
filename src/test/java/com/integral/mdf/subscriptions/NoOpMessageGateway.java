package com.integral.mdf.subscriptions;

import com.integral.mdf.MessageGateway;
import com.integral.notifications.mdf.SubscriptionResponse;

import java.util.Optional;

public class NoOpMessageGateway implements MessageGateway {

    SubscriptionResponse lastresponse;

    @Override
    public void sendResponse(SubscriptionResponse response, Optional<String> senderId) {
        this.lastresponse = response;
    }

    public SubscriptionResponse getLastResponse(){
        return this.lastresponse;
    }
}
