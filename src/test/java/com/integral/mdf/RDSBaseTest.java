package com.integral.mdf;

import java.io.InputStream;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.*;

import com.integral.mdf.cache.CacheClientC;
import com.integral.model.ReferenceEntity;
import com.integral.organization.MulticastAddress;
import com.integral.provision.*;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.integral.mdf.data.CreditLimitInfo;
import com.integral.model.OracleEntity;
import com.integral.model.registry.EntityRegistry;
import com.integral.rds.client.ClientFactory;
import com.integral.rds.client.ReferenceDataService;
import com.integral.rds.config.ServiceConfigMBeanC;
import com.integral.rds.exception.ReferenceDataServiceException;
import com.integral.rds.notification.NotificationService.NotificationEvent;
import com.integral.rds.setup.TestServerStartup;
import com.integral.virtualserver.MDFEntity;

public abstract class RDSBaseTest {

	private static final String MDF_SERVER_CLUSTER = "MDFServerCluster";

	protected static final int MAX_DEPTH = 2;
	protected  static final int PRICE_BOOK_PORT = 9078;
	protected  static final int VENUE_PORT = 9056;
	protected  static final String VENUE_MC_ADDRESS = "************";
	protected  static final String VENUE_MC_ADDRESS_1 = "************";
	protected  static final int CREDIT_MC_PORT = 4548;
	protected  static final String CREDIT_MC_GROUP = "***********";
	
	protected  static final int LR_MC_PORT = 4549;
	protected  static final String LR_MC_GROUP = "***********";
	
	protected  static final int PA_MC_PORT = 3609;
	protected  static final String PA_MC_GROUP = "*********";
	
	protected  static final String TESTFI1 = "testfi1";
	protected static final String TESTFI2 = "testfi2";

	private static final String STDQOTCNV = "STDQOTCNV";
	private static final String MAIN = "main";
	private static final String TESTVENUE = "testvenue";
	private static final String MARKET_DATA_STREAM = "market_data_stream";
	private static final String VENUE_STREAM = "venuestream";
	
	protected String lp1 = "{\"shortName\":\"lp1\",\"streamSuperLPIndex\":1001,\"fiCmId\":7001,\"streamIndex\":1,\"streamName\":\"stream1\",\"_id\":\"lp1\",\"namespaceName\":\"main\",\"versionId\":0}";
	protected String lp2 = "{\"shortName\":\"lp2\",\"streamSuperLPIndex\":1002,\"fiCmId\":7002,\"streamIndex\":2,\"streamName\":\"stream2\",\"_id\":\"lp2\",\"namespaceName\":\"main\",\"versionId\":0}";
	protected String lp3 = "{\"shortName\":\"lp3\",\"streamSuperLPIndex\":1003,\"fiCmId\":7003,\"streamIndex\":3,\"streamName\":\"stream3\",\"_id\":\"lp3\",\"namespaceName\":\"main\",\"versionId\":0}";
	protected String lp4 = "{\"shortName\":\"lp4\",\"streamSuperLPIndex\":1004,\"fiCmId\":7004,\"streamIndex\":4,\"streamName\":\"stream4\",\"_id\":\"lp4\",\"namespaceName\":\"main\",\"versionId\":0}";
	protected String lp5 = "{\"shortName\":\"lp5\",\"streamSuperLPIndex\":1005,\"fiCmId\":7005,\"streamIndex\":5,\"streamName\":\"stream5\",\"_id\":\"lp5\",\"namespaceName\":\"main\",\"versionId\":0}";
	protected String lp6 = "{\"shortName\":\"lp6\",\"streamSuperLPIndex\":1006,\"fiCmId\":7006,\"streamIndex\":6,\"streamName\":\"stream6\",\"_id\":\"lp6\",\"namespaceName\":\"main\",\"versionId\":0}";
	protected String lp7 = "{\"shortName\":\"lp7\",\"streamSuperLPIndex\":1007,\"fiCmId\":7007,\"streamIndex\":7,\"streamName\":\"stream7\",\"_id\":\"lp7\",\"namespaceName\":\"main\",\"versionId\":0}";
	protected String lp8 = "{\"shortName\":\"lp8\",\"streamSuperLPIndex\":1008,\"fiCmId\":7008,\"streamIndex\":8,\"streamName\":\"stream8\",\"_id\":\"lp8\",\"namespaceName\":\"main\",\"versionId\":0}";
	protected String lp9 = "{\"shortName\":\"lp9\",\"streamSuperLPIndex\":1009,\"fiCmId\":7009,\"streamIndex\":9,\"streamName\":\"stream9\",\"_id\":\"lp9\",\"namespaceName\":\"main\",\"versionId\":0}";
	protected String lp10 = "{\"shortName\":\"lp10\",\"streamSuperLPIndex\":1010,\"fiCmId\":7010,\"streamIndex\":10,\"streamName\":\"stream10\",\"_id\":\"lp10\",\"namespaceName\":\"main\",\"versionId\":0}";
	protected String lp11 = "{\"shortName\":\"lp11\",\"streamSuperLPIndex\":1011,\"fiCmId\":7011,\"streamIndex\":11,\"streamName\":\"stream11\",\"_id\":\"lp11\",\"namespaceName\":\"main\",\"versionId\":0}";

	protected String[] lp1_definedCcyPairs = new String[] {"EUR/USD", "USD/JPY","EUR/JPY"}; //,"USD/CHF","EUR/CHF"
	protected String[] lp2_definedCcyPairs = new String[] {"EUR/USD", "USD/CAD"};  //,"EUR/CAD","EUR/AUD"
	protected String[] lp3_definedCcyPairs = new String[] {"EUR/USD", "USD/JPY"}; //,"EUR/CHF","USD/CAD"
	protected String[] lp4_definedCcyPairs = new String[] {"EUR/USD"};
	protected String[] lp5_definedCcyPairs = new String[] {"EUR/USD"};
	protected String[] lp6_definedCcyPairs = new String[] {"EUR/USD"};
	protected String[] lp7_definedCcyPairs = new String[] {"EUR/USD"}; //,"EUR/CHF","USD/CAD"
	protected String[] lp8_definedCcyPairs = new String[] {"EUR/USD"}; //,"EUR/CHF","USD/CAD"
	protected String[] lp9_definedCcyPairs = new String[] {"EUR/USD"}; //,"EUR/CHF","USD/CAD"
	protected String[] lp10_definedCcyPairs = new String[] {"EUR/USD"}; //,"EUR/CHF","USD/CAD"
	protected String[] lp11_definedCcyPairs = new String[] {"EUR/USD"}; //,"EUR/CHF","USD/CAD"

	protected String lp1_Spreads = "[{\"lp\":\"lp1\",\"ccyPair\":\"EUR/USD\",\"spreads\":[{\"ccyPair\":\"EUR/USD\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":false,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.1,\"pipsOfferSpread\":0.1,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0},{\"ccyPair\":\"EUR/USD\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":true,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.2,\"pipsOfferSpread\":0.2,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0},{\"lp\":\"lp1\",\"ccyPair\":\"EUR/JPY\",\"spreads\":[{\"ccyPair\":\"EUR/JPY\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":true,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.2,\"pipsOfferSpread\":0.2,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0},{\"ccyPair\":\"EUR/JPY\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":false,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.1,\"pipsOfferSpread\":0.1,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0}]";
	protected String lp2_Spreads = "[{\"lp\":\"lp2\",\"ccyPair\":\"EUR/USD\",\"spreads\":[{\"ccyPair\":\"EUR/USD\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":false,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.1,\"pipsOfferSpread\":0.1,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0},{\"lp\":\"lp2\",\"ccyPair\":\"EUR/JPY\",\"spreads\":[{\"ccyPair\":\"EUR/JPY\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":true,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.2,\"pipsOfferSpread\":0.2,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0}]";
	protected String lp3_Spreads = "[{\"lp\":\"lp3\",\"ccyPair\":\"EUR/USD\",\"spreads\":[{\"ccyPair\":\"EUR/USD\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":false,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.1,\"pipsOfferSpread\":0.1,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0},{\"lp\":\"lp2\",\"ccyPair\":\"EUR/JPY\",\"spreads\":[{\"ccyPair\":\"EUR/JPY\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":true,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.2,\"pipsOfferSpread\":0.2,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0}]";
	protected String lp4_Spreads = "[{\"lp\":\"lp4\",\"ccyPair\":\"EUR/USD\",\"spreads\":[{\"ccyPair\":\"EUR/USD\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":false,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.1,\"pipsOfferSpread\":0.1,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0},{\"lp\":\"lp2\",\"ccyPair\":\"EUR/JPY\",\"spreads\":[{\"ccyPair\":\"EUR/JPY\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":true,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.2,\"pipsOfferSpread\":0.2,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0}]";
	protected String lp5_Spreads = "[{\"lp\":\"lp5\",\"ccyPair\":\"EUR/USD\",\"spreads\":[{\"ccyPair\":\"EUR/USD\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":false,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.1,\"pipsOfferSpread\":0.1,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0},{\"lp\":\"lp2\",\"ccyPair\":\"EUR/JPY\",\"spreads\":[{\"ccyPair\":\"EUR/JPY\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":true,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.2,\"pipsOfferSpread\":0.2,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0}]";
	protected String lp6_Spreads = "[{\"lp\":\"lp6\",\"ccyPair\":\"EUR/USD\",\"spreads\":[{\"ccyPair\":\"EUR/USD\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":false,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.1,\"pipsOfferSpread\":0.1,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0},{\"lp\":\"lp2\",\"ccyPair\":\"EUR/JPY\",\"spreads\":[{\"ccyPair\":\"EUR/JPY\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":true,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.2,\"pipsOfferSpread\":0.2,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0}]";
	protected String lp7_Spreads = "[{\"lp\":\"lp7\",\"ccyPair\":\"EUR/USD\",\"spreads\":[{\"ccyPair\":\"EUR/USD\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":false,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.1,\"pipsOfferSpread\":0.1,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0},{\"lp\":\"lp2\",\"ccyPair\":\"EUR/JPY\",\"spreads\":[{\"ccyPair\":\"EUR/JPY\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":true,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.2,\"pipsOfferSpread\":0.2,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0}]";
	protected String lp8_Spreads = "[{\"lp\":\"lp8\",\"ccyPair\":\"EUR/USD\",\"spreads\":[{\"ccyPair\":\"EUR/USD\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":false,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.1,\"pipsOfferSpread\":0.1,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0},{\"lp\":\"lp2\",\"ccyPair\":\"EUR/JPY\",\"spreads\":[{\"ccyPair\":\"EUR/JPY\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":true,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.2,\"pipsOfferSpread\":0.2,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0}]";
	protected String lp9_Spreads = "[{\"lp\":\"lp9\",\"ccyPair\":\"EUR/USD\",\"spreads\":[{\"ccyPair\":\"EUR/USD\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":false,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.1,\"pipsOfferSpread\":0.1,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0},{\"lp\":\"lp2\",\"ccyPair\":\"EUR/JPY\",\"spreads\":[{\"ccyPair\":\"EUR/JPY\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":true,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.2,\"pipsOfferSpread\":0.2,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0}]";
	protected String lp10_Spreads = "[{\"lp\":\"lp10\",\"ccyPair\":\"EUR/USD\",\"spreads\":[{\"ccyPair\":\"EUR/USD\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":false,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.1,\"pipsOfferSpread\":0.1,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0},{\"lp\":\"lp2\",\"ccyPair\":\"EUR/JPY\",\"spreads\":[{\"ccyPair\":\"EUR/JPY\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":true,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.2,\"pipsOfferSpread\":0.2,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0}]";
	protected String lp11_Spreads = "[{\"lp\":\"lp11\",\"ccyPair\":\"EUR/USD\",\"spreads\":[{\"ccyPair\":\"EUR/USD\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":false,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.1,\"pipsOfferSpread\":0.1,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0},{\"lp\":\"lp2\",\"ccyPair\":\"EUR/JPY\",\"spreads\":[{\"ccyPair\":\"EUR/JPY\",\"fwdSpreadGrpEnabled\":false,\"fwdSpreadInterpolate\":false,\"fwdSpreadType\":0,\"rawbidspread\":0.0,\"rawofferspread\":0.0,\"rawbidForwardPointSpread\":0.0,\"rawofferForwardPointSpread\":0.0,\"isGivebackAllowed\":true,\"giveBackPercentage\":0.0,\"isEnabled\":true,\"isGivebackUtilizedOrderMatching\":false,\"isKeepPriceImprovement\":false,\"isPreTradeEnabled\":false,\"enableSkew\":false,\"rawBidSkew\":0.0,\"rawOfferSkew\":0.0,\"bidSkewType\":0,\"offerSkewType\":0,\"pipsBidSpreadWithImprovement\":0.0,\"pipsOfferSpreadWithImprovement\":0.0,\"pipsBidSpreadImprovement\":0.0,\"pipsOfferSpreadImprovement\":0.0,\"pipsBidSpread\":0.2,\"pipsOfferSpread\":0.2,\"pipsBidSkew\":0.0,\"pipsBidGiveBackSkew\":0.0,\"pipsOfferGiveBackSkew\":0.0,\"pipsOfferSkew\":0.0,\"processUsingLiveFXMDS\":false,\"namespaceName\":\"main\",\"versionId\":0}],\"namespaceName\":\"main\",\"versionId\":0}]";

	String[] definedUserNames = new String[] { "testuser1@testfi1",
			"testuser2@testfi1", "testuser3@testfi1", "testuser1@testfi2",
			"testuser2@testfi2", "testuser3@testfi2" };

	String[] definedOrgNames = new String[] { TESTFI1, TESTFI2 ,TESTVENUE};
	int[] definedOrgIndexes = new int[] { 1001, 1002 , 1003};
	int[] definedOrgLEIndexes = new int[] { 9001, 9002 , 9003};
	String[] definedOrgMCGroup = new String[] { "************", "************" , VENUE_MC_ADDRESS};
	
	String[] definedLPOrgNames = new String[] { "lp1", "lp2" ,"lp3","lp4","lp5","lp6"};
	int[] definedLPOrgIndexes= new int[] { 100, 101 ,102, 104, 105, 106};

	String[] definedCcyPairs = new String[] { "USD/DKK", "NZD/TWD", "EUR/SEK",
			"NZD/JPY", "GBP/AED", "EUR/NOK", "GBP/DKK", "NZD/USD", "CAD/JPY",
			"USD/CZK", "EUR/GBP", "EUR/NZD", "GBP/SEK", "AUD/NZD", "CHF/JPY",
			"GBP/AUD", "EUR/MXN", "SEK/JPY", "GBP/USD", "AUD/JPY", "USD/MXN",
			"EUR/USD", "USD/HKD", "AUD/USD", "USD/CAD", "AUD/CAD", "USD/ZAR",
			"USD/PLN", "EUR/JPY", "EUR/PLN", "USD/NOK", "GBP/CHF", "USD/CHF",
			"AUD/CHF", "EUR/CHF", "USD/JPY", "GBP/CAD", "CAD/CHF", "GBP/JPY",
			"EUR/CAD", "EUR/DKK", "EUR/AUD", "USD/SEK", "EUR/GBP", "USD/CAD",
			"USD/JPY", "EUR/CZK", "EUR/JPY", "USD/CHF", "EUR/USD", "EUR/CHF" };
	String[] definedCcyNames = new String[] { "LSL", "CYP", "SAR", "CLP",
			"NGN", "LKR", "PLN", "ISK", "NAD", "KZT", "AUD", "INR", "MZM",
			"JOD", "EEK", "OMR", "KWD", "ILS", "MWK", "RUB", "JMD", "DKK",
			"NZD", "TND", "MRO", "ROL", "HTG", "NOK", "THB", "MGF", "QAR",
			"HNL", "MYR", "SKK", "AED", "USD", "CHF", "GTQ", "MXN", "SGD",
			"JPY", "RUX", "GMD", "CZK", "MUR", "BIF", "CNY", "IDR", "COP",
			"PKR", "GHC", "UGX", "MTL", "BGN", "BRL", "HUF", "ZWD", "PHP",
			"VEB", "ETB", "TZS", "BHD", "MAD", "BDT", "KES", "HKD", "ZMK",
			"PEN", "TWD", "ECS", "TTD", "LBP", "BBD", "EGP", "XPF", "ARS",
			"KRW", "TRL", "DOP", "SZL", "ANG", "XCD", "BZD", "DJF", "SVC",
			"ZAR", "XAF", "BYR", "CVE", "STD", "VUV", "BOB", "CRC", "LVL",
			"SCR", "CAD", "PYG", "VND", "GBP", "BND", "NPR", "LTL", "SBD",
			"EUR", "SEK", "UYU", "NIO" };
	int[] definedCcyIndexes = new int[] { 51, 37, 107, 50, 12, 86, 31, 26, 97,
			67, 35, 105, 22, 94, 65, 33, 78, 48, 10, 84, 53, 100, 46, 9, 58,
			96, 23, 13, 87, 83, 72, 103, 62, 28, 91, 75, 44, 5, 54, 17, 68, 36,
			90, 61, 27, 99, 69, 38, 106, 76, 45, 7, 60, 25, 98, 14, 88, 29,
			101, 73, 41, 16, 66, 34, 104, 2, 56, 20, 93, 63, 30, 74, 43, 4, 81,
			52, 15, 89, 59, 24, 95, 40, 3, 80, 21, 70, 42, 6, 82, 77, 47, 64,
			32, 71, 1, 102, 39, 79, 49, 11, 85, 55, 18, 92, 8, 57, 19 };

	static Properties prop = new Properties();

	public RDSBaseTest() {
		Assert.assertEquals(
				"Not matching length of defined ccy names and indexes",
				definedCcyNames.length, definedCcyIndexes.length);
		Assert.assertEquals(
				"Not matching length of defined org names and indexes",
				definedOrgNames.length, definedOrgIndexes.length);
	}
	
	static int a = 1;
	
//	@BeforeClass
//	public static void startRDSServerTest() throws Exception {
//		String s = "test";
//		System.out.println("a = " + a++);
//	}

	@BeforeClass
	public static void startRDSServer() throws Exception {

		InputStream is = ClassLoader.getSystemClassLoader()
				.getResourceAsStream(DefaultProvisioningTest.TEST_CONFIG_PROPS);
		prop.load(is);
		TestServerStartup.startRDSServer(prop,false);
		ServiceConfigMBeanC.getInstance().initialize(prop);
		ClientFactory.getFactory().init(ServiceConfigMBeanC.getInstance(),
				ServiceConfigMBeanC.getInstance().getRDSClientId());
	}

	@AfterClass
	public static void shutDownRDSServer() throws Exception {
		TestServerStartup.shutDownRDSServer();
	}

	protected void buildMDFEntityDataInRDS() throws Exception {
		ReferenceDataService rds = ClientFactory.getFactory()
				.getReferenceDataService();
		Assert.assertNotNull(rds.create(getMDFEntity()));
	}

	protected void buildServerRunTimeData() {
		EntityRegistry.registerOracleEntity(ServerRuntimeQuery.class,
				new OracleEntityHandler() {
					@Override
					public List<OracleEntity> handle(OracleEntity oracleEntity,
							NotificationEvent event)
							throws ReferenceDataServiceException {
						List<OracleEntity> entities = new ArrayList<OracleEntity>();
						ServerRuntime serverRunTime = new ServerRuntime();
						serverRunTime.setPriceBookMCPort(PRICE_BOOK_PORT);
						serverRunTime.setUserPrefix("test");
						serverRunTime.setCreditMCAddress(CREDIT_MC_GROUP);
						serverRunTime.setCreditMCPort(CREDIT_MC_PORT);
						serverRunTime.setZooQuorumString("localhost:2181");
						serverRunTime.setClusterNamespace("test");
						serverRunTime.setCreditMCAddress(CREDIT_MC_GROUP);
						serverRunTime.setCreditMCPort(CREDIT_MC_PORT);
						serverRunTime.setLiveRateMCAddress(LR_MC_GROUP);
						serverRunTime.setLiveRateMCPort(LR_MC_PORT);
						serverRunTime.setProviderMCAddress(PA_MC_GROUP);
						serverRunTime.setProviderMCPort(PA_MC_PORT);
						serverRunTime.setMvCreditCutOffPercent(10);
						serverRunTime.setDataCenterName("Test");
						entities.add(serverRunTime);
						return entities;
					}
				}, null);

	}

	protected void buildOrgProvisionData() {
		EntityRegistry.registerOracleEntity(OrgProvisionQuery.class,
				new OracleEntityHandler() {
					@Override
					public List<OracleEntity> handle(OracleEntity oracleEntity,
							NotificationEvent event)
							throws ReferenceDataServiceException {
						
						OrgProvisionQuery query = (OrgProvisionQuery) oracleEntity;
						
						List<String> orgNames = query.getOrgNames();
						Assert.assertEquals(1, orgNames.size());
						return getOrgsProvision(orgNames.get(0));
					}
				}, null);
	}

	protected void buildCcyProvisionData() {
		EntityRegistry.registerOracleEntity(CurrencyProvisionQuery.class,
				new OracleEntityHandler() {
					@Override
					public List<OracleEntity> handle(OracleEntity oracleEntity,
							NotificationEvent event)
							throws ReferenceDataServiceException {
						return getCcysProvision();
					}
				}, null);
	}
	
	protected void buildOrgIndexMappingData() {
		EntityRegistry.registerOracleEntity(OrgIndexProvisionQuery.class,
				new OracleEntityHandler() {
					@Override
					public List<OracleEntity> handle(OracleEntity oracleEntity,
							NotificationEvent event)
							throws ReferenceDataServiceException {
						OrgIndexProvisionQuery oipq = (OrgIndexProvisionQuery) oracleEntity;
						return "MDFFIS".equalsIgnoreCase(oipq.getShortName())? getOrgIndexFIsProvisions() : getOrgIndexProvisions();
					}
				}, null);
	}

	protected void buildUserProvisionData() {
		EntityRegistry.registerOracleEntity(UserProvisionQuery.class,
				new OracleEntityHandler() {
					@Override
					public List<OracleEntity> handle(OracleEntity oracleEntity,
							NotificationEvent event)
							throws ReferenceDataServiceException {
						return getUserProvisions();
					}
				}, null);
	}

	protected void buildCcyPairProvisionData() {
		EntityRegistry.registerOracleEntity(
				FXRateConventionProvisionQuery.class,
				new OracleEntityHandler() {
					@Override
					public List<OracleEntity> handle(OracleEntity oracleEntity,
							NotificationEvent event)
							throws ReferenceDataServiceException {

						FXRateConventionProvisionQuery query = (FXRateConventionProvisionQuery) oracleEntity;
						Assert.assertEquals(STDQOTCNV, query.getShortName());
						return getRateConventionProvisions();
					}
				}, null);
	}
	
	
	protected void buildLPProvisionData() {
		EntityRegistry.registerOracleEntity(
				LPProvisionQuery.class,
				new OracleEntityHandler() {
					@Override
					public List<OracleEntity> handle(OracleEntity oracleEntity,
							NotificationEvent event)
							throws ReferenceDataServiceException {
						return getLPProvisions();
					}
				}, null);
	}

	protected void buildMulticastAddressData() throws ReferenceDataServiceException {
		ReferenceDataService rds = ClientFactory.getFactory()
				.getReferenceDataService();
		Assert.assertNotNull(rds.create(getMulticastAddress()));
	}

	protected ReferenceEntity getMulticastAddress(){
		String organization = CacheClientC.MDF_CACHE;
		String type = CacheClientC.MULTICAST_HEARTBEAT;
		String shortName = organization + "-" + type;

		MulticastAddress ma = new MulticastAddress();
		ma.setNamespaceName("MAIN");
		ma.setOrganization(organization);
		ma.setType(type);
		ma.setShortName(shortName);
		ma.setMulitcastAddress("*********");

		return ma;
	}


	protected List<OracleEntity> getOrgIndexProvisions(){
		List<OracleEntity> list = new ArrayList<OracleEntity>();
		OrgIndexProvision oip = new OrgIndexProvision();
		oip.setNamespaceName("main");
		Map<String,Integer> map = new HashMap<>();
		for (int i=0;i<definedLPOrgNames.length;i++) {
			map.put(definedLPOrgNames[i], definedLPOrgIndexes[i]);
		}
		oip.setOrgIndexes(map);
		return list;
	}

	protected List<OracleEntity> getOrgIndexFIsProvisions(){
		List<OracleEntity> list = new ArrayList<OracleEntity>();
		OrgIndexProvision oip = new OrgIndexProvision();
		oip.setNamespaceName("main");
		Map<String,Integer> map = new HashMap<>();
		for (int i=0;i<definedOrgNames.length;i++) {
			map.put(definedOrgNames[i], definedOrgIndexes[i]);
		}
		oip.setOrgIndexes(map);
		list.add(oip);
		return list;
	}
	
	
	protected List<OracleEntity> getLPProvisions() {
		
		List<OracleEntity> lps = new ArrayList<OracleEntity>();
		LPProvision fromJson = new Gson().fromJson(lp1, LPProvision.class);
		fromJson.setLrEnabled(true);
		fromJson.setStreamStatus(true);
		fromJson.setCcyPairs(getCcyPairProv(lp1_definedCcyPairs));
		Type listType = new TypeToken<List<PPPProvision>>(){}.getType();
		List<PPPProvision> ppps1= new Gson().fromJson(lp1_Spreads,listType);
		fromJson.setPriceProvisions(ppps1);
		lps.add(fromJson);

		LPProvision fromJson2 = new Gson().fromJson(lp2, LPProvision.class);
		fromJson2.setLrEnabled(true);
		fromJson2.setStreamStatus(true);
		lps.add(fromJson2);
		fromJson2.setCcyPairs(getCcyPairProv(lp2_definedCcyPairs));
		List<PPPProvision> ppps2 = new Gson().fromJson(lp2_Spreads, listType);
		fromJson2.setPriceProvisions(ppps2);


		LPProvision fromJson3 = new Gson().fromJson(lp3, LPProvision.class);
		fromJson3.setLrEnabled(true);
		fromJson3.setStreamStatus(true);
		lps.add(fromJson3);
		fromJson3.setCcyPairs(getCcyPairProv(lp3_definedCcyPairs));
		List<PPPProvision> ppps3 = new Gson().fromJson(lp3_Spreads, listType);
		fromJson3.setPriceProvisions(ppps3);

		LPProvision fromJson4 = new Gson().fromJson(lp4, LPProvision.class);
		fromJson4.setLrEnabled(true);
		fromJson4.setStreamStatus(true);
		lps.add(fromJson4);
		fromJson4.setCcyPairs(getCcyPairProv(lp4_definedCcyPairs));
		List<PPPProvision> ppps4 = new Gson().fromJson(lp4_Spreads, listType);
		fromJson4.setPriceProvisions(ppps4);

		LPProvision fromJson5 = new Gson().fromJson(lp5, LPProvision.class);
		fromJson5.setLrEnabled(true);
		fromJson5.setStreamStatus(true);
		lps.add(fromJson5);
		fromJson5.setCcyPairs(getCcyPairProv(lp5_definedCcyPairs));
		List<PPPProvision> ppps5 = new Gson().fromJson(lp5_Spreads, listType);
		fromJson5.setPriceProvisions(ppps5);

		LPProvision fromJson6 = new Gson().fromJson(lp6, LPProvision.class);
		fromJson6.setLrEnabled(true);
		fromJson6.setStreamStatus(true);
		lps.add(fromJson6);
		fromJson6.setCcyPairs(getCcyPairProv(lp6_definedCcyPairs));
		List<PPPProvision> ppps6 = new Gson().fromJson(lp6_Spreads, listType);
		fromJson6.setPriceProvisions(ppps6);

		LPProvision fromJson7 = new Gson().fromJson(lp7, LPProvision.class);
		fromJson7.setLrEnabled(true);
		fromJson7.setStreamStatus(true);
		lps.add(fromJson7);
		fromJson7.setCcyPairs(getCcyPairProv(lp7_definedCcyPairs));
		List<PPPProvision> ppps7 = new Gson().fromJson(lp7_Spreads, listType);
		fromJson7.setPriceProvisions(ppps7);

		LPProvision fromJson8 = new Gson().fromJson(lp8, LPProvision.class);
		fromJson8.setLrEnabled(true);
		fromJson8.setStreamStatus(true);
		lps.add(fromJson8);
		fromJson8.setCcyPairs(getCcyPairProv(lp8_definedCcyPairs));
		List<PPPProvision> ppps8 = new Gson().fromJson(lp8_Spreads, listType);
		fromJson8.setPriceProvisions(ppps8);

		LPProvision fromJson9 = new Gson().fromJson(lp9, LPProvision.class);
		fromJson9.setLrEnabled(true);
		fromJson9.setStreamStatus(true);
		lps.add(fromJson9);
		fromJson9.setCcyPairs(getCcyPairProv(lp9_definedCcyPairs));
		List<PPPProvision> ppps9 = new Gson().fromJson(lp9_Spreads, listType);
		fromJson9.setPriceProvisions(ppps9);

		LPProvision fromJson10 = new Gson().fromJson(lp10, LPProvision.class);
		fromJson10.setLrEnabled(true);
		fromJson10.setStreamStatus(true);
		lps.add(fromJson10);
		fromJson10.setCcyPairs(getCcyPairProv(lp10_definedCcyPairs));
		List<PPPProvision> ppps10 = new Gson().fromJson(lp10_Spreads, listType);
		fromJson10.setPriceProvisions(ppps10);

		LPProvision fromJson11 = new Gson().fromJson(lp11, LPProvision.class);
		fromJson11.setLrEnabled(true);
		fromJson11.setStreamStatus(true);
		lps.add(fromJson11);
		fromJson11.setCcyPairs(getCcyPairProv(lp11_definedCcyPairs));
		List<PPPProvision> ppps11 = new Gson().fromJson(lp11_Spreads, listType);
		fromJson11.setPriceProvisions(ppps11);

		return lps;
	}

	
	protected List<OracleEntity> getOrgsProvision(String orgName) {
		List<OracleEntity> orgProvisions = new ArrayList<OracleEntity>();
		for (int i = 0; i < definedOrgIndexes.length; i++) {
			if(orgName.equals(definedOrgNames[i])){
				orgProvisions.add(buildOrgProvision(definedOrgNames[i],
					definedOrgIndexes[i],definedOrgMCGroup[i],definedOrgLEIndexes[i]));
				break;
			}
		}
		return orgProvisions;
	}
	
	protected OrgProvision buildOrgProvision(String shortName, int index,String address,int leindex) {
		OrgProvision orgProvision = new OrgProvision();
		orgProvision.setShortName(shortName);
		orgProvision.setNamespaceName(MAIN);
		if(address!=null){
			orgProvision.setMultiCastAddresses(getMultiCastAddress(address));
		}else{
			orgProvision.setMultiCastAddresses(getMultiCastAddress());
		}
		orgProvision.setIndex(index);
		orgProvision.setDefaultLEIndex(leindex);
		orgProvision.setFxRateConvention(STDQOTCNV);
		orgProvision.setPort(VENUE_PORT);
		orgProvision.setMaxDepth(MAX_DEPTH);
		return orgProvision;
	}

	protected List<OracleEntity> getRateConventionProvisions() {
		return getRateConventionProvisions(definedCcyPairs);
	}
	
	protected List<OracleEntity> getRateConventionProvisions(String[] ccyPairs) {
		List<OracleEntity> cpProvisions = new ArrayList<OracleEntity>();
		Set<String> curencyPairs = new LinkedHashSet<String>(
				Arrays.asList(ccyPairs));

		CurrencyPairProvision cpProvision;
		for (String curencyPair : curencyPairs) {
			cpProvision = new CurrencyPairProvision();
			cpProvision.setShortName(curencyPair);
			cpProvision.setSpotPrecision(4);
			cpProvisions.add(cpProvision);
		}
		return cpProvisions;
	}
	
	protected List<CurrencyPairProvision> getCcyPairProv(String[] ccyPairs) {
		List<CurrencyPairProvision> cpProvisions = new ArrayList<CurrencyPairProvision>();
		Set<String> curencyPairs = new LinkedHashSet<String>(
				Arrays.asList(ccyPairs));

		CurrencyPairProvision cpProvision;
		for (String curencyPair : curencyPairs) {
			cpProvision = new CurrencyPairProvision();
			cpProvision.setShortName(curencyPair);
			cpProvisions.add(cpProvision);
		}
		return cpProvisions;
	}

	protected List<OracleEntity> getUserProvisions() {
		List<OracleEntity> userProvisions = new ArrayList<OracleEntity>();

		for (int i = 0; i < definedUserNames.length; i++) {
			userProvisions.add(buildUserProvision(definedUserNames[i]));
		}
		return userProvisions;
	}

	protected UserProvision buildUserProvision(String userFullName) {
		UserProvision userProvision = new UserProvision();

		String[] split = userFullName.split("@");

		if (split.length != 2) {
			Assert.fail("Invalid user name format defined:" + userFullName);
		}
		userProvision.setUserName(split[0]);
		userProvision.setOrgName(split[1]);
		userProvision.setShortName(userFullName);
		return userProvision;
	}

	protected UserProvision buildUserProvision(String orgName, String userName) {
		UserProvision userProvision = new UserProvision();
		userProvision.setOrgName(orgName);
		userProvision.setUserName(userName);
		userProvision.setShortName(userName + '@' + orgName);
		return userProvision;
	}

	protected List<OracleEntity> getCcysProvision() {
		List<OracleEntity> ccyProvisions = new ArrayList<OracleEntity>();

		for (int i = 0; i < definedCcyNames.length; i++) {
			ccyProvisions.add(buildProvision(definedCcyIndexes[i],
					definedCcyNames[i]));
		}

		return ccyProvisions;
	}
	
	protected CurrencyProvision getUSDProvision() {
		return buildProvision(75, "USD");
	}

	protected CurrencyProvision getEURProvision() {
		return buildProvision(92, "EUR");
	}
	
	protected List<MulticastAddressProvision> getMultiCastAddress(String address) {
		List<MulticastAddressProvision> multiCastAddresses = new ArrayList<MulticastAddressProvision>();
		MulticastAddressProvision provision = new MulticastAddressProvision();
		provision.setAddress(address);
		provision.setStreamName(MARKET_DATA_STREAM);
		provision.setStreamId(-1);
		multiCastAddresses.add(provision);
		return multiCastAddresses;
	}

	protected List<MulticastAddressProvision> getMultiCastAddress() {
		MulticastAddressProvision provision = new MulticastAddressProvision();
		provision.setAddress(VENUE_MC_ADDRESS);
		provision.setStreamName(MARKET_DATA_STREAM);
		MulticastAddressProvision provision1= new MulticastAddressProvision();
		provision1.setAddress(VENUE_MC_ADDRESS);
		provision1.setStreamName(VENUE_STREAM);
		List<MulticastAddressProvision> multiCastAddresses = new ArrayList<MulticastAddressProvision>();
		multiCastAddresses.add(provision);
		multiCastAddresses.add(provision1);
		return multiCastAddresses;
	}

	protected CurrencyProvision buildProvision(int index, String name) {
		CurrencyProvision ccyProvision = new CurrencyProvision();
		ccyProvision.setIndex(index);
		ccyProvision.setShortName(name);
		ccyProvision.setTickValue(0.01d);
		ccyProvision.setRoundingType(BigDecimal.ROUND_DOWN);
		return ccyProvision;
	}

	protected MDFEntity getMDFEntity() {
		MDFEntity provision = new MDFEntity();
		provision.setDataCenterName("Test");
		provision.setShortName("MDF1-Test");
		provision.setTradingVenue(TESTVENUE);
		provision.setNamespaceName(MAIN);
		provision.setClusterName(MDF_SERVER_CLUSTER);
		provision.setStreamName(MARKET_DATA_STREAM);
		provision.setAggregationFrequency("TICK");
		provision.setNodeType(MDFEntity.NODE_PREDEFINED);
		return provision;
	}
	
	 protected CreditLimitInfo createCreditLimitInfo( long fiLe, long lpLe, byte status, short limitCcy, long aggLimit, long aggAvailable, long dailyLimit, long dailyAvailable, short valueDate) {   
        CreditLimitInfo info = new CreditLimitInfo( fiLe, lpLe, valueDate );
        info.setCreditStatus(status);
        info.setLimitCcy(limitCcy);
        info.setAggregateLimit(aggLimit);
        info.setAggregateAvailable(aggAvailable);
        info.setDailyLimit(dailyLimit);
        info.setDailyAvailable(dailyAvailable);
		return info;
	}

	protected abstract String getConfigFileName();

	protected void buildVenueConfigurationData(){
		EntityRegistry.registerOracleEntity(
				VenueConfigurationQuery.class,
				new OracleEntityHandler() {
					@Override
					public List<OracleEntity> handle(OracleEntity oracleEntity,
													 NotificationEvent event)
							throws ReferenceDataServiceException {
						return getVenueConfigurations();
					}
				}, null);
	}

	protected List<OracleEntity> getVenueConfigurations(){
		return Collections.emptyList();
	}


	protected void sleep(long interval){
		try{
			Thread.sleep(interval);
		}catch (Exception e){}
	}
}