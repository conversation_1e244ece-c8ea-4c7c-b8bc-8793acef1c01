package com.integral.mdf.rate;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;

import com.integral.mdf.RateSource;
import com.integral.mdf.data.FIProvisionImpl;
import com.integral.provision.MDFAggregationType;
import org.junit.Test;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.mdf.DefaultProvisioningTest;
import com.integral.mdf.Util;
import com.integral.mdf.data.CreditLimitInfo;
import com.integral.mdf.data.FIProvision;
import com.integral.mdf.data.ServerProvision;
import com.integral.provision.LPProvision;
import org.mockito.Mockito;

import static com.integral.provision.MDFAggregationType.*;
import static org.junit.Assert.*;

public class RateDistributionManagerTest extends DefaultProvisioningTest {

	private static final String EUR_USD = "EUR/USD";

	private static final int HEADER_SIZE = 3;

	private RateDistributionManager manager;

	@Test
	public void testCreatingRateBooksForProvisionedFI() throws Exception {
		ServerProvision serverProvision = doDefaultProvision();

		service.provision(TESTFI1);

		FIProvision fiProvision = serverProvision.getFIProvision(1001);

		manager = new RateDistributionManager(serverProvision);
		manager.setRateSource(Mockito.mock(RateSource.class));
		manager.createRateBooks(fiProvision);

		assertEquals(15, manager.rateChannels.size());

		Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
				.entrySet();

		for (Entry<Long, RateChannel> entry : entrySet) {
			List<RateBook> rateBooks = entry.getValue().getRateBooks();
			assertEquals(1, rateBooks.size());
			for (RateBook rateBook : rateBooks) {
				assertTrue(rateBook.isActive());
				assertEquals(fiProvision.getIndex(),
						rateBook.getFIIndex());
				assertEquals(1,rateBook.bm.active);
			}
		}
		
		Integer commonCcyPairIdx = getCommonCccyIndex(serverProvision, EUR_USD);
		RateChannel rateChannel1 = getCommonRateChannel(serverProvision,1,EUR_USD);		
		
		RateBook book1 = rateChannel1.getRatebook(fiProvision.getIndex(),commonCcyPairIdx,fiProvision.getAggregationType().getIndex());

		RateChannel rateChannel2 = getCommonRateChannel(serverProvision,2,EUR_USD); 
		
		RateBook book2 = rateChannel2.getRatebook(fiProvision.getIndex(), commonCcyPairIdx,fiProvision.getAggregationType().getIndex());

		// Assertion to verify same book across Lp's
		assertSame("Books not same across Lp's", book1, book2);
		
		// Provision another FI 
		service.provision(TESTFI2);

		for(RateBook rBook:manager.rateBooks.values()){			
			assertTrue(rBook.isActive());
		}
		
		manager.removeRateBooks(fiProvision);
		
		for(RateBook rBook:manager.rateBooks.values()){			
			if(rBook.getFIIndex() == fiProvision.getIndex()){
				fail("No rate books for this FI should be present after deactivation");
			}else{
				assertTrue(rBook.isActive());
				assertEquals(1,rBook.bm.active);
			}
		}
	}


	@Test
	public void testMultipleBooksCreateSingleFI() throws Exception {
		ServerProvision serverProvision = doDefaultProvision();

		service.provision(TESTFI1);

		FIProvision fiProvision = serverProvision.getFIProvision(1001);

		manager = new RateDistributionManager(serverProvision);
		manager.setRateSource(Mockito.mock(RateSource.class));
		manager.createRateBooks(fiProvision);

		assertEquals(15, manager.rateChannels.size());

		Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
				.entrySet();

		for (Entry<Long, RateChannel> entry : entrySet) {
			List<RateBook> rateBooks = entry.getValue().getRateBooks();
			assertEquals(1, rateBooks.size());
			for (RateBook rateBook : rateBooks) {
				assertTrue(rateBook.isActive());
				assertEquals(fiProvision.getIndex(),
						rateBook.getFIIndex());
				assertEquals(1,rateBook.bm.active);
				assertEquals(MDFAggregationType.FULL_BOOK.getIndex(),rateBook.aggregationType);
			}
		}

		Integer EURUSDccypidx = getCommonCccyIndex(serverProvision, EUR_USD);
		int EUR = Util.getBaseCurrencyIndex(EURUSDccypidx);
		int USD = Util.getVarCurrencyIndex(EURUSDccypidx);
		RateChannel rateChannel1 = getCommonRateChannel(serverProvision,1,EUR_USD);

		RateBook book1 = rateChannel1.getRatebook(fiProvision.getIndex(),EURUSDccypidx,fiProvision.getAggregationType().getIndex());

		RateChannel rateChannel2 = getCommonRateChannel(serverProvision,2,EUR_USD);

		RateBook book2 = rateChannel2.getRatebook(fiProvision.getIndex(), EURUSDccypidx,fiProvision.getAggregationType().getIndex());

		// Assertion to verify same book across Lp's
		assertSame("Books not same across Lp's", book1, book2);


		((FIProvisionImpl)fiProvision).setAggregationType(RAW_BOOK);
		assertNotNull(manager.createRateBook(fiProvision,EURUSDccypidx));

		Optional<RateBook> rateBookFI1EURUSDRBA = manager.getRateBook(fiProvision.getIndex(),EUR,USD,RAW_BOOK.getIndex());
		Optional<RateBook> rateBookFI1EURUSDFBA = manager.getRateBook(fiProvision.getIndex(),EUR,USD,FULL_BOOK.getIndex());

		assertNotNull(rateBookFI1EURUSDRBA.get());
		assertNotNull(rateBookFI1EURUSDFBA.get());

		assertNotEquals(rateBookFI1EURUSDRBA.get(),rateBookFI1EURUSDFBA.get());
		assertEquals(FULL_BOOK.getIndex(),rateBookFI1EURUSDFBA.get().aggregationType);
		assertEquals(RAW_BOOK.getIndex(),rateBookFI1EURUSDRBA.get().aggregationType);


		// Provision another FI
		service.provision(TESTFI2);

		for(RateBook rBook:manager.rateBooks.values()){
			assertTrue(rBook.isActive());
		}

		manager.removeRateBooks(fiProvision);

		for(RateBook rBook:manager.rateBooks.values()){
			if(rBook.getFIIndex() == fiProvision.getIndex()){
				fail("No rate books for this FI should be present after deactivation");
			}else{
				assertTrue(rBook.isActive());
				assertEquals(1,rBook.bm.active);
			}
		}
	}

	private RateChannel getCommonRateChannel(ServerProvision serverProvision,int streamIndex, String ccyPair) {
		
		Integer commonCcyPairIdx = getCommonCccyIndex(serverProvision, ccyPair);
		int bCcyIndex = Util.getBaseCurrencyIndex(commonCcyPairIdx);
		int vCcyIndex = Util.getVarCurrencyIndex(commonCcyPairIdx);

		return  manager.rateChannels.get(Util
				.getRateChannelKey(streamIndex, bCcyIndex, vCcyIndex));
	}

	protected Integer getCommonCccyIndex(ServerProvision serverProvision,
			String ccyPair) {
		assertTrue("Common Currency missing in the provision",
				serverProvision.getCcyPairIndex(ccyPair).isPresent());
		Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD)
				.get();
		return commonCcyPairIdx;
	}

	@Test
	public void testhandleCreditUpdate() throws Exception {
		ServerProvision serverProvision = doDefaultProvision();

		// provision the fi
		service.provision(TESTFI2);

		FIProvision fiProvision = serverProvision.getFIProvision(1002);

		Collection<LPProvision> lpProvisions = fiProvision.getLPProvisions();

		assertTrue(lpProvisions.iterator().hasNext());

		long lpLeid = lpProvisions.iterator().next().getFiClearingMemberLEId();

		manager = new RateDistributionManager(serverProvision);
		manager.setRateSource(Mockito.mock(RateSource.class));
		manager.createRateBooks(fiProvision);


		Optional<Integer> ccyIndex = serverProvision.getCcyIndex("USD");
		assertTrue(ccyIndex.isPresent());
		short limitCcy = (short) ccyIndex.get().intValue();
		long aggLimit = 10000000;
		long aggAvailable = 9000000;
		long dailyLimit = 20000000;
		long dailyAvailable = 19000000;
		short valDate = 5000;

		Collection<CreditLimitInfo> lines = new ArrayList<CreditLimitInfo>();
		for (int j = 0; j < 5; j++) {
			byte status = Math.random() > 0.5 ? (byte) 1 : (byte) 0;
			CreditLimitInfo cli = createCreditLimitInfo(
					fiProvision.getDefaultLEObjectId() + j, lpLeid + j, status,
					limitCcy, aggLimit + j, aggAvailable + j, dailyLimit + j,
					dailyAvailable + j, (short) (valDate + j));
			lines.add(cli);
		}

		UnSafeBuffer safeBuf = initBuffer(lines.size());

		for (CreditLimitInfo info : lines) {
			info.writeTo(safeBuf);
		}

		safeBuf.flip();
		
		//test partial read and moving through the credit lines
		manager.handleCreditUpdate(safeBuf,new CreditLimitInfo(0, 0, (short) 0));
		
//		RateChannel rateChannel1 = getCommonRateChannel(serverProvision, 1, EUR_USD); 
//		RateBook book1 = rateChannel1.getRatebook(fiProvision.getIndex(),
//				getCommonCccyIndex(serverProvision, EUR_USD));
//		ProvisioningCalculator provisioningCalculator = book1.pcalcRateIndex.get(1);
//		assertNotNull(provisioningCalculator);
	}

	private UnSafeBuffer initBuffer(int linesSize) {
		UnSafeBuffer safeBuf = new UnSafeBuffer();
		byte[] buffer = new byte[(linesSize * CreditLimitInfo.MSG_SIZE)
				+ HEADER_SIZE];
		safeBuf.init(buffer);
		safeBuf.put(CreditLimitInfo.VERSION);
		safeBuf.putShort(CreditLimitInfo.MSG_TYPE);
		return safeBuf;
	}

	@Override
	protected String getConfigFileName() {
		return TEST_CONFIG_PROPS;
	}
}
