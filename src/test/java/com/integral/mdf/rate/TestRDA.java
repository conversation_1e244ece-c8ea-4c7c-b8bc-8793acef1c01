package com.integral.mdf.rate;

import com.integral.mdf.data.*;
import com.integral.provision.MDFAggregationType;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.Assert.assertEquals;

public class TestRDA extends PriceAggregationTest{
    private static final MDFAggregationType TYPE = MDFAggregationType.RAW_DIRECT;

    @Test
    public void TC1_RDAFromSingleLP() throws Exception {
        System.out.println("===== START : TC1_RDAFromSingleLP =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(TYPE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        QuoteC quote = getQuoteC(serverProvision, "EUR/USD", "lp1", fiProvision, 3, 1.24, 1.26);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote);

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in TC1_RDAFromSingleLP ==="+aggregate.toString());
        assertEquals(3, aggregate.getNumBids());
        assertEquals(3, aggregate.getNumOffers());
        assertEquals(1.24, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.23, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[1], 0.001);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.22, aggregate.getBidPrices()[2], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(1.28, aggregate.getOfferPrices()[2], 0.001);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);

        System.out.println("===== END : TC1_RDAFromSingleLP =====");
    }

    @Test
    public void TC2_RDAFromTwoLPs() throws Exception {
        System.out.println("===== START : TC2_RDAFromTwoLPs =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(TYPE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        long time = System.currentTimeMillis();
        QuoteC quote1 = getQuoteC(serverProvision, "EUR/USD", "lp1", fiProvision, 3, 1.34, 1.36);
        quote1.setQuoteCreatedTime(time++);
        manager.handleRate(quote1);
        System.out.println("=====LP1 quote==="+quote1);
        QuoteC quote2 = getQuoteC(serverProvision, "EUR/USD", "lp2", fiProvision, 3, 1.24, 1.26);
        quote2.setQuoteCreatedTime(time++);
        manager.handleRate(quote2);
        System.out.println("=====LP2 quote==="+quote2);

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in TC2_RDAFromTwoLPs ==="+aggregate.toString());
        assertEquals(3, aggregate.getNumBids());
        assertEquals(3, aggregate.getNumOffers());
        assertEquals(1.24, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.23, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[1], 0.001);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.22, aggregate.getBidPrices()[2], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(1.28, aggregate.getOfferPrices()[2], 0.001);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);

        System.out.println("===== END : TC2_RDAFromTwoLPs =====");
    }
}
