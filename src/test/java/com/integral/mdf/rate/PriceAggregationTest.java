package com.integral.mdf.rate;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map.Entry;
import java.util.Set;

import com.integral.mdf.RateSource;
import com.integral.mdf.data.*;
import com.integral.mdf.rate.RateBook;
import com.integral.mdf.rate.RateChannel;
import com.integral.mdf.rate.RateDistributionManager;
import com.integral.mdf.rate.RateProcessor;

import org.junit.Assert;
import org.junit.Test;

import com.google.gson.Gson;
import com.integral.mdf.DefaultProvisioningTest;
import com.integral.mdf.PriceBookSink;
import com.integral.mdf.Util;
import com.integral.model.OracleEntity;
import com.integral.notifications.StreamUpdate;
import com.integral.provision.LPProvision;
import com.integral.provision.MDFAggregationType;
import org.mockito.Mockito;

public class PriceAggregationTest extends DefaultProvisioningTest {

	   private static final String LP22 = "lp2";
	    private static final String LP12 = "lp1";
	    private static final String EUR_USD = "EUR/USD";

	    
	    
	    protected RateBook getBook(ServerProvision serverProvision,
	                               FIProvision fiProvision, RateDistributionManager manager) {
	        return getBook(serverProvision, fiProvision, manager, 1);
	    }

	    protected RateBook getBook(ServerProvision serverProvision,
	                               FIProvision fiProvision, RateDistributionManager manager, int streamIdx) {
	        Assert.assertTrue("Common Currency missing in the provision",
	                serverProvision.getCcyPairIndex(EUR_USD).isPresent());
	        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD)
	                .get();
	        int bCcyIndex = Util.getBaseCurrencyIndex(commonCcyPairIdx);
	        int vCcyIndex = Util.getVarCurrencyIndex(commonCcyPairIdx);

	        RateChannel rateChannel1 = manager.rateChannels.get(Util
	                .getRateChannelKey(streamIdx, bCcyIndex, vCcyIndex));
	        RateBook book1 = rateChannel1.getRatebook(fiProvision.getIndex(),
	                commonCcyPairIdx,fiProvision.getAggregationType().getIndex());
	        return book1;
	    }

		protected CustomRateDistributionManager getCustomManager(ServerProvision serverProvision) throws IOException {
			CustomRateDistributionManager manager = new CustomRateDistributionManager(serverProvision){
				public void handleRate(QuoteC rate) {
					super.handleRate(rate);
					int sIdx = rate.getStreamIdx();
					int cIdx = rate.getCcyPairIdx();
					int bIdx = Util.getBaseCurrencyIndex(cIdx);
					int vIdx = Util.getVarCurrencyIndex(cIdx);
					long key = Util.getRateChannelKey(sIdx, bIdx, vIdx);
					RateChannel channel = rateChannels.get(key);

					if (channel != null) {
						RateProcessor rp = getRateProcessor(cIdx);
						channel.updateRateBooks(rp.sink,rp.rpm);
					}
				}
			};
			manager.setRateSource(Mockito.mock(RateSource.class));
			TestPriceBookSink sink = new TestPriceBookSink();
			RateProcessor[] processors = manager.rateProcessors;
			for (RateProcessor rateProcessor : processors) {
				rateProcessor.sink = sink;
				//Shutting down the rate processor , so that aggregation could be tested directly.
				rateProcessor.shutdown();
			}
			return manager;
		}

	    protected RateDistributionManager getNoHopManager(ServerProvision serverProvision) throws IOException {
	        RateDistributionManager manager = new RateDistributionManager(serverProvision){

	            public void handleRate(QuoteC rate) {
	                super.handleRate(rate);
	                int sIdx = rate.getStreamIdx();
	                int cIdx = rate.getCcyPairIdx();
	                int bIdx = Util.getBaseCurrencyIndex(cIdx);
	                int vIdx = Util.getVarCurrencyIndex(cIdx);
	                long key = Util.getRateChannelKey(sIdx, bIdx, vIdx);
	                RateChannel channel = rateChannels.get(key);

	                if (channel != null) {
	                    RateProcessor rp = getRateProcessor(cIdx);
	                    channel.updateRateBooks(rp.sink,rp.rpm);
	                }
	            }

	        };

			manager.setRateSource(Mockito.mock(RateSource.class));
			TestPriceBookSink sink = new TestPriceBookSink();
	        RateProcessor[] processors = manager.rateProcessors;
	        for (RateProcessor rateProcessor : processors) {
	            rateProcessor.sink = sink;
	            //Shutting down the rate processor , so that aggregation could be tested directly.
	            rateProcessor.shutdown();
	        }
	        return manager;
	    }

	    protected List<OracleEntity> getLPProvisions() {
	        List<OracleEntity> lpProvisions = super.getLPProvisions();
	        LPProvision fromJson = new Gson().fromJson(lp3, LPProvision.class);
	        fromJson.setLrEnabled(true);
			fromJson.setStreamStatus(true);
	        fromJson.setCcyPairs(getCcyPairProv(lp3_definedCcyPairs));
	        lpProvisions.add(fromJson);
	        return lpProvisions;
	    }

	    public QuoteC getInvertedQuoteC(ServerProvision serverProvision, String ccyp,
	                                    String lpName, FIProvision fi, int noOfTiers) {
	        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(ccyp).get();
	        LPProvision lpSelected = null;

	        for (LPProvision lp : fi.getLPProvisions()) {
	            if (lp.getShortName().equals(lpName)) {
	                lpSelected = lp;
	                break;
	            }
	        }
	        assertNotNull(lpSelected);
	        QuoteC quote = new QuoteC();
	        quote.setCcyPairIdx(commonCcyPairIdx);
	        quote.setStreamIdx(Util.getStreamIndex(serverProvision, lpSelected));
	        quote.setValueDate((short) 17500);

	        //bids
	        int bidTiers = noOfTiers;
	        for (int j = 0; j < bidTiers; j++) {
	            quote.setBidTiersNum(quote.getBidTiersNum() + 1);
	            int bid = quote.tierOffset(QuoteC.BUY, j);
	            quote.setPrice(bid, 1.25 - ((double) j / 100));
	            quote.setTotalQty(bid, 1000.0d + (j * 1000.0d));
	            quote.setShowQty(bid, 1000.0d + (j * 1000.0d));
	        }
	        //offers
	        int offerTiers = noOfTiers;
	        for (int j = 0; j < offerTiers; j++) {
	            quote.setOfferTiersNum(quote.getOfferTiersNum() + 1);
	            int offer = quote.tierOffset(QuoteC.SELL, j);
	            quote.setPrice(offer, 1.29 + ((double) j / 100));
	            quote.setTotalQty(offer, 1000.0d + (j * 1000.0d));
	            quote.setShowQty(offer, 1000.0d + (j * 1000.0d));
	        }
	        return quote;
	    }

	    public QuoteC getSingleInvertedQuoteC(ServerProvision serverProvision, String ccyp, String lpName, FIProvision
	            fi, int noOfTiers) {
	        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(ccyp).get();
	        LPProvision lpSelected = null;

	        for (LPProvision lp : fi.getLPProvisions()) {
	            if (lp.getShortName().equals(lpName)) {
	                lpSelected = lp;
	                break;
	            }
	        }
	        assertNotNull(lpSelected);
	        QuoteC quote = new QuoteC();
	        quote.setCcyPairIdx(commonCcyPairIdx);
	        quote.setStreamIdx(Util.getStreamIndex(serverProvision, lpSelected));
	        quote.setValueDate((short) 17500);

	        //bids
	        int bidTiers = noOfTiers;
	        for (int j = 0; j < bidTiers; j++) {
	            quote.setBidTiersNum(quote.getBidTiersNum() + 1);
	            int bid = quote.tierOffset(QuoteC.BUY, j);
	            quote.setPrice(bid, 1.24 - ((double) j / 100));
	            quote.setTotalQty(bid, 1000.0d + (j * 1000.0d));
	            quote.setShowQty(bid, 1000.0d + (j * 1000.0d));
	        }
	        //offers
	        int offerTiers = noOfTiers;
	        for (int j = 0; j < offerTiers; j++) {
	            quote.setOfferTiersNum(quote.getOfferTiersNum() + 1);
	            int offer = quote.tierOffset(QuoteC.SELL, j);
	            quote.setPrice(offer, 1.22 + ((double) j / 100));
	            quote.setTotalQty(offer, 1000.0d + (j * 1000.0d));
	            quote.setShowQty(offer, 1000.0d + (j * 1000.0d));
	        }
	        return quote;
	    }

	    public QuoteC getHighPrecisionQuoteC(ServerProvision serverProvision, String ccyp,
	                                         String lpName, FIProvision fi) {
	        return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.2114242, 1.2448576, 1000.3333d, 1000.3333d);
	    }


	//        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 4, 2, 1.21, 1.24);

	    public QuoteC getQuoteC(ServerProvision serverProvision, String ccyp,
	                            String lpName, FIProvision fi, int numOfTiers) {
	        return getQuoteC(serverProvision, ccyp, lpName, fi, numOfTiers, 1.21, 1.24);
	    }

	    public QuoteC getQuoteC(ServerProvision serverProvision, String ccyp,
	                            String lpName, FIProvision fi) {
	        return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.21, 1.24);
	    }

	    public QuoteC getQuoteC(ServerProvision serverProvision, String ccyp,
	                            String lpName, FIProvision fi, int noOfTiers, double startBid, double startOffer) {
	        return getQuoteC(serverProvision, ccyp, lpName, fi, noOfTiers, startBid, startOffer, 1000.0d, 1000.0d);
	    }

	    public QuoteC getOneSidedQuoteC(ServerProvision serverProvision, String ccyp,
	                                    String lpName, FIProvision fi, int noOfTiers, double startPrice, boolean isBid) {
	        return getOneSidedQuoteC(serverProvision, ccyp, lpName, fi, noOfTiers, startPrice, 1000.0d, isBid);
	    }
	    
	    public QuoteC getQuoteC(ServerProvision serverProvision, String ccyp,
	            String lpName, FIProvision fi, int noOfBidTiers, int noOfOfferTiers, double startBid, double startOffer) {
	    	return getQuoteC(serverProvision, ccyp, lpName, fi, noOfBidTiers, noOfOfferTiers, startBid, startOffer, 1000.0d, 1000.0d);
	    }
	    

	    public QuoteC getQuoteC(ServerProvision serverProvision, String ccyp,
	                            String lpName, FIProvision fi, int noOfTiers, double startBid, double startOffer,
	                            double startBLimit, double startOLimit) {
	        return getQuoteC(serverProvision, ccyp, lpName, fi, noOfTiers, noOfTiers, startBid, startOffer, startBLimit, startOLimit);


	    }

	public QuoteC getQuoteCPrecision(ServerProvision serverProvision, String ccyp, String lpName, FIProvision fi, int noOfTiers, double startBid, double startOffer, double Precision)
	{
		//Precision of 100 means rounds off to two decimal points, 1000 means rounds off to three decimal points
		double startBid1 = Math.round(startBid*Precision)/Precision;
		double startOffer1 = Math.round(startOffer*Precision)/Precision;
		return getQuoteC(serverProvision, ccyp, lpName, fi, noOfTiers, startBid1, startOffer1);

	}



	public QuoteC getQuoteCArray(ServerProvision serverProvision, String ccyp, String lpName, FIProvision fi, int BidnoOfTiers2, int OffernoOfTiers2, double startBid[], double startOffer[],double bidliq[],double offerliq[])
	{
		Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(ccyp).get();
		LPProvision lpSelected = null;

		for (LPProvision lp : fi.getLPProvisions()) {
			if (lp.getShortName().equals(lpName)) {
				lpSelected = lp;
				break;
			}
		}
		assertNotNull(lpSelected);

		QuoteC quote = new QuoteC();
		quote.setCcyPairIdx(commonCcyPairIdx);
		quote.setStreamIdx(Util.getStreamIndex(serverProvision, lpSelected));
		quote.setValueDate((short) 17500);
		quote.setQuoteCreatedTime(System.currentTimeMillis());

		int BidnoOfTiers = BidnoOfTiers2;

		for (int i=0;i<BidnoOfTiers;i++)
		{
			//quote.setPrice(quote.bidTiersOffset(), startBid-i);
			quote.setBidTiersNum(quote.getBidTiersNum()+1);
			int bid1 = quote.tierOffset(QuoteC.BUY, i);
			quote.setPrice(bid1, startBid[i]);
			quote.setTotalQty(bid1, bidliq[i]);
			quote.setShowQty(bid1, bidliq[i]);
		}

		int OffernoOfTiers = OffernoOfTiers2;
		for (int i=0;i<OffernoOfTiers;i++) {
			quote.setOfferTiersNum(quote.getOfferTiersNum() + 1);
			int offer1 = quote.tierOffset(QuoteC.SELL, i);
			quote.setPrice(offer1, startOffer[i]);
			quote.setTotalQty(offer1, offerliq[i]);
			quote.setShowQty(offer1, offerliq[i]);
		}
		return quote;

	}


		public QuoteC getQuoteC(ServerProvision serverProvision, String ccyp, String lpName, FIProvision fi,
				int noOfBidTiers, int noOfOfferTiers, double startBid, double startOffer, double startBLimit,
				double startOLimit) {
			Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(ccyp).get();
			LPProvision lpSelected = null;

			for (LPProvision lp : fi.getLPProvisions()) {
				if (lp.getShortName().equals(lpName)) {
					lpSelected = lp;
					break;
				}
			}
			assertNotNull(lpSelected);

			QuoteC quote = new QuoteC();
			quote.setCcyPairIdx(commonCcyPairIdx);
			quote.setStreamIdx(Util.getStreamIndex(serverProvision, lpSelected));
			quote.setValueDate((short) 17500);
			quote.setQuoteCreatedTime(System.currentTimeMillis());

			// bids
			int bidTiers = noOfBidTiers;
			for (int j = 0; j < bidTiers; j++) {
				quote.setBidTiersNum(quote.getBidTiersNum() + 1);
				int bid = quote.tierOffset(QuoteC.BUY, j);
				quote.setPrice(bid, startBid - ((double) j / 100));
				quote.setTotalQty(bid, startBLimit + (j * 1000.0d));
				quote.setShowQty(bid, startBLimit + (j * 1000.0d));
			}
			// offers
			int offerTiers = noOfOfferTiers;
			for (int j = 0; j < offerTiers; j++) {
				quote.setOfferTiersNum(quote.getOfferTiersNum() + 1);
				int offer = quote.tierOffset(QuoteC.SELL, j);
				quote.setPrice(offer, startOffer + ((double) j / 100));
				quote.setTotalQty(offer, startOLimit + (j * 1000.0d));
				quote.setShowQty(offer, startOLimit + (j * 1000.0d));
			}
			return quote;
		}
	   

	    public QuoteC getOneSidedQuoteC(ServerProvision serverProvision, String ccyp,
	                                    String lpName, FIProvision fi, int noOfTiers, double startPrice, double startLimit, boolean isBid) {
	        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(ccyp).get();
	        LPProvision lpSelected = null;

	        for (LPProvision lp : fi.getLPProvisions()) {
	            if (lp.getShortName().equals(lpName)) {
	                lpSelected = lp;
	                break;
	            }
	        }
	        assertNotNull(lpSelected);

	        QuoteC quote = new QuoteC();
	        quote.setCcyPairIdx(commonCcyPairIdx);
	        quote.setStreamIdx(Util.getStreamIndex(serverProvision, lpSelected));
	        quote.setValueDate((short) 17500);

	        if (isBid) {
	            //bids
	            double startBid = startPrice;
	            double startBLimit = startLimit;

	            int bidTiers = noOfTiers;
	            for (int j = 0; j < bidTiers; j++) {
	                quote.setBidTiersNum(quote.getBidTiersNum() + 1);
	                int bid = quote.tierOffset(QuoteC.BUY, j);
	                quote.setPrice(bid, startPrice - ((double) j / 100));
	                quote.setTotalQty(bid, startBLimit + (j * 1000.0d));
	                quote.setShowQty(bid, startBLimit + (j * 1000.0d));
	            }
	        } else {
	            //offers
	            double startOffer = startPrice;
	            double startOLimit = startLimit;

	            int offerTiers = noOfTiers;
	            for (int j = 0; j < offerTiers; j++) {
	                quote.setOfferTiersNum(quote.getOfferTiersNum() + 1);
	                int offer = quote.tierOffset(QuoteC.SELL, j);
	                quote.setPrice(offer, startOffer + ((double) j / 100));
	                quote.setTotalQty(offer, startOLimit + (j * 1000.0d));
	                quote.setShowQty(offer, startOLimit + (j * 1000.0d));
	            }
	        }
	        return quote;
	    }

	    public static class TestPriceBookSink implements PriceBookSink {
	        private PriceBook last;

	        @Override
	        public void begin() {
	        }

	        @Override
	        public void accept(PriceBook book) {
	            this.setLast(book);
	        }

	        @Override
	        public void end() {
	        }

	        @Override
	        public boolean isActive() {
	            return false;
	        }

	        public PriceBook getLast() {
	            return last;
	        }

	        public void setLast(PriceBook last) {
	            this.last = last;
	        }
	    }

}
