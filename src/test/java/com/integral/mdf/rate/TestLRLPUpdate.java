package com.integral.mdf.rate;

import com.integral.mdf.data.*;
import com.integral.notifications.StreamUpdate;
import com.integral.provision.MDFAggregationType;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;

public class TestLRLPUpdate extends PriceAggregationTest {

    public static String EUR_USD = "EUR/USD";
    public static String LP12 = "lp1";
    public static String LP22 = "lp2";

    @Test
    public void testLRLPUpdateRealLPFBA() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision,5,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();
        System.out.println("=======Aggregated book================" +aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.16, aggregate.getBidPrices()[5], 0.01);


        //testing the aggregation
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(6000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(8000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(10000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(6000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.28, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(1.29, aggregate.getOfferPrices()[5], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(8000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(10000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumOffers());

        //1. disable stream
        manager.handleLiquidityRulesLPUpdate(fiProvision, 1001, StreamUpdate.STREAM_INACTIVE);

        //make sure LP1 is withdrawn
        aggregate = book1.aggregate();
        System.out.println("=======Aggregated book after diaslbing LP1 stream================" +aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[4], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(5, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.28, aggregate.getOfferPrices()[4], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[4], 0.01);
                assertEquals(5, aggregate.getNumOffers());

        //2. make quote from stream which is inactive now
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.21,1.24,1000,1000);
        quote.setStreamIdx(1001);//send for old stream
        manager.handleRate(quote);

        aggregate = book1.aggregate();
        System.out.println("=======Aggregated book again from old stream================" +aggregate.toString());

        //make sure only LP2 is aggregate
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(5, aggregate.getNumBids());
        assertEquals(5, aggregate.getNumOffers());


    }


    @Test
    public void testLRLPUpdateRealLPOfftoONBPA() throws Exception {

        System.out.println("=======START:testLRLPUpdateRealLPOfftoONBPA TestCase start==============");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision,5,1.22,1.25,1000,1000);
        manager.handleRate(quote);

        System.out.println("=======Quote published================");
        //1. disable stream
        manager.handleLiquidityRulesLPUpdate(fiProvision, 1001, StreamUpdate.STREAM_INACTIVE);

        //make sure LP1 is withdrawn
        PriceBook aggregate = book1.aggregate();
        System.out.println("=======Aggregated book after diaslbing LP1 stream================" +aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.01);
        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("Assert done,....");

        manager.handleLiquidityRulesLPUpdate(fiProvision, 1001, StreamUpdate.STREAM_ACTIVE);

        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision,5,1.22,1.25,1000,1000);
        manager.handleRate(quote);

        aggregate = book1.aggregate();
        System.out.println("=======Aggregated book after stream active================" +aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.01);
        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());


    }


   @Test //Review if this is right,.
    public void testLRLPUpdateRealLPTwoFIs() throws  Exception{
       System.out.println("=======START:testLRLPUpdateRealLPTwoFIs TestCase start==============");
       ServerProvision serverProvision = doDefaultProvision();
       ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
       service.provision(TESTFI1);
       service.provision(TESTFI2);  //// Provision another FI

       FIProvision fiProvision = serverProvision.getFIProvision(1001);
       FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
       impl.setAggregationType(MDFAggregationType.FULL_BOOK);

       FIProvision fiProvision2 = serverProvision.getFIProvision(1002);
       FIProvisionImpl impl2 = (FIProvisionImpl) fiProvision2;
       impl2.setAggregationType(MDFAggregationType.FULL_BOOK);

//       fiProvision = serverProvision.getFIProvision(1002);
//       impl = (FIProvisionImpl) fiProvision;
//       impl.setAggregationType(MDFAggregationType.FULL_BOOK);


       RateDistributionManager manager = getNoHopManager(serverProvision);
       RateDistributionManager manager2 = getNoHopManager(serverProvision);
       manager.createRateBooks(fiProvision);
       manager2.createRateBooks(fiProvision2);

       Assert.assertEquals(15, manager.rateChannels.size());
       Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
               .entrySet();

       for (Map.Entry<Long, RateChannel> entry : entrySet) {
           List<RateBook> rateBooks = entry.getValue().getRateBooks();
           Assert.assertEquals(1, rateBooks.size());
           for (RateBook rateBook : rateBooks) {
               Assert.assertTrue(rateBook.isActive());
               Assert.assertEquals(fiProvision.getIndex(),
                   rateBook.getFIIndex());
       }
       }

       Assert.assertEquals(15, manager2.rateChannels.size());
       Set<Map.Entry<Long, RateChannel>> entrySet2 = manager2.rateChannels
               .entrySet();

       for (Map.Entry<Long, RateChannel> entry : entrySet2) {
           List<RateBook> rateBooks = entry.getValue().getRateBooks();
           Assert.assertEquals(1, rateBooks.size());
           for (RateBook rateBook : rateBooks) {
               Assert.assertTrue(rateBook.isActive());
               Assert.assertEquals(fiProvision2.getIndex(),
                       rateBook.getFIIndex());
           }
       }

       RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
       RateBook book2 = getBook(serverProvision, fiProvision2, manager2, 1001);

       Assert.assertNotNull(book1);
       Assert.assertNotNull(book2);
       // Assertion to verify same book across Lp's
       //Assert.assertSame("Books not same across Lp's", book1, book2);

        for (RateBook rBook : manager.rateBooks.values()) {
           Assert.assertTrue(rBook.isActive());
       }

//       for (RateBook rBook : manager.rateBooks.values()) {
//           Assert.assertTrue(rBook.isActive());
//       }

       //publish rates for LP12 on different FIs
       QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,1,1.21,1.24,1000,1000);
       manager.handleRate(quote);

       quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision2,1,1.22,1.25,1000,1000);
       manager2.handleRate(quote);

       System.out.println("=======Quote published================");
       //1. disable stream
       manager.handleLiquidityRulesLPUpdate(fiProvision, 1001, StreamUpdate.STREAM_INACTIVE);
       manager2.handleLiquidityRulesLPUpdate(fiProvision2, 1001, StreamUpdate.STREAM_INACTIVE);

       //Publish different rate from LP22 and aggregate only LP22 should aggregate for fi1
       quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision,1,1.23,1.26,1000,1000);
       manager.handleRate(quote);

       //make sure LP1 is withdrawn
       PriceBook aggregate = book1.aggregate();
       System.out.println("=======Aggregated book after diaslbing LP1 stream================" +aggregate.toString());

       //assert For bid price
       assertEquals(1.23, aggregate.getBidPrices()[0], 0.01);
       //testing the aggregation
       assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
       assertEquals(1, aggregate.getNumBids());

       //assert For offer price
       assertEquals(1.26, aggregate.getOfferPrices()[0], 0.01);
       //The book is sorted at the offer side as well
       assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
       assertEquals(1, aggregate.getNumOffers());


       //Publish different rate from LP22 and aggregate only LP22 should aggregate for fi2
       quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision2,1,1.20,1.23,1000,1000);
       manager2.handleRate(quote);

       //make sure LP1 is withdrawn for FI2 also
       aggregate = book2.aggregate();
       System.out.println("=======Aggregated book2 after diaslbing LP1 stream================" +aggregate.toString());

       //assert For bid price
       assertEquals(1.20, aggregate.getBidPrices()[0], 0.01);
       //testing the aggregation
       assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
       assertEquals(1, aggregate.getNumBids());

       //assert For offer price
       assertEquals(1.23, aggregate.getOfferPrices()[0], 0.01);
       //The book is sorted at the offer side as well
       assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
       assertEquals(1, aggregate.getNumOffers());


        //Make LP12 stream as Active again and aggregate
       manager.handleLiquidityRulesLPUpdate(fiProvision, 1001, StreamUpdate.STREAM_ACTIVE);
       manager2.handleLiquidityRulesLPUpdate(fiProvision2, 1001, StreamUpdate.STREAM_INACTIVE);

       quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,1,1.23,1.25,1000,1000);
       manager.handleRate(quote);

       quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision2,1,1.21,1.26,1000,1000);
       manager2.handleRate(quote);

       //Aggregates using the last two quotes
       aggregate = book1.aggregate();
       System.out.println("=======Aggregated book1 after enabling LP1 stream================" +aggregate.toString());

       //assert For bid price
       assertEquals(1.23, aggregate.getBidPrices()[0], 0.01);
       //testing the aggregation
       assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
       assertEquals(1, aggregate.getNumBids());

       //assert For offer price
       assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
       assertEquals(1.26, aggregate.getOfferPrices()[1], 0.01);
       //The book is sorted at the offer side as well
       assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
       assertEquals(2, aggregate.getNumOffers());

                //FI2
               quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision2,1,1.22,1.27,1000,1000);
               manager.handleRate(quote);

               quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision2,1,1.21,1.25,1000,1000);
               manager2.handleRate(quote);

       aggregate = book2.aggregate();
       System.out.println("=======Aggregated book2 LP12 remains inactive================" +aggregate.toString());

       //assert For bid price
       assertEquals(1.21, aggregate.getBidPrices()[0], 0.01);
       //testing the aggregation
       assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
       assertEquals(1, aggregate.getNumBids());

       //assert For offer price
       assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
       //The book is sorted at the offer side as well
       assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
       assertEquals(1, aggregate.getNumOffers());

   }




}
