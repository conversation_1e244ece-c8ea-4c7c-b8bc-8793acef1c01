package com.integral.mdf.rate;

import static org.junit.Assert.assertEquals;

import com.integral.mdf.rate.treeset.*;
import org.junit.Test;

import com.integral.util.Tuple;

public class TestTreeMapBasedAggregation {
	

	@Test
	public void testMergeOnConflict(){
		
		//Natural order prices
		MergeOnConflictTreeMap<Double, Double> prices = new MergeOnConflictTreeMap<>(new AddValueMergeHandler());
		prices.put(1.2121d, 1000d);
		prices.put(1.2120d, 1000d);
		prices.put(1.2122d, 1000d);
		prices.put(1.2121d, 1000d);
		
		assertEquals(1000d,prices.get(1.2122d) ,1.0d);
		assertEquals(2000d,prices.get(1.2121d) ,1.0d);
		assertEquals(1000d,prices.get(1.2120d) ,1.0d);
	}
	
	
	@Test
	public void testSortAndMergeBidPrices(){
		
		//Natural order prices
		MergeOnConflictTreeMap<Double, Double> prices = new MergeOnConflictTreeMap<>(new AddValueMergeHandler());
		prices.put(1.2121d, 1000d);
		prices.put(1.2120d, 1000d);
		prices.put(1.2122d, 1000d);
		prices.put(1.2121d, 1000d);
		
		assertEquals(1000d,prices.get(1.2122d) ,1.0d);
		assertEquals(2000d,prices.get(1.2121d) ,1.0d);
		assertEquals(1000d,prices.get(1.2120d) ,1.0d);
		
		
		//highest bid to lowest
		prices = new MergeOnConflictTreeMap<>(new BidPriceComparator(),new AddValueMergeHandler());
		prices.put(1.2121d, 1000d);
		prices.put(1.2120d, 1000d);
		prices.put(1.2122d, 1000d);
		prices.put(1.2121d, 1000d);
		
		//test merge
		assertEquals(1000d,prices.get(1.2122d) ,1.0d);
		assertEquals(2000d,prices.get(1.2121d) ,1.0d);
		assertEquals(1000d,prices.get(1.2120d) ,1.0d);
		
		//test sorting 
		assertEquals(1.2122d,prices.pollFirstEntry().getKey(),0.0001d);
		assertEquals(1.2121d,prices.pollFirstEntry().getKey(),0.0001d);
		assertEquals(1.2120d,prices.pollFirstEntry().getKey(),0.0001d);
		

		//lowest offer to highest
		prices = new MergeOnConflictTreeMap<>(new OfferPriceComparator(),new AddValueMergeHandler());
		prices.put(1.2121d, 1000d);
		prices.put(1.2120d, 1000d);
		prices.put(1.2122d, 1000d);
		prices.put(1.2121d, 1000d);
		
		assertEquals(1000d,prices.get(1.2122d) ,1.0d);
		assertEquals(2000d,prices.get(1.2121d) ,1.0d);
		assertEquals(1000d,prices.get(1.2120d) ,1.0d);
	}
	
	
	@Test
	public void testSortAndMergeOfferPrices(){
		
		//Natural order prices
		MergeOnConflictTreeMap<Double, Double> prices = new MergeOnConflictTreeMap<>(new AddValueMergeHandler());
		prices.put(1.2121d, 1000d);
		prices.put(1.2120d, 1000d);
		prices.put(1.2122d, 1000d);
		prices.put(1.2121d, 1000d);
		
		assertEquals(1000d,prices.get(1.2122d) ,1.0d);
		assertEquals(2000d,prices.get(1.2121d) ,1.0d);
		assertEquals(1000d,prices.get(1.2120d) ,1.0d);

		//lowest offer to highest
		prices = new MergeOnConflictTreeMap<>(new OfferPriceComparator(),new AddValueMergeHandler());
		prices.put(1.2121d, 1000d);
		prices.put(1.2120d, 1000d);
		prices.put(1.2122d, 1000d);
		prices.put(1.2121d, 1000d);
		
		assertEquals(1000d,prices.get(1.2122d) ,1.0d);
		assertEquals(2000d,prices.get(1.2121d) ,1.0d);
		assertEquals(1000d,prices.get(1.2120d) ,1.0d);
		
		//test sorting 
		assertEquals(1.2120d,prices.pollFirstEntry().getKey(),0.0001d);
		assertEquals(1.2121d,prices.pollFirstEntry().getKey(),0.0001d);
		assertEquals(1.2122d,prices.pollFirstEntry().getKey(),0.0001d);

	}
	
	
	@Test
	public void testSortAndMergeAndCountBidPrices(){
		
		//Natural order prices
		MergeOnConflictTreeMap<Double, DoubleInteger> prices = new MergeOnConflictTreeMap<>(new AddAddMergeHandler());
		prices.put(1.2121d, new DoubleInteger(1000d,1));
		prices.put(1.2120d, new DoubleInteger(1000d,1));
		prices.put(1.2122d, new DoubleInteger(1000d,1));
		prices.put(1.2121d, new DoubleInteger(1000d,1));
		
		assertEquals(1000d,prices.get(1.2122d).first ,1.0d);
		assertEquals(2000d,prices.get(1.2121d).first ,1.0d);
		assertEquals(1000d,prices.get(1.2120d).first ,1.0d);

		assertEquals(1,prices.get(1.2122d).second ,0.0d);
		assertEquals(2,prices.get(1.2121d).second ,0.0d);
		assertEquals(1,prices.get(1.2120d).second ,0.0d);
		
		
		//highest bid to lowest
		prices = new MergeOnConflictTreeMap<>(new BidPriceComparator(),new AddAddMergeHandler());
		prices.put(1.2121d, new DoubleInteger(1000d,1));
		prices.put(1.2120d, new DoubleInteger(1000d,1));
		prices.put(1.2122d, new DoubleInteger(1000d,1));
		prices.put(1.2121d, new DoubleInteger(1000d,1));
		
		//test merge
		assertEquals(1000d,prices.get(1.2122d).first ,1.0d);
		assertEquals(2000d,prices.get(1.2121d).first ,1.0d);
		assertEquals(1000d,prices.get(1.2120d).first ,1.0d);
		
		//test sorting 
		assertEquals(1.2122d,prices.pollFirstEntry().getKey(),0.0001d);
		assertEquals(1.2121d,prices.pollFirstEntry().getKey(),0.0001d);
		assertEquals(1.2120d,prices.pollFirstEntry().getKey(),0.0001d);
		

		//lowest offer to highest
		prices = new MergeOnConflictTreeMap<>(new OfferPriceComparator(),new AddAddMergeHandler());
		prices.put(1.2121d, new DoubleInteger(1000d,1));
		prices.put(1.2120d, new DoubleInteger(1000d,1));
		prices.put(1.2122d, new DoubleInteger(1000d,1));
		prices.put(1.2121d, new DoubleInteger(1000d,1));

		//test merge
		assertEquals(1000d,prices.get(1.2122d).first ,1.0d);
		assertEquals(2000d,prices.get(1.2121d).first ,1.0d);
		assertEquals(1000d,prices.get(1.2120d).first ,1.0d);
		
		//test sorting
		assertEquals(1.2120d,prices.pollFirstEntry().getKey(),0.0001d);
		assertEquals(1.2121d,prices.pollFirstEntry().getKey(),0.0001d);
		assertEquals(1.2122d,prices.pollFirstEntry().getKey(),0.0001d);		
	}

	
	@Test
	public void testSortAndMergeAndCountBidPricesQA(){
		
		//Natural order prices
		MergeOnConflictTreeMap<Double, DoubleInteger> prices = new MergeOnConflictTreeMap<>(new AddAddMergeHandler());
		prices.put(1.27962d, new DoubleInteger(1000d,1));
		prices.put(1.279635d, new DoubleInteger(1000d,1));
	
		assertEquals(1000d,prices.get(1.27962d).first ,1.0d);
		assertEquals(1000d,prices.get(1.279635d).first ,1.0d);
		

		assertEquals(1,prices.get(1.27962d).second ,0.0d);
		assertEquals(1,prices.get(1.279635d).second ,0.0d);
		
		//highest bid to lowest
		prices = new MergeOnConflictTreeMap<>(new BidPriceComparator(),new AddAddMergeHandler());
		prices.put(1.27962d, new DoubleInteger(1000d,1));
		prices.put(1.279635d, new DoubleInteger(1000d,1));
		
		//test merge
		assertEquals(1000d,prices.get(1.27962d).first ,1.0d);
		assertEquals(1000d,prices.get(1.279635d).first ,1.0d);

		
		//test sorting 
		assertEquals(1.279635d,prices.pollFirstEntry().getKey(),0.0001d);
		assertEquals(1.27962d,prices.pollFirstEntry().getKey(),0.0001d);
		

		//lowest offer to highest
		prices = new MergeOnConflictTreeMap<>(new OfferPriceComparator(),new AddAddMergeHandler());
		prices.put(1.2121d, new DoubleInteger(1000d,1));
		prices.put(1.2120d, new DoubleInteger(1000d,1));
		prices.put(1.2122d, new DoubleInteger(1000d,1));
		prices.put(1.2121d, new DoubleInteger(1000d,1));

		//test merge
		assertEquals(1000d,prices.get(1.2122d).first ,1.0d);
		assertEquals(2000d,prices.get(1.2121d).first ,1.0d);
		assertEquals(1000d,prices.get(1.2120d).first ,1.0d);
		
		//test sorting
		assertEquals(1.2120d,prices.pollFirstEntry().getKey(),0.0001d);
		assertEquals(1.2121d,prices.pollFirstEntry().getKey(),0.0001d);
		assertEquals(1.2122d,prices.pollFirstEntry().getKey(),0.0001d);		
	}
	
}
