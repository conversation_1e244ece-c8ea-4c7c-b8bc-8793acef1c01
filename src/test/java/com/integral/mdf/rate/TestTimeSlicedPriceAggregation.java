package com.integral.mdf.rate;

import com.integral.mdf.data.*;
import com.integral.notifications.StreamUpdate;
import com.integral.provision.MDFAggregationType;
import com.integral.virtualserver.MDFEntity;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.Assert.*;


//added below classes for debuggin
import java.util.Calendar;
import java.util.Date;
import java.time.ZonedDateTime;

public class TestTimeSlicedPriceAggregation extends PriceAggregationTest {


    public static final String LP22 = "lp2";
    public static final String LP12 = "lp1";
    public static final String EUR_USD = "EUR/USD";

    @Test
    public void testTimeSlicedFBA() throws Exception {

        System.out.println("===== START : testTimeSlicedFBA =====");

        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl)serverProvision).setAggregationFrequency("TIMESLCD");


        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        int aggInterval = 2000;
        impl.setAggregationInterval(aggInterval);//in millseconds

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testFBAFromSQLPs ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        //This quote should'n aggregate
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        aggregate = book1.aggregate();

        assertNull(aggregate);

        try {
       //     Thread.sleep(aggInterval + 100);
            Thread.sleep(aggInterval);
        }catch (Exception e){}

        aggregate = book1.aggregate();
        assertNotNull(aggregate);

        System.out.println("===== aggregate quote in testFBAFromSQLPs ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.001);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testTimeSlicedFBA =====");
    }

    @Test
    public void testTimeSlicedFBAWithMultipleQuoteUpdates() throws Exception {

        System.out.println("===== START : testTimeSlicedFBAWithMultipleQuoteUpdates =====");

        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl)serverProvision).setAggregationFrequency("TIMESLCD");


        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        int aggInterval = 2000;
        impl.setAggregationInterval(aggInterval);//in millseconds

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 first quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testTimeSlicedFBAWithMultipleQuoteUpdates ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.23, 1.26);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        assertNull(aggregate);

        //This quote should'n aggregate
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

         //second quote from LP2 should be part of aggregation
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.24, 1.27);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        assertNull(aggregate);

        try {
            //     Thread.sleep(aggInterval + 100);
            Thread.sleep(aggInterval);
        }catch (Exception e){}

        aggregate = book1.aggregate();
        assertNotNull(aggregate);

        System.out.println("===== aggregate quote in testTimeSlicedFBAWithMultipleQuoteUpdates ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.24, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.23, aggregate.getBidPrices()[1], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.26, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[1], 0.001);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testTimeSlicedFBAWithMultipleQuoteUpdates =====");
    }

    @Test
    public void testTimeSlicedFBAWithInactiveQuoteUpdate() throws Exception {

        System.out.println("===== START : testTimeSlicedFBAWithInactiveQuoteUpdate =====");

        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl)serverProvision).setAggregationFrequency("TIMESLCD");


        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        int aggInterval = 2000;
        impl.setAggregationInterval(aggInterval);//in millseconds

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 first quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testTimeSlicedFBAWithInactiveQuoteUpdate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        //This quote should'n aggregate
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.25, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        aggregate = book1.aggregate();
        assertNull(aggregate);

        try {
            //     Thread.sleep(aggInterval + 100);
            Thread.sleep(aggInterval);
        }catch (Exception e){}

        aggregate = book1.aggregate();
        assertNotNull(aggregate);

        System.out.println("===== aggregate quote in testTimeSlicedFBAWithInactiveQuoteUpdate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testTimeSlicedFBAWithInactiveQuoteUpdate =====");
    }

    @Test
    public void testTimeSlicedRBA () throws Exception {

        System.out.println("===== START : testTimeSlicedRBA =====");

        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl)serverProvision).setAggregationFrequency("TIMESLCD");

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        int aggInterval = 2000;
        impl.setAggregationInterval(aggInterval);//in millseconds

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
//        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testTimeSlicedRBA ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        //This quote should'n aggregate
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        aggregate = book1.aggregate();

        assertNull(aggregate);

        try {
            //     Thread.sleep(aggInterval + 100);
            Thread.sleep(aggInterval);
        }catch (Exception e){}

        aggregate = book1.aggregate();
        assertNotNull(aggregate);

        System.out.println("===== aggregate quote in testTimeSlicedRBA ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.22, aggregate.getBidPrices()[1], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.001);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testTimeSlicedRBA =====");

    }

    @Test
    public void testTimeSlicedRBAWithInactiveQuoteUpdate() throws Exception {

        System.out.println("===== START : testTimeSlicedRBAWithInactiveQuoteUpdate =====");

        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl)serverProvision).setAggregationFrequency("TIMESLCD");


        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        int aggInterval = 2000;
        impl.setAggregationInterval(aggInterval);//in millseconds

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 first quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testTimeSlicedFBAWithInactiveQuoteUpdate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        //This quote should'n aggregate
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.25, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        aggregate = book1.aggregate();
        assertNull(aggregate);

        try {
            //     Thread.sleep(aggInterval + 100);
            Thread.sleep(aggInterval);
        }catch (Exception e){}

        aggregate = book1.aggregate();
        assertNotNull(aggregate);

        System.out.println("===== aggregate quote in testTimeSlicedFBAWithInactiveQuoteUpdate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testTimeSlicedFBAWithInactiveQuoteUpdate =====");
    }

    @Test
    public void testTimeSlicedBPA () throws Exception {

        System.out.println("===== START : testTimeSlicedBPA =====");

        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl)serverProvision).setAggregationFrequency("TIMESLCD");

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        int aggInterval = 2000;
        impl.setAggregationInterval(aggInterval);//in millseconds

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testTimeSlicedBPA ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

         //This quote should'n aggregate
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.23, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        aggregate = book1.aggregate();

        assertNull(aggregate);

        try {
            //     Thread.sleep(aggInterval + 100);
            Thread.sleep(aggInterval);
        }catch (Exception e){}

        aggregate = book1.aggregate();
        assertNotNull(aggregate);

        System.out.println("===== aggregate quote in testTimeSlicedBPA ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.23, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testTimeSlicedBPA =====");

    }

    @Test
    public void testTimeSlicedBPAWithInactiveQuoteUpdate() throws Exception {

        System.out.println("===== START : testTimeSlicedBPAWithInactiveQuoteUpdate =====");

        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl)serverProvision).setAggregationFrequency(MDFEntity.AGGREGATION_FREQ_TIME_SLICED);


        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        int aggInterval = 2000;
        impl.setAggregationInterval(aggInterval);//in millseconds

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 first quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testTimeSlicedBPAWithInactiveQuoteUpdate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        //This quote should'n aggregate
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.25, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        aggregate = book1.aggregate();
        assertNull(aggregate);

        try {
            //     Thread.sleep(aggInterval + 100);
            Thread.sleep(aggInterval);
        }catch (Exception e){}

        aggregate = book1.aggregate();
        assertNotNull(aggregate);

        System.out.println("===== aggregate quote in testTimeSlicedBPAWithInactiveQuoteUpdate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testTimeSlicedBPAWithInactiveQuoteUpdate =====");
    }


    @Test
    public void testLRLPUpdateTimeslicedbook() throws Exception {
        System.out.println("===== START : testLRLPUpdateTimeslicedbook =====");

        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        ((ServerProvisionImpl)serverProvision).setAggregationFrequency(MDFEntity.AGGREGATION_FREQ_TIME_SLICED);

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        int aggInterval = 2000;
        impl.setAggregationInterval(aggInterval);//in millseconds


        //prep rdm
        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }


        //Start test here

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision,5,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();
        System.out.println("=======Aggregated book================" +aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.16, aggregate.getBidPrices()[5], 0.01);


        //testing the aggregation
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(6000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(8000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(10000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(6000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.28, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(1.29, aggregate.getOfferPrices()[5], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(8000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(10000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumOffers());

        //1. disable stream
        manager.handleLiquidityRulesLPUpdate(fiProvision, 1001, StreamUpdate.STREAM_INACTIVE);

        //make sure LP1 is withdrawn
        aggregate = book1.aggregate();
        System.out.println("=======Aggregated book after diaslbing LP1 stream================" +aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[4], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(5, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.28, aggregate.getOfferPrices()[4], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(5, aggregate.getNumOffers());


        //wait for aggregation interval to pass
        try{
            Thread.sleep(aggInterval + aggInterval);
        }catch (Exception e){

        }

        //2. make quote from stream which is inactive now
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.21,1.24,1000,1000);
        quote.setStreamIdx(1001);//send for old stream
        manager.handleRate(quote);

        aggregate = book1.aggregate();
        System.out.println("=======Aggregated book again from old stream================" +aggregate.toString());

        //make sure only LP2 is aggregate
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(5, aggregate.getNumBids());
        assertEquals(5, aggregate.getNumOffers());

        System.out.println("===== END : testLRLPUpdateTimeslicedbook =====");

    }

    //Higher agg interval
    @Test
    public void testTimeSlicedFBAHigherInterval() throws Exception {

        System.out.println("===== START : testTimeSlicedFBAHigherInterval =====");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl)serverProvision).setAggregationFrequency("TIMESLCD");
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        int aggInterval = 350000;
        impl.setAggregationInterval(aggInterval);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testTimeSlicedFBAHigherInterval ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        //This quote should'n aggregate
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        aggregate = book1.aggregate();
        assertNull(aggregate);  //No aggregation shud happen within the interval

        Thread.sleep(aggInterval/1000);

        aggregate = book1.aggregate();
        assertNotNull(aggregate);

        System.out.println("===== aggregate quote in testTimeSlicedFBAHigherInterval ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.001);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testTimeSlicedFBAHigherInterval =====");
    }

    @Test
    public void testTimeSlicedFBANoWait() throws Exception {

        System.out.println("===== START : testTimeSlicedFBANoWait =====");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl)serverProvision).setAggregationFrequency("TIMESLCD");
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        int aggInterval = 350000;
        impl.setAggregationInterval(aggInterval);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testTimeSlicedFBANoWait ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        //This quote should'n aggregate
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        aggregate = book1.aggregate();
        assertNull(aggregate);  //No aggregation shud happen within the interval

        Thread.sleep(100); //Wait for a lesser interval and check

        aggregate = book1.aggregate();
        assertNull(aggregate);

    }


    @Test
    public void testTimeSlicedFBALowerInterval() throws Exception {

        System.out.println("===== START : testTimeSlicedFBALowerInterval =====");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl)serverProvision).setAggregationFrequency("TIMESLCD");
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        int aggInterval = 350000;
        impl.setAggregationInterval(aggInterval);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testTimeSlicedFBALowerInterval ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        //This quote should'n aggregate
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        aggregate = book1.aggregate();
        assertNull(aggregate);  //Interval is less so aggregation will happen

        Date date = new Date();
        //This method returns the time in millis
        long timeMilli = date.getTime();
        System.out.println("Time in milliseconds using Date class: " + timeMilli);

        Thread.sleep(aggInterval/1000);

        date = new Date();
        //This method returns the time in millis
        timeMilli = date.getTime();
        System.out.println("Time in milliseconds using Date class: " + timeMilli);

        aggregate = book1.aggregate();
        assertNotNull(aggregate);

        System.out.println("===== aggregate quote in testTimeSlicedFBALowerInterval ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.001);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testTimeSlicedFBALowerInterval =====");
    }

// Below 2 tcs are repetition of other existing tcs. These are added by Spoorthy when millsec is changed to microsecs.
    /**Lower agg interval - when no quote is published within the interval, it still waits
     /If interval is 10ms sec, publish rate at 12th ms which shud also aggregation, because it keeps waiting for quote
     */
    @Test
    public void testTimeSlicedFBALowerIntervalNoQuote() throws Exception {

        System.out.println("===== START : testTimeSlicedFBALowerInterval =====");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl)serverProvision).setAggregationFrequency("TIMESLCD");
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        int aggInterval = 350000;
        impl.setAggregationInterval(aggInterval);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        Thread.sleep(aggInterval/1000);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testTimeSlicedFBALowerIntervalNoQuote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        Date date = new Date();
        //This method returns the time in millis
        long timeMilli = date.getTime();
        System.out.println("Time in milliseconds using Date class: " + timeMilli);
        Thread.sleep(100);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        date = new Date();
        //This method returns the time in millis
        timeMilli = date.getTime();
        System.out.println("Time in milliseconds using Date class: " + timeMilli);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        //No quote was present during wait period aggregation will happen as soon first quote is available
        aggregate = book1.aggregate();
        assertNull(aggregate);

        Thread.sleep(300);

// Below quote is published after the aggregation interval and will get aggregated.
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        aggregate = book1.aggregate();
        assertNotNull(aggregate);

        System.out.println("===== aggregate quote in testTimeSlicedFBALowerInterval ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.001);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testTimeSlicedFBALowerInterval =====");
    }

    @Test
    public void testTimeSlicedFBAHigherIntervalNoQuote() throws Exception {

        System.out.println("===== START : testTimeSlicedFBAHigherIntervalNoQuote =====");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl)serverProvision).setAggregationFrequency("TIMESLCD");
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        int aggInterval = 450000;
        impl.setAggregationInterval(aggInterval);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }


        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testTimeSlicedFBAHigherIntervalNoQuote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        Thread.sleep(aggInterval/1000);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        //No quote was present during agg period aggregation should happen even in wait period as soon first quote is available
        aggregate = book1.aggregate();
        assertNotNull(aggregate);

        System.out.println("==2=== aggregate quote in testTimeSlicedFBAHigherIntervalNoQuote ==="+aggregate.toString());

        System.out.println("===== END : testTimeSlicedFBAHigherIntervalNoQuote =====");
    }

}
