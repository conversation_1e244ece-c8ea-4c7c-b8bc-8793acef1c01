package com.integral.mdf.rate;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.mdf.Util;
import com.integral.mdf.data.*;
import com.integral.mdf.rate.live.MarketPrice;
import com.integral.mdf.rate.live.MarketRate;
import com.integral.mdf.rate.live.MarketRateC;
import com.integral.mdf.rate.live.RateConversionCache;
import com.integral.mdf.rate.provisioning.ProvisioningCalculator;
import com.integral.notifications.MaxCreditInfo;
import com.integral.notifications.StreamUpdate;
import com.integral.provision.LPProvision;
import com.integral.provision.MDFAggregationType;
import org.agrona.concurrent.UnsafeBuffer;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.Assert.*;

public class TestStreamStatus extends PriceAggregationTest {

    public static String EUR_USD = "EUR/USD";
    public static String LP12 = "lp1";
    public static String LP22 = "lp2";
    private long StreamIndex2 = 5001; //value other than 1001 , 1002 and 1003


    @Test
    public void testStreamStatusChangeFBA() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        LPProvision lp1Prov = null;
        for (LPProvision lpProv : fiProvision.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lp1Prov = lpProv;
                break;
            }
        }
        Assert.assertNotNull(lp1Prov);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();
        System.out.println("========book1 aggregate============" +aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.16, aggregate.getBidPrices()[5], 0.01);

        //testing the aggregation
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(6000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(8000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(10000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(12000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.28, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(1.29, aggregate.getOfferPrices()[5], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(8000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(10000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(12000.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumOffers());

        //1. disable stream
        manager.handleStreamStatusChange(1001, StreamUpdate.STREAM_INACTIVE, lp1Prov.getShortName());

        //make sure LP1 is withdrawn
        aggregate = book1.aggregate();
        System.out.println("=======book1 when stream inactive========" +aggregate.toString());

        //make sure only LP2 is aggregated
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertNotEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertNotEquals(4000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(6, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());

    }


    @Test
    public void testStreamStatusChangeFBAInatoAct() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        LPProvision lp1Prov = null;
        for (LPProvision lpProv : fiProvision.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lp1Prov = lpProv;
                break;
            }
        }
        Assert.assertNotNull(lp1Prov);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        //1. disable stream
        manager.handleStreamStatusChange(1001, StreamUpdate.STREAM_INACTIVE, lp1Prov.getShortName());

        //make sure LP1 is withdrawn
        PriceBook aggregate = book1.aggregate();
        System.out.println("=======book1 when stream inactive========" +aggregate.toString());

        //make sure only LP2 is aggregated
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertNotEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertNotEquals(4000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(6, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());

        manager.handleStreamStatusChange(1001, StreamUpdate.STREAM_ACTIVE, lp1Prov.getShortName());

        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.22,1.27,1000,1000);
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        aggregate = book1.aggregate();
        System.out.println("========book1 aggregate after active============" +aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[5], 0.01);
        assertEquals(1.16, aggregate.getBidPrices()[6], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(7000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(9000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(11000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6000.00, aggregate.getBidQtys()[6], 0.01);
        assertEquals(7, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.28, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(1.29, aggregate.getOfferPrices()[5], 0.01);
        assertEquals(1.3, aggregate.getOfferPrices()[6], 0.01);
        assertEquals(1.31, aggregate.getOfferPrices()[7], 0.01);
        assertEquals(1.32, aggregate.getOfferPrices()[8], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(7000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(9000.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[6], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[7], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[8], 0.01);
        assertEquals(9, aggregate.getNumOffers());

    }


    @Test //Review if this is right
    public void testStreamStatusChangeFBATwoFIs() throws Exception {

        System.out.println("=======START:testStreamStatusChangeFBATwoFIs TestCase start==============");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);
        service.provision(TESTFI2);  //// Provision another FI

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        FIProvision fiProvision2 = serverProvision.getFIProvision(1002);
        FIProvisionImpl impl2 = (FIProvisionImpl) fiProvision2;
        impl2.setAggregationType(MDFAggregationType.FULL_BOOK);

//       fiProvision = serverProvision.getFIProvision(1002);
//       impl = (FIProvisionImpl) fiProvision;
//       impl.setAggregationType(MDFAggregationType.FULL_BOOK);
        LPProvision lp1Prov = null;
        for (LPProvision lpProv : fiProvision.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lp1Prov = lpProv;
                break;
            }
        }
        Assert.assertNotNull(lp1Prov);

        LPProvision lp1Prov2 = null;
        for (LPProvision lpProv : fiProvision2.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lp1Prov2 = lpProv;
                break;
            }
        }
        Assert.assertNotNull(lp1Prov2);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        RateDistributionManager manager2 = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        manager2.createRateBooks(fiProvision2);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        Assert.assertEquals(15, manager2.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet2 = manager2.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet2) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision2.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision2, manager2, 1001);

        Assert.assertNotNull(book1);
        Assert.assertNotNull(book2);
        // Assertion to verify same book across Lp's
        //Assert.assertSame("Books not same across Lp's", book1, book2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        //make sure LP1 is withdrawn
        PriceBook aggregate = book1.aggregate();
        System.out.println("=======book1 when stream active========" +aggregate.toString());

        //make sure only LP2 is aggregated
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(6, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());


        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision2,6,1.21,1.24,1000,1000);
        manager2.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision2,6,1.21,1.24,1000,1000);
        manager2.handleRate(quote);

        //make sure LP1 is withdrawn
        PriceBook aggregate2 = book2.aggregate();
        System.out.println("=======book2 when stream active========" +aggregate2.toString());

        //make sure only LP2 is aggregated
        assertEquals(1.21, aggregate2.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate2.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate2.getBidPrices()[1], 0.01);
        assertEquals(2000.00, aggregate2.getBidQtys()[0], 0.01);
        assertEquals(4000.00, aggregate2.getBidQtys()[1], 0.01);
        assertEquals(6, aggregate2.getNumBids());
        assertEquals(6, aggregate2.getNumOffers());

        //1. disable stream
        manager.handleStreamStatusChange(1001, StreamUpdate.STREAM_INACTIVE, lp1Prov.getShortName());
        manager2.handleStreamStatusChange(1001, StreamUpdate.STREAM_INACTIVE, lp1Prov2.getShortName());

        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.22,1.27,1000,1000);
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        aggregate = book1.aggregate();
        System.out.println("========book1 aggregate after Inactive stream1 LP1============" +aggregate.toString());

        //make sure only LP2 is aggregated
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertNotEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(6, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());



        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision2,6,1.22,1.27,1000,1000);
        manager2.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision2,6,1.20,1.24,1000,1000);
        manager2.handleRate(quote);


        aggregate2 = book2.aggregate();
        System.out.println("========book2 aggregate after Inactive============" +aggregate2.toString());

        //make sure only LP2 is aggregated
        assertEquals(1.20, aggregate2.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate2.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate2.getBidPrices()[1], 0.01);
        assertNotEquals(2000.00, aggregate2.getBidQtys()[0], 0.01);
        assertNotEquals(4000.00, aggregate2.getBidQtys()[1], 0.01);
        assertEquals(1000.00, aggregate2.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate2.getBidQtys()[1], 0.01);
        assertEquals(6, aggregate2.getNumBids());
        assertEquals(6, aggregate2.getNumOffers());

    }


    @Test
    public void testStreamStatusLRActStreamIna() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        LPProvision lp1Prov = null;
        for (LPProvision lpProv : fiProvision.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lp1Prov = lpProv;
                break;
            }
        }
        Assert.assertNotNull(lp1Prov);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        //1. disable stream
        manager.handleStreamStatusChange(1001, StreamUpdate.STREAM_INACTIVE, lp1Prov.getShortName());

        //make sure LP1 is withdrawn
        PriceBook aggregate = book1.aggregate();
        System.out.println("=======book1 when stream inactive========" +aggregate.toString());

        //make sure only LP2 is aggregated
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertNotEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertNotEquals(4000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(6, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());

        manager.handleStreamStatusChange(1001, StreamUpdate.STREAM_ACTIVE, lp1Prov.getShortName());

        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.22,1.27,1000,1000);
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        aggregate = book1.aggregate();
        System.out.println("========book1 aggregate after active============" +aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[5], 0.01);
        assertEquals(1.16, aggregate.getBidPrices()[6], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(7000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(9000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(11000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6000.00, aggregate.getBidQtys()[6], 0.01);
        assertEquals(7, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.28, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(1.29, aggregate.getOfferPrices()[5], 0.01);
        assertEquals(1.3, aggregate.getOfferPrices()[6], 0.01);
        assertEquals(1.31, aggregate.getOfferPrices()[7], 0.01);
        assertEquals(1.32, aggregate.getOfferPrices()[8], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(7000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(9000.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[6], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[7], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[8], 0.01);
        assertEquals(9, aggregate.getNumOffers());

    }


    @Test
    public void testStreamStatusLRInaStreamAct() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        LPProvision lp1Prov = null;
        for (LPProvision lpProv : fiProvision.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lp1Prov = lpProv;
                break;
            }
        }
        Assert.assertNotNull(lp1Prov);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);


        System.out.println("=======Quote published================");
        //1. disable stream
        manager.handleLiquidityRulesLPUpdate(fiProvision, 1001, StreamUpdate.STREAM_INACTIVE);

        //make sure LP1 is withdrawn
        PriceBook aggregate = book1.aggregate();
        System.out.println("=======book1 when stream inactive========" +aggregate.toString());

        //make sure only LP2 is aggregated
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertNotEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertNotEquals(4000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(6, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());

        manager.handleLiquidityRulesLPUpdate(fiProvision, 1001, StreamUpdate.STREAM_ACTIVE);

        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.22,1.27,1000,1000);
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        aggregate = book1.aggregate();
        System.out.println("========book1 aggregate after active============" +aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[5], 0.01);
        assertEquals(1.16, aggregate.getBidPrices()[6], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(7000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(9000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(11000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6000.00, aggregate.getBidQtys()[6], 0.01);
        assertEquals(7, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.28, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(1.29, aggregate.getOfferPrices()[5], 0.01);
        assertEquals(1.3, aggregate.getOfferPrices()[6], 0.01);
        assertEquals(1.31, aggregate.getOfferPrices()[7], 0.01);
        assertEquals(1.32, aggregate.getOfferPrices()[8], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(7000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(9000.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[6], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[7], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[8], 0.01);
        assertEquals(9, aggregate.getNumOffers());

    }

    @Test
    public void testStreamStatusChangeDefault() throws Exception {
        //StreamIndex2 is assigned as default, but stream status changed to inactive for stream 1001
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        LPProvision lp1Prov = null;
        for (LPProvision lpProv : fiProvision.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lp1Prov = lpProv;
                break;
            }
                    }
        Assert.assertNotNull(lp1Prov);
        System.out.println("==========LP prov Obj============" +lp1Prov.toString());
        lp1Prov.setStreamName(null);
        lp1Prov.setStreamIndex(StreamIndex2);
        System.out.println("==========LP prov Obj 2============" +lp1Prov.toString());
        System.out.println("==========        lp1Prov.getShortName()============" +lp1Prov.getShortName());

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();
        System.out.println("========book1 aggregate============" +aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.16, aggregate.getBidPrices()[5], 0.01);

        //testing the aggregation
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(6000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(8000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(10000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(12000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.28, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(1.29, aggregate.getOfferPrices()[5], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(8000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(10000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(12000.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumOffers());

        //1. disable stream
        manager.handleStreamStatusChange((int) StreamIndex2, StreamUpdate.STREAM_INACTIVE, lp1Prov.getShortName());

        //make sure LP1 is withdrawn
        aggregate = book1.aggregate();
        System.out.println("=======book1 when stream inactive========" +aggregate.toString());
        //book1 remains same because default stream StreamIndex2 is made inactive, but fi has stream 1001 assigned for its lp

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.28, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(1.29, aggregate.getOfferPrices()[5], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(8000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(10000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(12000.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumOffers());


    }

    //@Test
    public void TestMaxCredit_Agg() throws Exception {
        ProvisioningCalculator calculator;
        double PRICE = 1.12d;
        int PRECISION = 4;
        UnSafeBuffer safeBuf;
        final int MTU = 1400;
        MaxCreditInfo maxCreditInfo = new MaxCreditInfo();
        safeBuf = new UnSafeBuffer();
        byte[] buffer;
        buffer = new byte[MTU];
        safeBuf.init(buffer);
        UnsafeBuffer safeBuf2 = null;
        safeBuf2 = new UnsafeBuffer(buffer);

        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        long fILe = fiProvision.getDefaultLEObjectId();
        impl.setDefaultLEObjId(7453);
        System.out.println("====fILe=========" + fILe);

        //assuming xyz/usd
        int baseccy = 2; //xyz
        int varccy = 1; //usd

        Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        //LPProvision lp = obj.getLP(fiProvision); //replace this with the loop for fetching lp provision
        LPProvision lp = null;
        for (LPProvision lp1 : fiProvision.getLPProvisions()) {
            if (lp1.getShortName().equals("lp1")) {
                lp = lp1;
                break;
            }
        }
        Assert.assertNotNull(lp);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        long lple = lp.getFiClearingMemberLEId();
        System.out.println("====lple=========" + lple);

        //int noOfTiers = 1;
        //QuoteC quote1 = obj.getQuote(CPIndex, noOfTiers);  //default limit is set to 10000

        QuoteC quote1 = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,1,1.11,1.24,1000,1000);
        quote1.stampReceivedTime();
        manager.handleRate(quote1);

        QuoteC quote2 = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision,1,1.21,1.34,1000,1000);
        quote2.stampReceivedTime();
        manager.handleRate(quote2);

        MarketRate mr = getMarketRate(baseccy,varccy,1.5,1.5,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/abc also - to test logic in cache...
        mr = getMarketRate(varccy,3,1.75,1.75,1000,1000);
        RateConversionCache.getInstance().update(mr);

        maxCreditInfo.setMaxCredit(500);
        maxCreditInfo.setValueDate(quote1.getValueDate());
        maxCreditInfo.setFiLE(fILe);
        manager.handleMaxCredit(safeBuf , maxCreditInfo);  // calls on credit
        System.out.println("Max credit info,........ "+ maxCreditInfo.toString());  //Gives blank
        System.out.println("safeBuf info,........ "+ safeBuf.getString());

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote1.buffer());
        calculator.onRate(quote1);//sets the raw rate and raw rate pool objects
        calculator.onCredit(quote1.getValueDate(),(byte) 1,500,(short)1);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first


        System.out.println("=========CreditLimitInfo should give credit details=========  status=1,limitccy=1,limit=2000,vd="+quote1.getValueDate());
        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        provisionedQuote = calculator.getProvisionedRate();  //calls apply credit
        System.out.println("=========calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(333.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(1.24, provisionedQuote.getPPrice(offerTierOffset), 0.01);



        PriceBook aggregate = book1.aggregate();
        System.out.println("=======book1 before credit========" +aggregate.toString());


        buffer = provisionedQuote.buffer().byteArray();
        safeBuf.putByteArray(buffer);
        manager.handleMaxCredit(safeBuf, maxCreditInfo);

//        String buffer1 = provisionedQuote.toString();
//        safeBuf.putString(buffer1); //exception
//        manager.handleMaxCredit(safeBuf, maxCreditInfo);
//          safeBuf2.putStringUtf8(0, buffer1);

          //QuoteC local = rawRatePool.borrowObject();
        //local.readFromArray(quote1.buffer());
        //calculator.rawRate.set(local);


        //provisionedQuote.writeTo(safeBuf2);   quote1.readFromArray(safeBuf2);  //throws exception
        System.out.println("=======buffer before credit========" +safeBuf.getString());
        System.out.println("=======buffer before credit========" +safeBuf2.toString());
        //quote1.readFromArray(safeBuf2);  //index out of bounds exception

        manager.handleRate(quote1);
        System.out.println("=======quote1 before credit========" +quote1.toString());
        manager.handleMaxCredit(safeBuf , maxCreditInfo);  // calls on credit

        aggregate = book1.aggregate();
        System.out.println("========book1 aggregate after credit============" +aggregate.toString());

    }

    MarketRate getMarketRate(int baseidx, int varidx, double bid, double offer,long bidlimit, long offerlimit){

        MarketRate mr = new MarketRateC(1,1);
        mr.setBaseCcyIndex(baseidx);
        mr.setVarCcyIndex(varidx);
        mr.setProviderIndex(1000);//doesn't matter
        mr.setQuoteId(1000l);

        MarketPrice bidmp = mr.getOrCreateBidPrice(0);
        bidmp.setSpotRate(bid);
        bidmp.setRate(bid);
        bidmp.setLimit(bidlimit);
        bidmp.setTotalLimit(bidlimit);
        mr.addBidPrice(bidmp);
        mr.setNoOfBidTiers(1);

        MarketPrice offermp = mr.getOrCreateOfferPrice(0);
        offermp.setSpotRate(offer);
        offermp.setRate(offer);
        offermp.setLimit(offerlimit);
        offermp.setTotalLimit(offerlimit);
        mr.addOfferPrice(offermp);
        mr.setNoOfOfferTiers(1);


        return mr;
    }


}
