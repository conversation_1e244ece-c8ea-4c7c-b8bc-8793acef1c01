package com.integral.mdf.rate;

import com.integral.mdf.Util;
import com.integral.mdf.data.*;
import com.integral.provision.CurrencyPairProvision;
import com.integral.provision.MDFAggregationType;
import com.integral.virtualserver.MDFEntity;
import org.junit.Assert;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.TestRule;
import org.junit.rules.TestWatcher;
import org.junit.runner.Description;

import java.util.*;

import static org.junit.Assert.*;

public class TestOnDemandAggregation extends PriceAggregationTest {

    public static final String LP22 = "lp2";
    public static final String LP12 = "lp1";
    public static final String EUR_USD = "EUR/USD";


    @Rule
    public TestRule watcher = new TestWatcher() {
        protected void starting(Description description) {
            System.out.println("Starting test: " + description.getMethodName());
        }

        protected void finished(Description description) {
            System.out.println("Finished test: " + description.getMethodName());
        }
    };

    protected void set3TierRates(QuoteC quote, byte buyOrSell, double p1, double p2, double p3) {
        //tier 1
        int tieridx = quote.tierOffset(buyOrSell, 0);
        quote.setPrice(tieridx, p1);
        //tier 2
        tieridx = quote.tierOffset(buyOrSell, 1);
        quote.setPrice(tieridx, p2);
        //tier 3
        tieridx = quote.tierOffset(buyOrSell, 2);
        quote.setPrice(tieridx, p3);
    }

    protected void set2TierRates(QuoteC quote, byte buyOrSell, double p1, double p2) {
        //tier 1
        int tieridx = quote.tierOffset(buyOrSell, 0);
        quote.setPrice(tieridx, p1);
        //tier 2
        tieridx = quote.tierOffset(buyOrSell, 1);
        quote.setPrice(tieridx, p2);
    }

    /**
     * MultiQuote-MultiQuote VWAP aggregation And BestPrice aggregation .
     * test simulates subscription for VWAP and BestPrice.
     *
     * @throws Exception
     */
    @Test
    public void testMultipleBook_VWAPBPAAggregation() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMultipleBook_VWAPBPAAggregation  =====");
        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        int EUR = Util.getBaseCurrencyIndex(commonCcyPairIdx);
        int USD = Util.getVarCurrencyIndex(commonCcyPairIdx);
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000, 2000, 3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook vwapbook = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(vwapbook);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", vwapbook, book2);

        //Add another book
        ((FIProvisionImpl) fiProvision).setAggregationType(MDFAggregationType.BEST_PRICE);
        manager.createRateBook(fiProvision, commonCcyPairIdx);

        Optional<RateBook> Obpbook = manager.getRateBook(fiProvision.getIndex(), EUR, USD, MDFAggregationType.BEST_PRICE.getIndex());

        assertTrue(Obpbook.isPresent());

        RateBook bpbook = Obpbook.get();

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        assertNotEquals(vwapbook, bpbook);


        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(vwapbook.getPricebook().getTiers());
        assertEquals(0, vwapbook.getPricebook().getTiers().length);

        //add tiers now
        vwapbook.getPricebook().addTier(1000);
        vwapbook.getPricebook().addTier(2000);
        vwapbook.getPricebook().addTier(3000);

        //simulate rate updates from LP! and LP@
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2120, 1.2119);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2123, 1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2119, 1.2118);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2125, 1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote===" + quote.toString());

        //Simulate aggregation
        PriceBook vwapaggregate = vwapbook.aggregate();
        System.out.println("=====#1 vwapaggregate quote in testVWAPBPAAggregation8jun  ===" + vwapaggregate.toString());
        PriceBook bpaggregate = bpbook.aggregate();

        //assert VWAP===========================
        //assert For bid price
        assertEquals(1.2121, vwapaggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21206, vwapaggregate.getBidPrices()[1], 0.00001);
        //  assertEquals(1.21207, vwapaggregate.getBidPrices()[2], 0.001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, vwapaggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, vwapaggregate.getBidQtys()[1], 0.01);
        assertEquals(2, vwapaggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, vwapaggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.21224, vwapaggregate.getOfferPrices()[1], 0.0001);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, vwapaggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, vwapaggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, vwapaggregate.getNumOffers());


        //assert BestPrice
        assertEquals(1.2121, bpaggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, bpaggregate.getBidQtys()[0], 0.01);
        assertEquals(1, bpaggregate.getNumBids());
        assertEquals(1.2122, bpaggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, bpaggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, bpaggregate.getNumOffers());


    }


    @Test
    public void testRateBookUnsubscription() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testRateBookUnsubscription  =====");


        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        int EUR = Util.getBaseCurrencyIndex(commonCcyPairIdx);
        int USD = Util.getVarCurrencyIndex(commonCcyPairIdx);
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000, 2000, 3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook vwapbook = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(vwapbook);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", vwapbook, book2);

        //Add another book
        ((FIProvisionImpl) fiProvision).setAggregationType(MDFAggregationType.BEST_PRICE);
        manager.createRateBook(fiProvision, commonCcyPairIdx);


        Optional<RateBook> Obpbook = manager.getRateBook(fiProvision.getIndex(), EUR, USD, MDFAggregationType.BEST_PRICE.getIndex());

        assertTrue(Obpbook.isPresent());

        RateBook bpbook = Obpbook.get();

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        assertNotEquals(vwapbook, bpbook);


        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(vwapbook.getPricebook().getTiers());
        assertEquals(0, vwapbook.getPricebook().getTiers().length);

        //add tiers now
        vwapbook.getPricebook().addTier(1000);
        vwapbook.getPricebook().addTier(2000);
        vwapbook.getPricebook().addTier(3000);

        //simulate rate updates from LP! and LP@
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2120, 1.2119);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2123, 1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2119, 1.2118);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2125, 1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote===" + quote.toString());

        //Simulate aggregation
        PriceBook vwapaggregate = vwapbook.aggregate();
        System.out.println("=====#1 vwapaggregate quote in testVWAPBPAAggregation8jun  ===" + vwapaggregate.toString());
        PriceBook bpaggregate = bpbook.aggregate();

        //assert VWAP===========================
        //assert For bid price
        assertEquals(1.2121, vwapaggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21206, vwapaggregate.getBidPrices()[1], 0.00001);
        //  assertEquals(1.21207, vwapaggregate.getBidPrices()[2], 0.001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, vwapaggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, vwapaggregate.getBidQtys()[1], 0.01);
        assertEquals(2, vwapaggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, vwapaggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.21224, vwapaggregate.getOfferPrices()[1], 0.0001);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, vwapaggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, vwapaggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, vwapaggregate.getNumOffers());


        //assert BestPrice
        assertEquals(1.2121, bpaggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, bpaggregate.getBidQtys()[0], 0.01);
        assertEquals(1, bpaggregate.getNumBids());
        assertEquals(1.2122, bpaggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, bpaggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, bpaggregate.getNumOffers());


        //Now remove bast pricebook
        manager.deactivateRateBook(fiProvision, commonCcyPairIdx, MDFAggregationType.BEST_PRICE);

        assertFalse(bpbook.isActive());

        //this update should not goto book
        manager.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        bpaggregate = bpbook.aggregate();

        assertNotNull(bpaggregate);

        assertEquals(0.0, bpaggregate.getBidPrices()[0], 0.001);
        assertEquals(0.0, bpaggregate.getBidQtys()[0], 0.01);
        assertEquals(1, bpaggregate.getNumBids());
        assertEquals(0.0, bpaggregate.getOfferPrices()[0], 0.01);
        assertEquals(0.0, bpaggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, bpaggregate.getNumOffers());
    }

    /**
     * Adding three books
     */
    @Test
    public void testMultipleBook_BPAVWAPFBAAggregation() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMultipleBook_BPAVWAPFBAAggregation  =====");
        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        int EUR = Util.getBaseCurrencyIndex(commonCcyPairIdx);
        int USD = Util.getVarCurrencyIndex(commonCcyPairIdx);
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook BPAbook = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(BPAbook);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", BPAbook, book2);

        System.out.println("=====Book2: Add another book===");
        impl.setAggregationTiers(new double[]{1000, 2000, 3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        //Add another book
        ((FIProvisionImpl) fiProvision).setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
        manager.createRateBook(fiProvision, commonCcyPairIdx);

        Optional<RateBook> VWAPbook = manager.getRateBook(fiProvision.getIndex(), EUR, USD, MDFAggregationType.WEIGHTED_AVERAGE.getIndex());
        assertTrue(VWAPbook.isPresent());

        RateBook VWAPbook1 = VWAPbook.get();
        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        assertNotEquals(BPAbook, VWAPbook);


        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(VWAPbook1.getPricebook().getTiers());
        assertEquals(0, VWAPbook1.getPricebook().getTiers().length);

        //add tiers now
        VWAPbook1.getPricebook().addTier(1000);
        VWAPbook1.getPricebook().addTier(2000);
        VWAPbook1.getPricebook().addTier(3000);

        //simulate rate updates from LP! and LP@
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2120, 1.2119);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2123, 1.2124);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2119, 1.2118);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2125, 1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote===" + quote.toString());

        //Simulate aggregation
        PriceBook bpaggregate = BPAbook.aggregate();
        System.out.println("=====#1 bpaggregate quote in testMultipleBook_BPAVWAPFBAAggregation  ===" + bpaggregate.toString());
        PriceBook vwapaggregate = VWAPbook1.aggregate();
        System.out.println("=====#2 vwapaggregate quote in testMultipleBook_BPAVWAPFBAAggregation  ===" + vwapaggregate.toString());

        //assert VWAP===========================
        //assert For bid price
        assertEquals(1.2121, vwapaggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21206, vwapaggregate.getBidPrices()[1], 0.00001);
        //  assertEquals(1.21207, vwapaggregate.getBidPrices()[2], 0.001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, vwapaggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, vwapaggregate.getBidQtys()[1], 0.01);
        assertEquals(2, vwapaggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, vwapaggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.21224, vwapaggregate.getOfferPrices()[1], 0.0001);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, vwapaggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, vwapaggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, vwapaggregate.getNumOffers());

        //assert BestPrice
        assertEquals(1.2121, bpaggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, bpaggregate.getBidQtys()[0], 0.01);
        assertEquals(1, bpaggregate.getNumBids());
        assertEquals(1.2122, bpaggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, bpaggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, bpaggregate.getNumOffers());


        System.out.println("=====Book3: Add another book===");
        //Add another book
        ((FIProvisionImpl) fiProvision).setAggregationType(MDFAggregationType.FULL_BOOK);
        manager.createRateBook(fiProvision, commonCcyPairIdx);

        Optional<RateBook> Obpbook = manager.getRateBook(fiProvision.getIndex(), EUR, USD, MDFAggregationType.FULL_BOOK.getIndex());
        assertTrue(Obpbook.isPresent());
        RateBook FBAbook = Obpbook.get();
        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }
        assertNotEquals(BPAbook, FBAbook);

        //simulate rate updates from LP! and LP@
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote===" + quote.toString());

        //Simulate aggregation
        PriceBook FBAaggregate = FBAbook.aggregate();
        System.out.println("=====#3 FBAaggregate quote in testMultipleBook_BPAVWAPAggregation  ===" + FBAaggregate.toString());

        assertEquals(1.21, FBAaggregate.getBidPrices()[0], 0.001);
        assertEquals(1.2, FBAaggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.19, FBAaggregate.getBidPrices()[2], 0.001);

        assertEquals(2000.00, FBAaggregate.getBidQtys()[0], 0.01);
        assertEquals(4000.00, FBAaggregate.getBidQtys()[1], 0.01);
        assertEquals(6000.00, FBAaggregate.getBidQtys()[2], 0.01);
        assertEquals(3, FBAaggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, FBAaggregate.getOfferPrices()[0], 0.000001);
        assertEquals(1.23, FBAaggregate.getOfferPrices()[1], 0.0001);
        assertEquals(1.24, FBAaggregate.getOfferPrices()[2], 0.0001);
        assertEquals(1.25, FBAaggregate.getOfferPrices()[3], 0.0001);
        assertEquals(1.26, FBAaggregate.getOfferPrices()[4], 0.0001);

        assertEquals(1000.00, FBAaggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, FBAaggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000.00, FBAaggregate.getOfferQtys()[2], 0.01);
        assertEquals(2000.00, FBAaggregate.getOfferQtys()[3], 0.01);
        assertEquals(3000.00, FBAaggregate.getOfferQtys()[4], 0.01);
        assertEquals(5, FBAaggregate.getNumOffers());


    }

    /***
     * Add BPA, then add FBA, then remove FBA, and republish quote so same bid and offer should aggregate for BPA
     */
    @Test
    public void testMultipleBook_BPAFBAremoveFBA() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMultipleBook_BPAFBAremoveFBA  =====");
        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        int EUR = Util.getBaseCurrencyIndex(commonCcyPairIdx);
        int USD = Util.getVarCurrencyIndex(commonCcyPairIdx);
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook BPAbook = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(BPAbook);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", BPAbook, book2);


        //simulate rate updates from LP! and LP@
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2120, 1.2119);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2123, 1.2124);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2119, 1.2118);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2125, 1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote===" + quote.toString());


        //Simulate aggregation
        PriceBook bpaggregate = BPAbook.aggregate();
        System.out.println("=====#1 bpaggregate quote in testMultipleBook_BPAFBAremoveFBA  ===" + bpaggregate.toString());

        //assert BestPrice
        assertEquals(1.2121, bpaggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, bpaggregate.getBidQtys()[0], 0.01);
        assertEquals(1, bpaggregate.getNumBids());
        assertEquals(1.2122, bpaggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, bpaggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, bpaggregate.getNumOffers());


        System.out.println("=====Book2 Add another book FBA===");
        //Add another book
        ((FIProvisionImpl) fiProvision).setAggregationType(MDFAggregationType.FULL_BOOK);
        manager.createRateBook(fiProvision, commonCcyPairIdx);

        Optional<RateBook> Obpbook = manager.getRateBook(fiProvision.getIndex(), EUR, USD, MDFAggregationType.FULL_BOOK.getIndex());
        assertTrue(Obpbook.isPresent());
        RateBook FBAbook = Obpbook.get();
        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }
        assertNotEquals(BPAbook, FBAbook);

        //simulate rate updates from LP! and LP@
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote===" + quote.toString());

        //Simulate aggregation
        PriceBook FBAaggregate = FBAbook.aggregate();
        System.out.println("=====#2 FBAaggregate quote in testMultipleBook_BPAFBAremoveFBA  ===" + FBAaggregate.toString());

        assertEquals(1.21, FBAaggregate.getBidPrices()[0], 0.001);
        assertEquals(1.2, FBAaggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.19, FBAaggregate.getBidPrices()[2], 0.001);

        assertEquals(2000.00, FBAaggregate.getBidQtys()[0], 0.01);
        assertEquals(4000.00, FBAaggregate.getBidQtys()[1], 0.01);
        assertEquals(6000.00, FBAaggregate.getBidQtys()[2], 0.01);
        assertEquals(3, FBAaggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, FBAaggregate.getOfferPrices()[0], 0.000001);
        assertEquals(1.23, FBAaggregate.getOfferPrices()[1], 0.0001);
        assertEquals(1.24, FBAaggregate.getOfferPrices()[2], 0.0001);
        assertEquals(1.25, FBAaggregate.getOfferPrices()[3], 0.0001);
        assertEquals(1.26, FBAaggregate.getOfferPrices()[4], 0.0001);

        assertEquals(1000.00, FBAaggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, FBAaggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000.00, FBAaggregate.getOfferQtys()[2], 0.01);
        assertEquals(2000.00, FBAaggregate.getOfferQtys()[3], 0.01);
        assertEquals(3000.00, FBAaggregate.getOfferQtys()[4], 0.01);
        assertEquals(5, FBAaggregate.getNumOffers());

        //remove FBA, BPA should exist
        //Now remove bast pricebook
        manager.deactivateRateBook(fiProvision, commonCcyPairIdx, MDFAggregationType.FULL_BOOK);
        assertFalse(FBAbook.isActive());

        //this update should not goto book
        manager.handleRate(quote);
        System.out.println("=====deactivate quote===" + quote.toString());
        FBAaggregate = FBAbook.aggregate();
        assertNotNull(FBAaggregate);

        assertEquals(0.0, FBAaggregate.getBidPrices()[0], 0.001);
        assertEquals(0.0, FBAaggregate.getBidQtys()[0], 0.01);
        assertEquals(1, FBAaggregate.getNumBids());
        assertEquals(0.0, FBAaggregate.getOfferPrices()[0], 0.01);
        assertEquals(0.0, FBAaggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, FBAaggregate.getNumOffers());

        //assert same bid and offer
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        bpaggregate = BPAbook.aggregate();
        System.out.println("=====#3 bpaggregate quote in testMultipleBook_BPAFBAremoveFBA  ===" + bpaggregate.toString());

        //assert BestPrice
        assertEquals(1.22, bpaggregate.getBidPrices()[0], 0.00001);
        assertEquals(1000.00, bpaggregate.getBidQtys()[0], 0.0001);
        assertEquals(1, bpaggregate.getNumBids());
        assertEquals(1.22, bpaggregate.getOfferPrices()[0], 0.000001);
        assertEquals(1000.00, bpaggregate.getOfferQtys()[0], 0.000001);
        assertEquals(1, bpaggregate.getNumOffers());

    }

    /**
     * Add two books and unsubscribe from all
     * Publish rate and see the quote is not picked after deactivation*
     */
    @Test
    public void testRateBookUnsubscriptionAll() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testRateBookUnsubscriptionAll  =====");
        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        int EUR = Util.getBaseCurrencyIndex(commonCcyPairIdx);
        int USD = Util.getVarCurrencyIndex(commonCcyPairIdx);
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000, 2000, 3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook vwapbook = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(vwapbook);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", vwapbook, book2);

        //Add another book
        ((FIProvisionImpl) fiProvision).setAggregationType(MDFAggregationType.BEST_PRICE);
        manager.createRateBook(fiProvision, commonCcyPairIdx);

        Optional<RateBook> Obpbook = manager.getRateBook(fiProvision.getIndex(), EUR, USD, MDFAggregationType.BEST_PRICE.getIndex());
        assertTrue(Obpbook.isPresent());
        RateBook bpbook = Obpbook.get();

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }
        assertNotEquals(vwapbook, bpbook);


        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(vwapbook.getPricebook().getTiers());
        assertEquals(0, vwapbook.getPricebook().getTiers().length);

        //add tiers now
        vwapbook.getPricebook().addTier(1000);
        vwapbook.getPricebook().addTier(2000);
        vwapbook.getPricebook().addTier(3000);

        //simulate rate updates from LP! and LP@
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2120, 1.2119);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2123, 1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2119, 1.2118);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2125, 1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote===" + quote.toString());

        //Simulate aggregation
        PriceBook vwapaggregate = vwapbook.aggregate();
        System.out.println("=====#1 vwapaggregate quote in testRateBookUnsubscriptionAll  ===" + vwapaggregate.toString());
        PriceBook bpaggregate = bpbook.aggregate();

        //assert VWAP===========================
        //assert For bid price
        assertEquals(1.2121, vwapaggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21206, vwapaggregate.getBidPrices()[1], 0.00001);
        //  assertEquals(1.21207, vwapaggregate.getBidPrices()[2], 0.001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, vwapaggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, vwapaggregate.getBidQtys()[1], 0.01);
        assertEquals(2, vwapaggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, vwapaggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.21224, vwapaggregate.getOfferPrices()[1], 0.0001);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, vwapaggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, vwapaggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, vwapaggregate.getNumOffers());


        //assert BestPrice
        assertEquals(1.2121, bpaggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, bpaggregate.getBidQtys()[0], 0.01);
        assertEquals(1, bpaggregate.getNumBids());
        assertEquals(1.2122, bpaggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, bpaggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, bpaggregate.getNumOffers());

        //Now remove best pricebook
        manager.deactivateRateBook(fiProvision, commonCcyPairIdx, MDFAggregationType.BEST_PRICE);
        assertFalse(bpbook.isActive());

        //this update should not goto book
        manager.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());
        bpaggregate = bpbook.aggregate();
        assertNotNull(bpaggregate);

        assertEquals(0.0, bpaggregate.getBidPrices()[0], 0.001);
        assertEquals(0.0, bpaggregate.getBidQtys()[0], 0.01);
        assertEquals(1, bpaggregate.getNumBids());
        assertEquals(0.0, bpaggregate.getOfferPrices()[0], 0.01);
        assertEquals(0.0, bpaggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, bpaggregate.getNumOffers());


        //Now remove WEIGHTED_AVERAGE pricebook
        manager.deactivateRateBook(fiProvision, commonCcyPairIdx, MDFAggregationType.WEIGHTED_AVERAGE);
        assertFalse(vwapbook.isActive());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.25, 1.26);
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2119, 1.2118);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2125, 1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        //this update should not goto book
        manager.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());
        vwapaggregate = vwapbook.aggregate();
        assertNotNull(vwapaggregate);

        assertEquals(0.0, vwapaggregate.getBidPrices()[0], 0.001);
        assertEquals(0.0, vwapaggregate.getBidQtys()[0], 0.01);
        assertEquals(1, vwapaggregate.getNumBids());
        assertEquals(0.0, vwapaggregate.getOfferPrices()[0], 0.01);
        assertEquals(0.0, vwapaggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, vwapaggregate.getNumOffers());

        //agg again
        vwapaggregate = vwapbook.aggregate();


    }


    /**
     * Inverted rates from one LP, so that rate gets dropped, a
     * Two books should be aggregated from the other LP rate
     *
     * @throws Exception
     */
    @Test
    public void testMultipleBook_VWAPBPAInverted() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMultipleBook_VWAPBPAInverted  =====");
        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        int EUR = Util.getBaseCurrencyIndex(commonCcyPairIdx);
        int USD = Util.getVarCurrencyIndex(commonCcyPairIdx);
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000, 2000, 3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook vwapbook = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(vwapbook);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", vwapbook, book2);

        //Add another book
        ((FIProvisionImpl) fiProvision).setAggregationType(MDFAggregationType.BEST_PRICE);
        manager.createRateBook(fiProvision, commonCcyPairIdx);

        Optional<RateBook> Obpbook = manager.getRateBook(fiProvision.getIndex(), EUR, USD, MDFAggregationType.BEST_PRICE.getIndex());
        assertTrue(Obpbook.isPresent());
        RateBook bpbook = Obpbook.get();

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        assertNotEquals(vwapbook, bpbook);


        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(vwapbook.getPricebook().getTiers());
        assertEquals(0, vwapbook.getPricebook().getTiers().length);

        //add tiers now
        vwapbook.getPricebook().addTier(1000);
        vwapbook.getPricebook().addTier(2000);
        vwapbook.getPricebook().addTier(3000);

        //simulate rate updates from LP! and LP@
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, 1.24, 2000, 2000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        sleep(1);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.19, 1.20, 2000, 2000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote===" + quote.toString());

        //Simulate aggregation
        PriceBook vwapaggregate = vwapbook.aggregate();
        System.out.println("=====#1 vwapaggregate quote in testMultipleBook_VWAPBPAInverted  ===" + vwapaggregate.toString());
        PriceBook bpaggregate = bpbook.aggregate();
        System.out.println("=====#2 bpaggregate quote in testMultipleBook_VWAPBPAInverted  ===" + bpaggregate.toString());

        //LP1 quote is dropped, and LP2 quote is aggregated in Vwap
        assertEquals(1.19, vwapaggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.18666, vwapaggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, vwapaggregate.getBidQtys()[0], 0.00001);
        assertEquals(3000.00, vwapaggregate.getBidQtys()[1], 0.00001);
        assertEquals(2, vwapaggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2, vwapaggregate.getOfferPrices()[0], 0.00001);
        //Offer is doing round down instead of round up, hence this assert fails
        assertEquals(1.20334, vwapaggregate.getOfferPrices()[1], 0.00001);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, vwapaggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, vwapaggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, vwapaggregate.getNumOffers());

        //LP1 quote is dropped, and LP2 quote is aggregated
        //assert BestPrice
        assertEquals(1.19, bpaggregate.getBidPrices()[0], 0.00001);
        assertEquals(2000.00, bpaggregate.getBidQtys()[0], 0.00001);
        assertEquals(1, bpaggregate.getNumBids());
        assertEquals(1.2, bpaggregate.getOfferPrices()[0], 0.00001);
        assertEquals(2000.00, bpaggregate.getOfferQtys()[0], 0.00001);
        assertEquals(1, bpaggregate.getNumOffers());


    }


    /**
     * Check quotes, second agg is picking up second and third tier quotes from previous quotes
     * Inverted book, so aggregation does not happen for the second book,
     * first agg book rate should get aggregated with new rate after that
     * fails - bug PLT-3568
     */
    @Test
    public void testMultipleBook_BPAFBAGivingInverted() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMultipleBook_BPAFBAGivingInverted  =====");
        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        int EUR = Util.getBaseCurrencyIndex(commonCcyPairIdx);
        int USD = Util.getVarCurrencyIndex(commonCcyPairIdx);
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook BPAbook = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(BPAbook);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", BPAbook, book2);

        //simulate rate updates from LP! and LP@
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2120, 1.2119);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2123, 1.2124);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2119, 1.2118);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2125, 1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote===" + quote.toString());


        //Simulate aggregation
        PriceBook bpaggregate = BPAbook.aggregate();
        System.out.println("=====#1 bpaggregate quote in testMultipleBook_BPAFBAGivingInverted  ===" + bpaggregate.toString());

        //assert BestPrice
        assertEquals(1.2121, bpaggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, bpaggregate.getBidQtys()[0], 0.01);
        assertEquals(1, bpaggregate.getNumBids());
        assertEquals(1.2122, bpaggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, bpaggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, bpaggregate.getNumOffers());

        System.out.println("=====Book2 Add another book FBA===");
        //Add another book
        ((FIProvisionImpl) fiProvision).setAggregationType(MDFAggregationType.FULL_BOOK);
        manager.createRateBook(fiProvision, commonCcyPairIdx);

        Optional<RateBook> Obpbook = manager.getRateBook(fiProvision.getIndex(), EUR, USD, MDFAggregationType.FULL_BOOK.getIndex());
        assertTrue(Obpbook.isPresent());
        RateBook FBAbook = Obpbook.get();
        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }
        assertNotEquals(BPAbook, FBAbook);

        //simulate rate updates from LP! and LP@
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote===" + quote.toString());

        //Simulate aggregation
        PriceBook FBAaggregate = FBAbook.aggregate();
        System.out.println("=====#2 FBAaggregate quote in testMultipleBook_BPAFBAremoveFBA  ===" + FBAaggregate.toString());

        assertEquals(1.21, FBAaggregate.getBidPrices()[0], 0.001);
        assertEquals(1.2, FBAaggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.19, FBAaggregate.getBidPrices()[2], 0.001);

        assertEquals(2000.00, FBAaggregate.getBidQtys()[0], 0.01);
        assertEquals(4000.00, FBAaggregate.getBidQtys()[1], 0.01);
        assertEquals(6000.00, FBAaggregate.getBidQtys()[2], 0.01);
        assertEquals(3, FBAaggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, FBAaggregate.getOfferPrices()[0], 0.000001);
        assertEquals(1.23, FBAaggregate.getOfferPrices()[1], 0.0001);
        assertEquals(1.24, FBAaggregate.getOfferPrices()[2], 0.0001);
        assertEquals(1.25, FBAaggregate.getOfferPrices()[3], 0.0001);
        assertEquals(1.26, FBAaggregate.getOfferPrices()[4], 0.0001);

        assertEquals(1000.00, FBAaggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, FBAaggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000.00, FBAaggregate.getOfferQtys()[2], 0.01);
        assertEquals(2000.00, FBAaggregate.getOfferQtys()[3], 0.01);
        assertEquals(3000.00, FBAaggregate.getOfferQtys()[4], 0.01);
        assertEquals(5, FBAaggregate.getNumOffers());

        //Inverted quote dropped
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.24, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====Inverted quote LP1===" + quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.23, 1.20);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====Inverted quote LP2===" + quote.toString());

        PriceBook aggregate = FBAbook.aggregate();
        System.out.println("=====3: Inverted aggregate quote in testVWAPFromSQLPGivingInvertedTiers ===" + aggregate.toString());

        //assert For bid price
        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
       // assertEquals(0.0, aggregate.getBidPrices()[1], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        //BPA book should get updated with new quote
        //assert same bid and offer
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        bpaggregate = BPAbook.aggregate();
        System.out.println("=====#4 bpaggregate quote in testMultipleBook_BPAFBAremoveFBA  ===" + bpaggregate.toString());

        //assert BestPrice
        assertEquals(1.22, bpaggregate.getBidPrices()[0], 0.00001);
        assertEquals(1000.00, bpaggregate.getBidQtys()[0], 0.0001);
        assertEquals(1, bpaggregate.getNumBids());
        assertEquals(1.24, bpaggregate.getOfferPrices()[0], 0.000001);
        assertEquals(1000.00, bpaggregate.getOfferQtys()[0], 0.000001);
        assertEquals(1, bpaggregate.getNumOffers());

    }


    /***
     * Add RBA, then add MTFOK, then remove RBA, and republish quote so same bid and offer should aggregate for BPA
     */
    @Test
    public void testMultipleBook_RBAremoveMTFOK() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMultipleBook_RBAremoveMTFOK  =====");
        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        int EUR = Util.getBaseCurrencyIndex(commonCcyPairIdx);
        int USD = Util.getVarCurrencyIndex(commonCcyPairIdx);
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook RBABook = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(RBABook);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", RBABook, book2);


        //simulate rate updates from LP! and LP@
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2120, 1.2119);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2123, 1.2124);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2119, 1.2118);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2125, 1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote===" + quote.toString());


        //Simulate aggregation
        PriceBook rbaaggregate = RBABook.aggregate();
        System.out.println("=====#1 rbaaggregate quote in testMultipleBook_BPAFBAremoveFBA  ===" + rbaaggregate.toString());

        //assert BestPrice
        assertEquals(1.2121, rbaaggregate.getBidPrices()[0], 0.000001);
        assertEquals(1.2121, rbaaggregate.getBidPrices()[1], 0.000001);
        assertEquals(1.212, rbaaggregate.getBidPrices()[2], 0.000001);
        assertEquals(1.2119, rbaaggregate.getBidPrices()[3], 0.000001);
        assertEquals(1.2119, rbaaggregate.getBidPrices()[4], 0.000001);
        assertEquals(1.2118, rbaaggregate.getBidPrices()[5], 0.000001);
        assertEquals(1000.00, rbaaggregate.getBidQtys()[0], 0.000001);
        assertEquals(1000.00, rbaaggregate.getBidQtys()[1], 0.000001);
        assertEquals(2000.00, rbaaggregate.getBidQtys()[2], 0.000001);
        assertEquals(3000.00, rbaaggregate.getBidQtys()[3], 0.000001);
        assertEquals(2000.00, rbaaggregate.getBidQtys()[4], 0.000001);
        assertEquals(3000.00, rbaaggregate.getBidQtys()[5], 0.000001);
        assertEquals(6, rbaaggregate.getNumBids());
        assertEquals(1.2122, rbaaggregate.getOfferPrices()[0], 0.000001);
        assertEquals(1.2122, rbaaggregate.getOfferPrices()[1], 0.000001);
        assertEquals(1.2123, rbaaggregate.getOfferPrices()[2], 0.000001);
        assertEquals(1.2124, rbaaggregate.getOfferPrices()[3], 0.000001);
        assertEquals(1.2125, rbaaggregate.getOfferPrices()[4], 0.000001);
        assertEquals(1.2127, rbaaggregate.getOfferPrices()[5], 0.000001);
        assertEquals(1000.00, rbaaggregate.getOfferQtys()[0], 0.000001);
        assertEquals(1000.00, rbaaggregate.getOfferQtys()[1], 0.000001);
        assertEquals(2000.00, rbaaggregate.getOfferQtys()[2], 0.000001);
        assertEquals(3000.00, rbaaggregate.getOfferQtys()[3], 0.000001);
        assertEquals(2000.00, rbaaggregate.getOfferQtys()[4], 0.000001);
        assertEquals(3000.00, rbaaggregate.getOfferQtys()[5], 0.000001);
        assertEquals(6, rbaaggregate.getNumOffers());


        System.out.println("=====Book2 Add another book MTFOK===");
        //Add another book
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        ((FIProvisionImpl) fiProvision).setAggregationType(MDFAggregationType.MULTI_TIER_FOK);
        manager.createRateBook(fiProvision, commonCcyPairIdx);

        Optional<RateBook> Obpbook = manager.getRateBook(fiProvision.getIndex(), EUR, USD, MDFAggregationType.MULTI_TIER_FOK.getIndex());
        assertTrue(Obpbook.isPresent());
        RateBook MTFOKBook = Obpbook.get();
        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }
        assertNotEquals(RBABook, MTFOKBook);

        //simulate rate updates from LP! and LP@
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote===" + quote.toString());

        //Simulate aggregation
        PriceBook MTFOKAggregate = MTFOKBook.aggregate();
        System.out.println("=====#2 MTFOKAggregate quote in testMultipleBook_BPAFBAremoveFBA  ===" + MTFOKAggregate.toString());

        assertEquals(1.21, MTFOKAggregate.getBidPrices()[0], 0.001);
        assertEquals(1.2, MTFOKAggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.19, MTFOKAggregate.getBidPrices()[2], 0.001);

        assertEquals(1000.00, MTFOKAggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, MTFOKAggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, MTFOKAggregate.getBidQtys()[2], 0.01);
        assertEquals(3, MTFOKAggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, MTFOKAggregate.getOfferPrices()[0], 0.000001);
        assertEquals(1.23, MTFOKAggregate.getOfferPrices()[1], 0.0001);
        assertEquals(1.24, MTFOKAggregate.getOfferPrices()[2], 0.0001);
        assertEquals(0.0, MTFOKAggregate.getOfferPrices()[3], 0.0001);
        assertEquals(0.0, MTFOKAggregate.getOfferPrices()[4], 0.0001);

        assertEquals(1000.00, MTFOKAggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, MTFOKAggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, MTFOKAggregate.getOfferQtys()[2], 0.01);
        assertEquals(0.0, MTFOKAggregate.getOfferQtys()[3], 0.01);
        assertEquals(0.0, MTFOKAggregate.getOfferQtys()[4], 0.01);
        assertEquals(3, MTFOKAggregate.getNumOffers());

        //remove FBA, BPA should exist
        manager.deactivateRateBook(fiProvision, commonCcyPairIdx, MDFAggregationType.MULTI_TIER_FOK);
        assertFalse(MTFOKBook.isActive());

        //this update should not goto book
        manager.handleRate(quote);
        System.out.println("=====deactivate quote===" + quote.toString());
        MTFOKAggregate = MTFOKBook.aggregate();
        assertNotNull(MTFOKAggregate);

        assertEquals(0.0, MTFOKAggregate.getBidPrices()[0], 0.001);
        assertEquals(0.0, MTFOKAggregate.getBidQtys()[0], 0.01);
        assertEquals(1, MTFOKAggregate.getNumBids());
        assertEquals(0.0, MTFOKAggregate.getOfferPrices()[0], 0.01);
        assertEquals(0.0, MTFOKAggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, MTFOKAggregate.getNumOffers());

        //assert same bid and offer
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        rbaaggregate = RBABook.aggregate();
        System.out.println("=====#3 rbaaggregate quote in testMultipleBook_BPAFBAremoveFBA  ===" + rbaaggregate.toString());

        //assert BestPrice
        assertEquals(1.22, rbaaggregate.getBidPrices()[0], 0.000001);
        assertEquals(1.21, rbaaggregate.getBidPrices()[1], 0.000001);
        assertEquals(1.21, rbaaggregate.getBidPrices()[2], 0.000001);
        assertEquals(1.2, rbaaggregate.getBidPrices()[3], 0.000001);
        assertEquals(1.2, rbaaggregate.getBidPrices()[4], 0.000001);
        assertEquals(1.19, rbaaggregate.getBidPrices()[5], 0.000001);
        assertEquals(1000.00, rbaaggregate.getBidQtys()[0], 0.000001);
        assertEquals(2000.00, rbaaggregate.getBidQtys()[1], 0.000001);
        assertEquals(1000.00, rbaaggregate.getBidQtys()[2], 0.000001);
        assertEquals(3000.00, rbaaggregate.getBidQtys()[3], 0.000001);
        assertEquals(2000.00, rbaaggregate.getBidQtys()[4], 0.000001);
        assertEquals(3000.00, rbaaggregate.getBidQtys()[5], 0.000001);
        assertEquals(6, rbaaggregate.getNumBids());
        assertEquals(1.22, rbaaggregate.getOfferPrices()[0], 0.000001);
        assertEquals(1.23, rbaaggregate.getOfferPrices()[1], 0.000001);
        assertEquals(1.24, rbaaggregate.getOfferPrices()[2], 0.000001);
        assertEquals(1.24, rbaaggregate.getOfferPrices()[3], 0.000001);
        assertEquals(1.25, rbaaggregate.getOfferPrices()[4], 0.000001);
        assertEquals(1.26, rbaaggregate.getOfferPrices()[5], 0.000001);
        assertEquals(1000.00, rbaaggregate.getOfferQtys()[0], 0.000001);
        assertEquals(2000.00, rbaaggregate.getOfferQtys()[1], 0.000001);
        assertEquals(3000.00, rbaaggregate.getOfferQtys()[2], 0.000001);
        assertEquals(1000.00, rbaaggregate.getOfferQtys()[3], 0.000001);
        assertEquals(2000.00, rbaaggregate.getOfferQtys()[4], 0.000001);
        assertEquals(3000.00, rbaaggregate.getOfferQtys()[5], 0.000001);
        assertEquals(6, rbaaggregate.getNumOffers());
    }

    //Higher tier to lower tier
    @Test
    public void testTwoRateBookTiers() throws Exception {
        System.out.println("===== START : testRateBookTiers  =====");
        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        int EUR = Util.getBaseCurrencyIndex(commonCcyPairIdx);
        int USD = Util.getVarCurrencyIndex(commonCcyPairIdx);
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000, 2000, 3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook vwapbook = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(vwapbook);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", vwapbook, book2);

        //Add another book
        ((FIProvisionImpl) fiProvision).setAggregationType(MDFAggregationType.BEST_PRICE);
        manager.createRateBook(fiProvision, commonCcyPairIdx);

        Optional<RateBook> Obpbook = manager.getRateBook(fiProvision.getIndex(), EUR, USD, MDFAggregationType.BEST_PRICE.getIndex());
        assertTrue(Obpbook.isPresent());

        RateBook bpbook = Obpbook.get();
        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }
        assertNotEquals(vwapbook, bpbook);

        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(vwapbook.getPricebook().getTiers());
        assertEquals(0, vwapbook.getPricebook().getTiers().length);

        //add tiers now
        vwapbook.getPricebook().addTier(1000);
        vwapbook.getPricebook().addTier(2000);
        vwapbook.getPricebook().addTier(3000);

        //simulate rate updates from LP! and LP@
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2120, 1.2119);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2123, 1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2119, 1.2118);
        set3TierRates(quote, QuoteC.SELL, 1.2122, 1.2125, 1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote===" + quote.toString());

        //Simulate aggregation
        PriceBook vwapaggregate = vwapbook.aggregate();
        System.out.println("=====#1 vwapaggregate quote in   ===" + vwapaggregate.toString());
        PriceBook bpaggregate = bpbook.aggregate();

        //assert VWAP===========================
        //assert For bid price
        assertEquals(1.2121, vwapaggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21206, vwapaggregate.getBidPrices()[1], 0.00001);
        //  assertEquals(1.21207, vwapaggregate.getBidPrices()[2], 0.001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, vwapaggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, vwapaggregate.getBidQtys()[1], 0.01);
        assertEquals(2, vwapaggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, vwapaggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.21224, vwapaggregate.getOfferPrices()[1], 0.0001);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, vwapaggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, vwapaggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, vwapaggregate.getNumOffers());

        //assert BestPrice
        assertEquals(1.2121, bpaggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, bpaggregate.getBidQtys()[0], 0.01);
        assertEquals(1, bpaggregate.getNumBids());
        assertEquals(1.2122, bpaggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, bpaggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, bpaggregate.getNumOffers());


        //Now remove VWAP book
        manager.deactivateRateBook(fiProvision, commonCcyPairIdx, MDFAggregationType.WEIGHTED_AVERAGE);
        assertFalse(vwapbook.isActive());

        //this update should not goto book
        manager.handleRate(quote);
        System.out.println("=====LP1 quote after removing vwap book===" + quote.toString());

        vwapaggregate = vwapbook.aggregate();
        System.out.println("=====#1 vwapaggregate quote after remove vwap  ===" + vwapaggregate.toString());

        assertEquals(0.0, vwapaggregate.getBidPrices()[0], 0.001);
        assertEquals(0.0, vwapaggregate.getBidQtys()[0], 0.01);
        assertEquals(0.0, vwapaggregate.getBidQtys()[1], 0.01);
        assertEquals(1, vwapaggregate.getNumBids());
        assertEquals(0.0, vwapaggregate.getOfferPrices()[0], 0.01);
        assertEquals(0.0, vwapaggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, vwapaggregate.getNumOffers());
    }

    //Higher tier to lower tier
    @Test
    public void testSingleRateBookTiers() throws Exception {
        System.out.println("===== START : testRateBookTiers  =====");
        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl) serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        int EUR = Util.getBaseCurrencyIndex(commonCcyPairIdx);
        int USD = Util.getVarCurrencyIndex(commonCcyPairIdx);
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000, 2000, 3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook vwapbook = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(vwapbook);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", vwapbook, book2);

         //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(vwapbook.getPricebook().getTiers());
        assertEquals(0, vwapbook.getPricebook().getTiers().length);

        //add tiers now
        vwapbook.getPricebook().addTier(1000);
        vwapbook.getPricebook().addTier(2000);
        vwapbook.getPricebook().addTier(3000);

        //simulate rate updates from LP! and LP@
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.20, 1.22, 1000,1000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote, QuoteC.BUY, 1.2121, 1.2120, 1.2119);
        set3TierRates(quote, QuoteC.SELL, 1.2126, 1.2127, 1.2128);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        //Simulate aggregation
        PriceBook vwapaggregate = vwapbook.aggregate();
        System.out.println("=====#1 vwapaggregate quote in   ===" + vwapaggregate.toString());

        //Add another rate with 1 tiers and aggregate again
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.20, 1.21,1000,1000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set2TierRates(quote, QuoteC.BUY, 1.2120, 1.2119);
        set2TierRates(quote, QuoteC.SELL, 1.2132, 1.2133);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        //Simulate aggregation
        vwapaggregate = vwapbook.aggregate();
        System.out.println("=====#1 vwapaggregate second time   ===" + vwapaggregate.toString());

        assertEquals(1.212, vwapaggregate.getBidPrices()[0], 0.00001);
        assertEquals(1000, vwapaggregate.getBidQtys()[0], 0.0001);
        assertEquals(0.0, vwapaggregate.getBidQtys()[1], 0.01);
        assertEquals(1, vwapaggregate.getNumBids());
        assertEquals(1.2132, vwapaggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1000, vwapaggregate.getOfferQtys()[0], 0.0001);
        assertEquals(1, vwapaggregate.getNumOffers());

    }

}




