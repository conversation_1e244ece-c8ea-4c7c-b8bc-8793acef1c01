package com.integral.mdf.rate.provisioning;

import com.integral.mdf.DefaultProvisioningTest;
import com.integral.mdf.Util;
import com.integral.mdf.data.*;
import com.integral.mdf.rate.PriceAggregationTest;
import com.integral.mdf.rate.RateChannel;
import com.integral.mdf.rate.RateDistributionManager;
import com.integral.mdf.rate.live.MarketPrice;
import com.integral.mdf.rate.live.MarketRate;
import com.integral.mdf.rate.live.MarketRateC;
import com.integral.mdf.rate.live.RateConversionCache;
import com.integral.provision.LPProvision;
import com.integral.util.Tuple;
import org.jctools.maps.NonBlockingHashMapLong;
import org.junit.After;
import org.junit.Assert;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

public class TestCredit extends DefaultProvisioningTest {

    public static final String LP22 = "lp2";
    public static final String LP12 = "lp1";
    public static final String EUR_USD = "EUR/USD";
    private static final int PRECISION = 4;
    private static final double PRICE = 1.12d;
    public ProvisioningCalculator calculator;
    public RateDistributionManager manager;
    private static final int HEADER_SIZE = 3;
    NonBlockingHashMapLong<RateChannel> rateChannels = new NonBlockingHashMapLong<>(1024);
    ProvisionedQuoteC provisionedQuote;
    PriceAggregationTest priceAggregationTest = new PriceAggregationTest();

    @Test
    public void TestCreditQuoteBidOffer() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        //get fiProvision //FIProvision fiProvision = getFIProvision();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        assertNotNull(fiProvision);

        //get cpindex //        Integer cpIndex = getCPIdx(fiProvision);
        Integer CPIndex = obj.getCPIdx(fiProvision);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, CPIndex, CPIndex, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers);
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        System.out.println("provisionedQuote=========" + provisionedQuote.getProvisionedQuoteString());

        assertEquals(quote.getQuoteCreatedTime(), provisionedQuote.getQuoteCreatedTime());
        obj.assertProvisionedQuote(noOfTiers, quote, provisionedQuote);

        Tuple<Boolean, Long> value = new Tuple<Boolean, Long>(true, (long) 5000);
        calculator.creditInfo.put(provisionedQuote.getValueDate(), value);
        //sets this credit limit as the value for both bid and offer limits
        calculator.applyCredit(provisionedQuote, value);

        //        CreditLimitInfo cl;
//        cl.setCreditStatus();
        System.out.println("========newQuote.getValueDate(),value=========" + provisionedQuote.getValueDate());
        System.out.println("calculator getProvisionedRate===========" + calculator.getProvisionedRate());
        //System.out.println("calculator getProvisionedRate" +calculator.getCreditLimit(CreditLimitInfo cl)));
        System.out.println("provisionedQuote2=========" + provisionedQuote.getProvisionedQuoteString());
        System.out.println("provisionedQuote quote=========" + provisionedQuote);

        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the ShowQty fields
        assertEquals(5000.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(5000.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);


    }


    @Test
    public void TestCreditDailyAgg() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        //get fiProvision //FIProvision fiProvision = getFIProvision();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        assertNotNull(fiProvision);

        Integer CPIndex = obj.getCPIdx(fiProvision);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, CPIndex, CPIndex, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        //set credit object values
        short valueDate = 233;
        CreditLimitInfo info = new CreditLimitInfo(1l, 1l, valueDate);
        info.setCreditStatus((byte) 1);          //NO_CHECK 0, ACTIVE 1, SUSPEND 2
        info.setDailyAvailable(2000);
        info.setAggregateAvailable(7000);
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects
        calculator.onCredit(info);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========CreditLimitInfo should give credit details=========" + info.toString());

        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        //applies credit
        obj.applyCreditC(calculator, info, provisionedQuote);
        System.out.println("=========calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        assertEquals(2000.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(2000.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

    }

    @Test   //credit should not be applied on non-default LE //fi default Le is 9001, but credit applied for other LE s as well
    public void TestCreditDailyAggLE() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        assertNotNull(fiProvision);

        long fILe = fiProvision.getDefaultLEObjectId();
        impl.setDefaultLEObjId(7453);
        System.out.println("====fILe=========" + fILe);

        //assuming xyz/usd
        int baseccy = 2; //xyz
        int varccy = 1; //usd
        MarketRate mr = getMarketRate(baseccy,varccy,1.5,1.5,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/abc also - to test logic in cache...
        mr = getMarketRate(varccy,3,1.75,1.75,1000,1000);
        RateConversionCache.getInstance().update(mr);

        Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);
        long lple = lp.getFiClearingMemberLEId();
        System.out.println("====lple=========" + lple);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        //set credit object values
        short valueDate = quote.getValueDate();
        CreditLimitInfo info = new CreditLimitInfo(fILe, 1l, valueDate);
        info.setCreditStatus((byte) 1);          //NO_CHECK 0, ACTIVE 1, SUSPEND 2
        info.setDailyAvailable(2000);
        info.setAggregateAvailable(7000);
        info.setLimitCcy((short)1);//USD
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects
        calculator.onCredit(info);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========CreditLimitInfo should give credit details=========" + info.toString());

        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        //applies credit
        //Tuple<Boolean, Long> credit = calculator.creditInfo.get(provisionedQuote.getValueDate());
        //calculator.applyCredit();

        //obj.applyCreditC(calculator, info, provisionedQuote);
        provisionedQuote = calculator.getProvisionedRate();

        System.out.println("=========calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        // 2000(applied credit)  divided by  1.5(Direct rate for XYZ/USD)
        assertEquals(1333.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);
        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(1333.0 , provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

    }

    //@Test
   /*public void TestCreditDailyAggConversionRate() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        //get fiProvision //FIProvision fiProvision = getFIProvision();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        assertNotNull(fiProvision);

        //assuming xyz/usd
        int baseccy = 2; //xyz
        int varccy = 1; //usd
        RateConversionCache.getInstance().notify(baseccy,varccy,1.5);

        Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        //Integer CPIndex = obj.getCPIdx(fiProvision);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        //set credit object values
        short valueDate = 233;
        CreditLimitInfo info = new CreditLimitInfo(1l, 1l, valueDate);
        info.setCreditStatus((byte) 1);          //NO_CHECK 0, ACTIVE 1, SUSPEND 2
        info.setDailyAvailable(2000);
        info.setAggregateAvailable(7000);
        info.setLimitCcy((short)1);//USD
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects
        calculator.onCredit(info);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========CreditLimitInfo should give credit details=========" + info.toString());

        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        //applies credit
        obj.applyCreditC(calculator, info, provisionedQuote);
        System.out.println("=========calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        assertEquals(2000.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(2000.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

    }


    /**
     * USD is limit ccy of credit limit
     * Main ccypair = XYZ/USD same as EUR/USD
     * @throws Exception
     */

    @Test
    public void TestCreditLimitConversionXYZUSD() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        //get fiProvision //FIProvision fiProvision = getFIProvision();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        assertNotNull(fiProvision);

        //assuming xyz/usd
        int baseccy = 2; //xyz
        int varccy = 1; //usd
        MarketRate mr = getMarketRate(baseccy,varccy,1.5,1.5,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/abc also - to test logic in cache...
        mr = getMarketRate(varccy,3,1.75,1.75,1000,1000);
        RateConversionCache.getInstance().update(mr);

        Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        //Integer CPIndex = obj.getCPIdx(fiProvision);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        //set credit object values
        short valueDate = quote.getValueDate();
        CreditLimitInfo info = new CreditLimitInfo(1l, 1l, valueDate);
        info.setCreditStatus((byte) 1);          //NO_CHECK 0, ACTIVE 1, SUSPEND 2
        info.setDailyAvailable(2000);
        info.setAggregateAvailable(7000);
        info.setLimitCcy((short)1);//USD
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects
        calculator.onCredit(info);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========CreditLimitInfo should give credit details=========" + info.toString());

        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        //applies credit
        //Tuple<Boolean, Long> credit = calculator.creditInfo.get(provisionedQuote.getValueDate());
        //calculator.applyCredit();

        //obj.applyCreditC(calculator, info, provisionedQuote);
        provisionedQuote = calculator.getProvisionedRate();

        System.out.println("=========calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        // 2000(applied credit)  divided by  1.5(Direct rate for XYZ/USD)
        assertEquals(1333.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);
        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(1333.0 , provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);
    }


    /**
     * USD is limit ccy of credit limit
     * Main ccypair = USD/XYZ - No conversion required.
     * @throws Exception
     */
    @Test
    public void TestCreditLimitConversionUSDXYZ() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        //get fiProvision //FIProvision fiProvision = getFIProvision();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        assertNotNull(fiProvision);

        //assuming usd/xyz
        int baseccy = 1; //USD
        int varccy = 3; //XYZ
        MarketRate mr = getMarketRate(baseccy,varccy,1.5,1.5,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/abc also - to test logic in cache...
        mr = getMarketRate(2,baseccy,1.75,1.75,1000,1000);
        RateConversionCache.getInstance().update(mr);

        Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        //Integer CPIndex = obj.getCPIdx(fiProvision);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        //set credit object values
        short valueDate = quote.getValueDate();
        CreditLimitInfo info = new CreditLimitInfo(1l, 1l, valueDate);
        info.setCreditStatus((byte) 1);          //NO_CHECK 0, ACTIVE 1, SUSPEND 2
        info.setDailyAvailable(2000);
        info.setAggregateAvailable(7000);
        info.setLimitCcy((short)1);//USD
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects
        calculator.onCredit(info);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========CreditLimitInfo should give credit details=========" + info.toString());

        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        //applies credit
        //Tuple<Boolean, Long> credit = calculator.creditInfo.get(provisionedQuote.getValueDate());
        //calculator.applyCredit();

        //obj.applyCreditC(calculator, info, provisionedQuote);
        provisionedQuote = calculator.getProvisionedRate();

        System.out.println("=========calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        assertEquals(2000.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);
        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(2000.0 , provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

    }


    /**
     * USD is limit ccy of credit limit.
     * Main ccypair = XYZ/ABC
     * USD cross = XYZ/USD
     * @throws Exception
     */
    @Test
    public void TestCreditLimitConversionXYZABC_XYZUSD_Cross() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        //get fiProvision //FIProvision fiProvision = getFIProvision();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        assertNotNull(fiProvision);

        //assuming xyz/abc
        int baseccy = 2; //XYZ
        int varccy = 3; //ABC

        //XYZ/ABC live rate - useless for conversion.
        MarketRate mr = getMarketRate(baseccy,varccy,2.0,2.0,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add xyz/usd - required for conversion
        mr = getMarketRate(baseccy,1,1.5,1.5,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/abc also - to test logic in cache...
        mr = getMarketRate(1,varccy,1.75,1.75,1000,1000);
        RateConversionCache.getInstance().update(mr);

        Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        //Integer CPIndex = obj.getCPIdx(fiProvision);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        //set credit object values
        short valueDate = quote.getValueDate();
        CreditLimitInfo info = new CreditLimitInfo(1l, 1l, valueDate);
        info.setCreditStatus((byte) 1);          //NO_CHECK 0, ACTIVE 1, SUSPEND 2
        info.setDailyAvailable(2000);
        info.setAggregateAvailable(7000);
        info.setLimitCcy((short)1);//USD
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects
        calculator.onCredit(info);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========CreditLimitInfo should give credit details=========" + info.toString());

        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        //applies credit
        //Tuple<Boolean, Long> credit = calculator.creditInfo.get(provisionedQuote.getValueDate());
        //calculator.applyCredit();

        //obj.applyCreditC(calculator, info, provisionedQuote);
        provisionedQuote = calculator.getProvisionedRate();

        System.out.println("=========calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        //2000 divided by 1.5 (conversion rate for XYZ/USD
        assertEquals(1333.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);
        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(1333.0 , provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

    }


    /**
     * USD is limit ccy of credit limit.
     * Main ccypair = XYZ/ABC
     * USD cross = USD/XYZ
     * @throws Exception
     */
    @Test
    public void TestCreditLimitConversionXYZABC_USDXYZ_Cross() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        //get fiProvision //FIProvision fiProvision = getFIProvision();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        assertNotNull(fiProvision);

        //assuming xyz/abc
        int baseccy = 2; //XYZ
        int varccy = 3; //ABC

        //XYZ/ABC live rate - useless for conversion.
        MarketRate mr = getMarketRate(baseccy,varccy,2.0,2.0,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/xyz - required for conversion
        mr = getMarketRate(1,baseccy,1.5,1.5,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/abc also - to test logic in cache...
        mr = getMarketRate(1,varccy,1.75,1.75,1000,1000);
        RateConversionCache.getInstance().update(mr);

        Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        //Integer CPIndex = obj.getCPIdx(fiProvision);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        //set credit object values
        short valueDate = quote.getValueDate();
        CreditLimitInfo info = new CreditLimitInfo(1l, 1l, valueDate);
        info.setCreditStatus((byte) 1);          //NO_CHECK 0, ACTIVE 1, SUSPEND 2
        info.setDailyAvailable(2000);
        info.setAggregateAvailable(7000);
        info.setLimitCcy((short)1);//USD
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects
        calculator.onCredit(info);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========CreditLimitInfo should give credit details=========" + info.toString());

        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        //applies credit
        //Tuple<Boolean, Long> credit = calculator.creditInfo.get(provisionedQuote.getValueDate());
        //calculator.applyCredit();

        //obj.applyCreditC(calculator, info, provisionedQuote);
        provisionedQuote = calculator.getProvisionedRate();

        System.out.println("=========calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        //Inverted rate (2000 * (1 divided by 1.5))
        assertEquals(3000, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);
        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(3000 , provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

    }


    @Test //huge credit available, so LP quote is qualified as it is
    public void TestCreditLimitConversionXYZUSDmorelmt() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        //get fiProvision //FIProvision fiProvision = getFIProvision();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        assertNotNull(fiProvision);

        //assuming xyz/usd
        int baseccy = 2; //xyz
        int varccy = 1; //usd
        MarketRate mr = getMarketRate(baseccy,varccy,1.5,1.5,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/abc also - to test logic in cache...
        mr = getMarketRate(varccy,3,1.75,1.75,1000,1000);
        RateConversionCache.getInstance().update(mr);
        System.out.println("====MarketRate=========" + mr.getBidPrices());

        Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        //Integer CPIndex = obj.getCPIdx(fiProvision);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        //set credit object values
        short valueDate = quote.getValueDate();
        CreditLimitInfo info = new CreditLimitInfo(1l, 1l, valueDate);
        info.setCreditStatus((byte) 1);          //NO_CHECK 0, ACTIVE 1, SUSPEND 2
        info.setDailyAvailable(200000);
        info.setAggregateAvailable(700000);
        info.setLimitCcy((short)1);//USD
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects
        calculator.onCredit(info);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========CreditLimitInfo should give credit details=========" + info.toString());

        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        //applies credit
        //Tuple<Boolean, Long> credit = calculator.creditInfo.get(provisionedQuote.getValueDate());
        //calculator.applyCredit();

        //obj.applyCreditC(calculator, info, provisionedQuote);
        provisionedQuote = calculator.getProvisionedRate();

        System.out.println("=========calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields

        assertEquals(10000, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);
        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(10000 , provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);
    }


    @Test
    public void TestCreditLimitConversionUSDXYZmorelmt() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        //get fiProvision //FIProvision fiProvision = getFIProvision();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        assertNotNull(fiProvision);

        //assuming xyz/usd
        int baseccy = 2; //xyz
        int varccy = 1; //usd
        MarketRate mr = getMarketRate(baseccy,varccy,1.5,1.5,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/abc also - to test logic in cache...
        mr = getMarketRate(varccy,3,1.75,1.75,1000,1000);
        RateConversionCache.getInstance().update(mr);
        System.out.println("====MarketRate=========" + mr.getBidPrices());

        Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        //Integer CPIndex = obj.getCPIdx(fiProvision);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        //set credit object values
        short valueDate = quote.getValueDate();
        CreditLimitInfo info = new CreditLimitInfo(1l, 1l, valueDate);
        info.setCreditStatus((byte) 1);          //NO_CHECK 0, ACTIVE 1, SUSPEND 2
        info.setDailyAvailable(200000);
        info.setAggregateAvailable(7000);
        info.setLimitCcy((short)1);//USD
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects
        calculator.onCredit(info);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========CreditLimitInfo should give credit details=========" + info.toString());

        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        //applies credit
        //Tuple<Boolean, Long> credit = calculator.creditInfo.get(provisionedQuote.getValueDate());
        //calculator.applyCredit();

        //obj.applyCreditC(calculator, info, provisionedQuote);
        provisionedQuote = calculator.getProvisionedRate();

        System.out.println("=========calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        //7000 divided by 1.5
        assertEquals(4666, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);
        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(4666 , provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

    }


    @Test //LP qualifies less
    public void TestCreditLimitConversionUSDXYZmorelmtlowrate() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        //get fiProvision //FIProvision fiProvision = getFIProvision();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        assertNotNull(fiProvision);

        //assuming xyz/usd
        int baseccy = 2; //xyz
        int varccy = 1; //usd
        MarketRate mr = getMarketRate(baseccy,varccy,0.5,0.5,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/abc also - to test logic in cache...
        mr = getMarketRate(varccy,3,0.75,0.75,1000,1000);
        RateConversionCache.getInstance().update(mr);
        System.out.println("====MarketRate=========" + mr.getBidPrices());

        Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        //Integer CPIndex = obj.getCPIdx(fiProvision);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers, 9000);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        //set credit object values
        short valueDate = quote.getValueDate();
        CreditLimitInfo info = new CreditLimitInfo(1l, 1l, valueDate);
        info.setCreditStatus((byte) 1);          //NO_CHECK 0, ACTIVE 1, SUSPEND 2
        info.setDailyAvailable(6000);
        info.setAggregateAvailable(6000);
        info.setLimitCcy((short)1);//USD
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects
        calculator.onCredit(info);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========CreditLimitInfo should give credit details=========" + info.toString());

        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        //applies credit
        //Tuple<Boolean, Long> credit = calculator.creditInfo.get(provisionedQuote.getValueDate());
        //calculator.applyCredit();

        //obj.applyCreditC(calculator, info, provisionedQuote);
        provisionedQuote = calculator.getProvisionedRate();
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects

        System.out.println("=========calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        //6000 divided by 0.5 - 12000, but LP only qualifies 9000
        assertEquals(9000, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);
        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(9000 , provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

    }


    @Test
    public void TestCreditLimitConversionXYZUSDmorelmtlowrate() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        //get fiProvision //FIProvision fiProvision = getFIProvision();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        assertNotNull(fiProvision);

        //assuming xyz/usd
        int baseccy = 2; //xyz
        int varccy = 1; //usd
        MarketRate mr = getMarketRate(baseccy,varccy,0.5,0.5,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/abc also - to test logic in cache...
        mr = getMarketRate(varccy,3,0.75,0.75,1000,1000);
        RateConversionCache.getInstance().update(mr);
        System.out.println("====MarketRate=========" + mr.getBidPrices());

        Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        //Integer CPIndex = obj.getCPIdx(fiProvision);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        //set credit object values
        short valueDate = quote.getValueDate();
        CreditLimitInfo info = new CreditLimitInfo(1l, 1l, valueDate);
        info.setCreditStatus((byte) 1);          //NO_CHECK 0, ACTIVE 1, SUSPEND 2
        info.setDailyAvailable(200000);
        info.setAggregateAvailable(700000);
        info.setLimitCcy((short)1);//USD
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects
        calculator.onCredit(info);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========CreditLimitInfo should give credit details=========" + info.toString());

        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        //applies credit
        //Tuple<Boolean, Long> credit = calculator.creditInfo.get(provisionedQuote.getValueDate());
        //calculator.applyCredit();

        //obj.applyCreditC(calculator, info, provisionedQuote);
        provisionedQuote = calculator.getProvisionedRate();

        System.out.println("=========calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        assertEquals(10000, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);
        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(10000 , provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

    }


    @Test
    public void TestCreditLimitConversionXYZUSDlesslmtlowrate() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        //get fiProvision //FIProvision fiProvision = getFIProvision();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        assertNotNull(fiProvision);

        //assuming xyz/usd
        int baseccy = 2; //xyz
        int varccy = 1; //usd
        MarketRate mr = getMarketRate(baseccy,varccy,0.5,0.5,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/abc also - to test logic in cache...
        mr = getMarketRate(varccy,3,0.75,0.75,1000,1000);
        RateConversionCache.getInstance().update(mr);
        System.out.println("====MarketRate=========" + mr.getBidPrices());

        Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        //Integer CPIndex = obj.getCPIdx(fiProvision);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        //set credit object values
        short valueDate = quote.getValueDate();
        CreditLimitInfo info = new CreditLimitInfo(1l, 1l, valueDate);
        info.setCreditStatus((byte) 1);          //NO_CHECK 0, ACTIVE 1, SUSPEND 2
        info.setDailyAvailable(3000);
        info.setAggregateAvailable(7000);
        info.setLimitCcy((short)1);//USD
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects
        calculator.onCredit(info);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========CreditLimitInfo should give credit details=========" + info.toString());

        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        //applies credit
        //Tuple<Boolean, Long> credit = calculator.creditInfo.get(provisionedQuote.getValueDate());
        //calculator.applyCredit();

        //obj.applyCreditC(calculator, info, provisionedQuote);
        provisionedQuote = calculator.getProvisionedRate();

        System.out.println("=========calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        //3000 divided by 0.5
        assertEquals(6000, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);
        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(6000 , provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

    }


    /**
     * USD is limit ccy of credit limit.
     * Main ccypair = XYZ/ABC
     * USD cross = USD/XYZ
     * @throws Exception
     */
    @Test //LP gives less
    public void TestCreditLimitConversionXYZABC_USDXYZ_Crosslesslmt() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        //get fiProvision //FIProvision fiProvision = getFIProvision();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        assertNotNull(fiProvision);

        //assuming xyz/abc
        int baseccy = 2; //XYZ
        int varccy = 3; //ABC

        //XYZ/ABC live rate - useless for conversion.
        MarketRate mr = getMarketRate(baseccy,varccy,2.0,2.0,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/xyz - required for conversion
        mr = getMarketRate(1,baseccy,0.5,0.5,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/abc also - to test logic in cache...
        mr = getMarketRate(1,varccy,1.75,1.75,1000,1000);
        RateConversionCache.getInstance().update(mr);

        Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        //Integer CPIndex = obj.getCPIdx(fiProvision);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers, 800);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        //set credit object values
        short valueDate = quote.getValueDate();
        CreditLimitInfo info = new CreditLimitInfo(1l, 1l, valueDate);
        info.setCreditStatus((byte) 1);          //NO_CHECK 0, ACTIVE 1, SUSPEND 2
        info.setDailyAvailable(2000);
        info.setAggregateAvailable(7000);
        info.setLimitCcy((short)1);//USD
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects
        calculator.onCredit(info);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========CreditLimitInfo should give credit details=========" + info.toString());

        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        //applies credit
        //Tuple<Boolean, Long> credit = calculator.creditInfo.get(provisionedQuote.getValueDate());
        //calculator.applyCredit();

        //obj.applyCreditC(calculator, info, provisionedQuote);
        provisionedQuote = calculator.getProvisionedRate();

        System.out.println("=========calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        //Inverted rate (2000 * (1 divided by 0.5))
        assertEquals(800, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);
        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(800 , provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

    }


    @Test
    public void TestMaxCredit() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        assertNotNull(fiProvision);

        long fILe = fiProvision.getDefaultLEObjectId();
        impl.setDefaultLEObjId(7453);
        System.out.println("====fILe=========" + fILe);

        //assuming xyz/usd
        int baseccy = 2; //xyz
        int varccy = 1; //usd
        MarketRate mr = getMarketRate(baseccy,varccy,1.5,1.5,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/abc also - to test logic in cache...
        mr = getMarketRate(varccy,3,1.75,1.75,1000,1000);
        RateConversionCache.getInstance().update(mr);

        Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);
        long lple = lp.getFiClearingMemberLEId();
        System.out.println("====lple=========" + lple);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        //set credit object values
        short valueDate = quote.getValueDate();
//        CreditLimitInfo info = new CreditLimitInfo(fILe, 1l, valueDate);
//        info.setCreditStatus((byte) 1);          //NO_CHECK 0, ACTIVE 1, SUSPEND 2
//        info.setDailyAvailable(2000);
//        info.setAggregateAvailable(7000);
//        info.setLimitCcy((short)1);//USD
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects
        calculator.onCredit(valueDate,(byte) 1,2000,(short)1);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========CreditLimitInfo should give credit details=========  status=1,limitccy=1,limit=2000,vd="+valueDate);

        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        //applies credit
        //Tuple<Boolean, Long> credit = calculator.creditInfo.get(provisionedQuote.getValueDate());
        //calculator.applyCredit();

        //obj.applyCreditC(calculator, info, provisionedQuote);
        provisionedQuote = calculator.getProvisionedRate();

        System.out.println("=========calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        // 2000(applied credit)  divided by  1.5(Direct rate for XYZ/USD)
        assertEquals(1333.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);
        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(1333.0 , provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

    }

    //Modifying credit second time and then validating the code
    @Test
    public void TestMaxCredit_Modify() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        assertNotNull(fiProvision);

        long fILe = fiProvision.getDefaultLEObjectId();
        impl.setDefaultLEObjId(7453);
        System.out.println("====fILe=========" + fILe);

        //assuming xyz/usd
        int baseccy = 2; //xyz
        int varccy = 1; //usd
        MarketRate mr = getMarketRate(baseccy,varccy,1.5,1.5,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/abc also - to test logic in cache...
        mr = getMarketRate(varccy,3,1.75,1.75,1000,1000);
        RateConversionCache.getInstance().update(mr);

        Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);
        long lple = lp.getFiClearingMemberLEId();
        System.out.println("====lple=========" + lple);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        //set credit object values
        short valueDate = quote.getValueDate();
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects
        calculator.onCredit(valueDate,(byte) 1,2000,(short)1);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========CreditLimitInfo should give credit details=========  status=1,limitccy=1,limit=2000,vd="+valueDate);

        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        provisionedQuote = calculator.getProvisionedRate();
        System.out.println("=========calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        // 2000(applied credit)  divided by  1.5(Direct rate for XYZ/USD)
        assertEquals(1333.0, provisionedQuote.getPShowQty(bidTierOffset), 0.0001);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.0001);
        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(1333.0 , provisionedQuote.getPShowQty(offerTierOffset), 0.0001);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.0001);

        //Modify the max credit limit and check the rate
        calculator.onCredit(valueDate,(byte) 1,3500,(short)1);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========Modified credit - CreditLimitInfo should give credit details=========  status=1,limitccy=1,limit=3500,vd="+valueDate);
        assertEquals(1333.0, provisionedQuote.getPShowQty(bidTierOffset), 0.0001);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.0001);

        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(1333.0, provisionedQuote.getPShowQty(offerTierOffset), 0.0001);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.0001);

        provisionedQuote = calculator.getProvisionedRate();
        System.out.println("=========Modified credit - calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        // 2000(applied credit)  divided by  1.5(Direct rate for XYZ/USD)
        assertEquals(2333.0, provisionedQuote.getPShowQty(bidTierOffset), 0.001);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.001);
        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(2333.0 , provisionedQuote.getPShowQty(offerTierOffset), 0.001);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.001);


    }


    @Test
    public void TestMaxCredit_0() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        assertNotNull(fiProvision);

        long fILe = fiProvision.getDefaultLEObjectId();
        impl.setDefaultLEObjId(7453);
        System.out.println("====fILe=========" + fILe);

        //assuming xyz/usd
        int baseccy = 2; //xyz
        int varccy = 1; //usd
        MarketRate mr = getMarketRate(baseccy,varccy,1.5,1.5,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/abc also - to test logic in cache...
        mr = getMarketRate(varccy,3,1.75,1.75,1000,1000);
        RateConversionCache.getInstance().update(mr);

        Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);
        long lple = lp.getFiClearingMemberLEId();
        System.out.println("====lple=========" + lple);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        //set credit object values
        short valueDate = quote.getValueDate();
//        CreditLimitInfo info = new CreditLimitInfo(fILe, 1l, valueDate);
//        info.setCreditStatus((byte) 1);          //NO_CHECK 0, ACTIVE 1, SUSPEND 2
//        info.setDailyAvailable(2000);
//        info.setAggregateAvailable(7000);
//        info.setLimitCcy((short)1);//USD
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects
        calculator.onCredit(valueDate,(byte) 1,0,(short)1);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========CreditLimitInfo should give credit details=========  status=1,limitccy=1,limit=2000,vd="+valueDate);

        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        //applies credit
        //Tuple<Boolean, Long> credit = calculator.creditInfo.get(provisionedQuote.getValueDate());
        //calculator.applyCredit();

        //obj.applyCreditC(calculator, info, provisionedQuote);
        provisionedQuote = calculator.getProvisionedRate();

        System.out.println("=========calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        // 2000(applied credit)  divided by  1.5(Direct rate for XYZ/USD)
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);
        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0 , provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

    }

    //Check - Not sure of the expected values, check regular credit flow
    @Test
    public void TestMaxCredit_Neg() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        assertNotNull(fiProvision);

        long fILe = fiProvision.getDefaultLEObjectId();
        impl.setDefaultLEObjId(7453);
        System.out.println("====fILe=========" + fILe);

        //assuming xyz/usd
        int baseccy = 2; //xyz
        int varccy = 1; //usd
        MarketRate mr = getMarketRate(baseccy,varccy,1.5,1.5,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/abc also - to test logic in cache...
        mr = getMarketRate(varccy,3,1.75,1.75,1000,1000);
        RateConversionCache.getInstance().update(mr);

        Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);
        long lple = lp.getFiClearingMemberLEId();
        System.out.println("====lple=========" + lple);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        //set credit object values
        short valueDate = quote.getValueDate();
//        CreditLimitInfo info = new CreditLimitInfo(fILe, 1l, valueDate);
//        info.setCreditStatus((byte) 1);          //NO_CHECK 0, ACTIVE 1, SUSPEND 2
//        info.setDailyAvailable(2000);
//        info.setAggregateAvailable(7000);
//        info.setLimitCcy((short)1);//USD
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects
        calculator.onCredit(valueDate,(byte) 1,-350,(short)1);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========CreditLimitInfo should give credit details=========  status=1,limitccy=1,limit=2000,vd="+valueDate);

        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        //applies credit
        //Tuple<Boolean, Long> credit = calculator.creditInfo.get(provisionedQuote.getValueDate());
        //calculator.applyCredit();

        //obj.applyCreditC(calculator, info, provisionedQuote);
        provisionedQuote = calculator.getProvisionedRate();

        System.out.println("=========calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        // 2000(applied credit)  divided by  1.5(Direct rate for XYZ/USD)
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);
        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0 , provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

    }

    //Even though credit limit is 2000, provisioned quote has default limits as 10000
    @Test
    public void TestMaxCredit_NOCheck() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        assertNotNull(fiProvision);

        long fILe = fiProvision.getDefaultLEObjectId();
        impl.setDefaultLEObjId(7453);
        System.out.println("====fILe=========" + fILe);

        //assuming xyz/usd
        int baseccy = 2; //xyz
        int varccy = 1; //usd
        MarketRate mr = getMarketRate(baseccy,varccy,1.5,1.5,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/abc also - to test logic in cache...
        mr = getMarketRate(varccy,3,1.75,1.75,1000,1000);
        RateConversionCache.getInstance().update(mr);

        Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);
        long lple = lp.getFiClearingMemberLEId();
        System.out.println("====lple=========" + lple);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        //set credit object values
        short valueDate = quote.getValueDate();
//        CreditLimitInfo info = new CreditLimitInfo(fILe, 1l, valueDate);
//        info.setCreditStatus((byte) 1);          //NO_CHECK 0, ACTIVE 1, SUSPEND 2
//        info.setDailyAvailable(2000);
//        info.setAggregateAvailable(7000);
//        info.setLimitCcy((short)1);//USD
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects
        calculator.onCredit(valueDate,(byte) 0,2000,(short)1);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========CreditLimitInfo should give credit details=========  status=1,limitccy=1,limit=2000,vd="+valueDate);

        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        //applies credit
        //Tuple<Boolean, Long> credit = calculator.creditInfo.get(provisionedQuote.getValueDate());
        //calculator.applyCredit();

        //obj.applyCreditC(calculator, info, provisionedQuote);
        provisionedQuote = calculator.getProvisionedRate();

        System.out.println("=========calculator getProvisionedRate should not be credit qualified, since it is NO Check=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        // 2000(applied credit)  divided by  1.5(Direct rate for XYZ/USD)
        assertEquals(10000, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);
        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(10000 , provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

    }

    @Test
    public void TestMaxCredit_limitexceeds() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI2);
        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        assertNotNull(fiProvision);

        long fILe = fiProvision.getDefaultLEObjectId();
        impl.setDefaultLEObjId(7453);
        System.out.println("====fILe=========" + fILe);

        //assuming xyz/usd
        int baseccy = 2; //xyz
        int varccy = 1; //usd
        MarketRate mr = getMarketRate(baseccy,varccy,1.5,1.5,15000,15000);
        RateConversionCache.getInstance().update(mr);

        //add usd/abc also - to test logic in cache...
        mr = getMarketRate(varccy,3,1.75,1.75,15000,15000);
        RateConversionCache.getInstance().update(mr);

        Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);
        long lple = lp.getFiClearingMemberLEId();
        System.out.println("====lple=========" + lple);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());
        //set credit object values
        short valueDate = quote.getValueDate();
//        CreditLimitInfo info = new CreditLimitInfo(fILe, 1l, valueDate);
//        info.setCreditStatus((byte) 1);          //NO_CHECK 0, ACTIVE 1, SUSPEND 2
//        info.setDailyAvailable(2000);
//        info.setAggregateAvailable(7000);
//        info.setLimitCcy((short)1);//USD
        calculator.onRate(quote);       //sets the raw rate and raw rate pool objects
        calculator.onCredit(valueDate,(byte) 1,200000,(short)1);  //sets the first and the second element in the creditinfo object  same as calculator.creditInfo.put or creditLimitInfo.first
        System.out.println("=========CreditLimitInfo should give credit details=========  status=1,limitccy=1,limit=2000,vd="+valueDate);

        System.out.println("=========calculator getProvisionedRate should NOT be credit qualified=========" + provisionedQuote.toString());
        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //PShowQty fields are not exist yet
        assertEquals(0.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.0, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(0.0, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        //applies credit
        //Tuple<Boolean, Long> credit = calculator.creditInfo.get(provisionedQuote.getValueDate());
        //calculator.applyCredit();

        //obj.applyCreditC(calculator, info, provisionedQuote);
        provisionedQuote = calculator.getProvisionedRate();

        System.out.println("=========calculator getProvisionedRate should be credit qualified=========" + provisionedQuote.toString());

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        //The credit qualified limits are seen in the PShowQty fields
        // 2000(applied credit)  divided by  1.5(Direct rate for XYZ/USD)
        assertEquals(10000.0, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);
        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(10000.0 , provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

    }

    //@Test
   public void TestFullflow_Credit() throws Exception {
        ProvisioningCalculatorTest obj = new ProvisioningCalculatorTest();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        RateDistributionManager manager = new RateDistributionManager(serverProvision);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        //impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        LPProvision lp1Prov = null;
        for (LPProvision lpProv : fiProvision.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lp1Prov = lpProv;
                break;
            }
        }
        Assert.assertNotNull(lp1Prov);

        //assuming xyz/usd
        int baseccy = 2; //xyz
        int varccy = 1; //usd
        MarketRate mr = getMarketRate(baseccy,varccy,1.5,1.5,1000,1000);
        RateConversionCache.getInstance().update(mr);

        //add usd/abc also - to test logic in cache...
        mr = getMarketRate(varccy,3,1.75,1.75,1000,1000);
        RateConversionCache.getInstance().update(mr);


        long fILe = fiProvision.getDefaultLEObjectId();
        impl.setDefaultLEObjId(7453);
        System.out.println("====fILe=========" + fILe);

        //Integer CPIndex = Util.getCurrencyPairIndex(baseccy,varccy);
        Integer CPIndex = obj.getCPIdx(fiProvision);
        //Integer CPIndex =serverProvision.getCcyPairName(server.get);
        assertNotNull(CPIndex);
        System.out.println("====CPIndex=========" + CPIndex.toString());

        LPProvision lp = obj.getLP(fiProvision);
        long lple = lp.getFiClearingMemberLEId();
        System.out.println("====lple=========" + lple);

        //get calculator
        calculator = new ProvisioningCalculator("TestDaily", "TestDaily", lp1Prov, fiProvision, baseccy, varccy, rawRatePool, PRECISION, -1, true);

        int noOfTiers = 1;
        QuoteC quote = obj.getQuote(CPIndex, noOfTiers);  //default limit is set to 10000
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());
        System.out.println("quote==========" + quote.toString());

        manager.handleRate(quote);

        int sIdx = quote.getStreamIdx();
        int cIdx = quote.getCcyPairIdx();
        int bIdx = Util.getBaseCurrencyIndex(cIdx);
        int vIdx = Util.getVarCurrencyIndex(cIdx);
        long key = Util.getRateChannelKey(sIdx, bIdx, vIdx);
        RateChannel channel = rateChannels.get(key);

//        if (channel != null) {
//            RateProcessor rp = manager.getRateProcessor(cIdx);
//            channel.updateRateBooks(rp.sink,rp.rpm);
//        }

        manager.createRateBooks(fiProvision);


        //RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        //PriceBook aggregate = book1.aggregate();
        //System.out.println("=======book1 when stream inactive========" +aggregate.toString());

        //manager.handleMaxCredit();

        //Validate credit provisioned quote

    }


    @After
    public void clearRateCache() {
        RateConversionCache.getInstance().clear();
    }


    MarketRate getMarketRate(int baseidx, int varidx, double bid, double offer,long bidlimit, long offerlimit){

        MarketRate mr = new MarketRateC(1,1);
        mr.setBaseCcyIndex(baseidx);
        mr.setVarCcyIndex(varidx);
        mr.setProviderIndex(1000);//doesn't matter
        mr.setQuoteId(1000l);

        MarketPrice bidmp = mr.getOrCreateBidPrice(0);
        bidmp.setSpotRate(bid);
        bidmp.setRate(bid);
        bidmp.setLimit(bidlimit);
        bidmp.setTotalLimit(bidlimit);
        mr.addBidPrice(bidmp);
        mr.setNoOfBidTiers(1);

        MarketPrice offermp = mr.getOrCreateOfferPrice(0);
        offermp.setSpotRate(offer);
        offermp.setRate(offer);
        offermp.setLimit(offerlimit);
        offermp.setTotalLimit(offerlimit);
        mr.addOfferPrice(offermp);
        mr.setNoOfOfferTiers(1);


        return mr;
    }

}

