package com.integral.mdf.rate.provisioning;

import com.integral.mdf.DefaultProvisioningTest;
import com.integral.mdf.Util;
import com.integral.mdf.data.*;
import com.integral.provision.LPProvision;
import com.integral.util.Tuple;
import org.junit.Test;

import java.util.Iterator;

import static org.junit.Assert.*;

public class ProvisioningCalculatorTest extends DefaultProvisioningTest {

    private static final int PRECISION = 4;
    private static final double PRICE = 1.12d;
    private ProvisioningCalculator calculator;

    @Override
    protected String getConfigFileName() {
        return TEST_CONFIG_PROPS;
    }

    @Test
    public void testLRLPChanges() throws Exception {
        FIProvision fiProvision = getFIProvision();

        Integer cpIndex = getCPIdx(fiProvision);

        LPProvision lp = getLP(fiProvision);

        getCalculator(fiProvision, cpIndex, lp);

        assertEquals(0x7F, calculator.getActive());

        calculator.deActivateForLRLPUpdate();
        assertEquals(0x7E, calculator.getActive());

        calculator.activateForLRLPUpdate();
        assertEquals(0x7F, calculator.getActive());

        calculator.deActivateForStreamStatusUpdate();
        assertEquals(0x7D, calculator.getActive());

        calculator.activateForStreamStatusUpdate();
        assertEquals(0x7F, calculator.getActive());

        // deactivate both stream status and LR_LP bits
        calculator.deActivateForStreamStatusUpdate();
        calculator.deActivateForLRLPUpdate();

        assertEquals(0x7C, calculator.getActive());

        //activate only stream status
        calculator.activateForStreamStatusUpdate();
        assertEquals(0x7E, calculator.getActive());

        calculator.activateForLRLPUpdate();
        assertEquals(0x7F, calculator.getActive());

        // disable both
        calculator.deActivateForLRLPUpdate();
        calculator.deActivateForStreamStatusUpdate();

        //activate only LR_LP update
        calculator.activateForLRLPUpdate();
        assertEquals(0x7D, calculator.getActive());

        calculator.activateForStreamStatusUpdate();
        assertEquals(0x7F, calculator.getActive());
    }

    @Test
    public void testCreditCalculationForMultiTier() throws Exception {
        FIProvision fiProvision = getFIProvision();

        Integer cpIndex = getCPIdx(fiProvision);

        LPProvision lp = getLP(fiProvision);

        getCalculator(fiProvision, cpIndex, lp);

        int noOfTiers = 3;
        QuoteC quote = getQuote(cpIndex, noOfTiers);
        quote.stampReceivedTime();
        System.out.println("quote.bidtierOffset = " + quote.bidTiersOffset());
        System.out.println("quote.OffertierOffset = " + quote.offerTiersOffset());
        System.out.println("quote.QuoteCreatedTimeOffset = " + quote.getQCTOffset());

        ProvisionedQuoteC provisionedQuote = new ProvisionedQuoteC();
        provisionedQuote.readFrom(quote.buffer());

        assertEquals(quote.getQuoteCreatedTime(),provisionedQuote.getQuoteCreatedTime());

        assertProvisionedQuote(noOfTiers, quote, provisionedQuote);

        Tuple<Boolean, Long> value = new Tuple<Boolean, Long>(true, 19000l);
        calculator.creditInfo.put(provisionedQuote.getValueDate(), value);
        calculator.applyCredit(provisionedQuote, value);

        //raw quote remains unchanged
        assertProvisionedQuote(noOfTiers, quote, provisionedQuote);

        //assert For Credit application

        assertEquals(2, provisionedQuote.getPBidTiersNum());
        assertEquals(2, provisionedQuote.getPOfferTiersNum());

        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        assertEquals(10000.00, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 1);
        assertEquals(19000.00, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(10000.00, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 1);
        assertEquals(19000.00, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

    }

    protected void getCalculator(FIProvision fiProvision, Integer cpIndex,
                                 LPProvision lp) {
        calculator = new ProvisioningCalculator("Test", "Test", lp, fiProvision, Util.getBaseCurrencyIndex(cpIndex),
                Util.getVarCurrencyIndex(cpIndex), rawRatePool, PRECISION, -1, true);
    }

    @Test
    public void testCreditCalculationForMultiQuote() throws Exception {
        FIProvision fiProvision = getFIProvision();

        Integer cpIndex = getCPIdx(fiProvision);

        LPProvision lp = getLP(fiProvision);

        getCalculator(fiProvision, cpIndex, lp);

        int noOfTiers = 5;
        QuoteC quote = getQuote(cpIndex, noOfTiers);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);


        //make copy
        QuoteC local = rawRatePool.borrowObject();
        local.readFromArray(quote.buffer());

        calculator.creditInfo.put(local.getValueDate(), new Tuple<Boolean, Long>(true, 39000l));

        calculator.rawRate.set(local);

        ProvisionedQuoteC provisionedQuote = calculator.getProvisionedRate();

        //raw quote remains unchanged
        assertProvisionedQuote(noOfTiers, quote, provisionedQuote);

        //assert For Credit application

        assertEquals(3, provisionedQuote.getPBidTiersNum());
        assertEquals(3, provisionedQuote.getPOfferTiersNum());

        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        assertEquals(10000.00, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 1);
        assertEquals(20000.00, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 2);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        //assertion for multi quote
        assertEquals(9000.00, provisionedQuote.getPShowQty(bidTierOffset), 0.01);


        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(10000.00, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 1);
        assertEquals(20000.00, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 2);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);
        //assertion for multi quote
        assertEquals(9000.00, provisionedQuote.getPShowQty(offerTierOffset), 0.01);

        assertTrue(provisionedQuote.isActive());
    }


    @Test
    public void testCreditCalculationForZeroTiers() throws Exception {
        FIProvision fiProvision = getFIProvision();

        Integer cpIndex = getCPIdx(fiProvision);

        LPProvision lp = getLP(fiProvision);

        getCalculator(fiProvision, cpIndex, lp);

        QuoteC quote = getQuote(cpIndex, 0);

        //make copy
        QuoteC local = rawRatePool.borrowObject();
        local.readFromArray(quote.buffer());

        calculator.creditInfo.put(local.getValueDate(), new Tuple<Boolean, Long>(true, 1000000l));

        calculator.rawRate.set(local);

        ProvisionedQuoteC provisionedQuote = calculator.getProvisionedRate();

        //assert For Credit application

        assertEquals(0, provisionedQuote.getPBidTiersNum());
        assertEquals(0, provisionedQuote.getPOfferTiersNum());

        assertFalse(provisionedQuote.isActive());
    }

    @Test
    public void testCreditCalculationForQuoteWithZeroLimits() throws Exception {
        FIProvision fiProvision = getFIProvision();

        Integer cpIndex = getCPIdx(fiProvision);

        LPProvision lp = getLP(fiProvision);

        getCalculator(fiProvision, cpIndex, lp);

        QuoteC quote = getQuote(cpIndex, 1, 0.00);

        //make copy
        QuoteC local = rawRatePool.borrowObject();
        local.readFromArray(quote.buffer());

        calculator.creditInfo.put(local.getValueDate(), new Tuple<Boolean, Long>(true, 1000000l));

        calculator.rawRate.set(local);

        ProvisionedQuoteC provisionedQuote = calculator.getProvisionedRate();

        //assert For Credit application

        assertEquals(1, provisionedQuote.getPBidTiersNum());
        assertEquals(1, provisionedQuote.getPOfferTiersNum());

        assertFalse(provisionedQuote.isActive());
    }


    @Test
    public void testCreditCalculationForZeroCredit() throws Exception {
        FIProvision fiProvision = getFIProvision();

        Integer cpIndex = getCPIdx(fiProvision);

        LPProvision lp = getLP(fiProvision);

        getCalculator(fiProvision, cpIndex, lp);

        int noOfTiers = 3;
        QuoteC quote = getQuote(cpIndex, noOfTiers);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);

        //make copy
        QuoteC local = rawRatePool.borrowObject();
        local.readFromArray(quote.buffer());

        calculator.creditInfo.put(local.getValueDate(), new Tuple<Boolean, Long>(true, 0l));

        calculator.rawRate.set(local);

        ProvisionedQuoteC provisionedQuote = calculator.getProvisionedRate();

        //assert For Credit application

        assertEquals(1, provisionedQuote.getPBidTiersNum());
        assertEquals(1, provisionedQuote.getPOfferTiersNum());

        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        assertEquals(0.00, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(0.00, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        assertFalse(provisionedQuote.isActive());
    }


    @Test
    public void testCreditUpdateShouldUpdateFlagAndLimit() throws Exception {
        FIProvision fiProvision = getFIProvision();

        Integer cpIndex = getCPIdx(fiProvision);
        LPProvision lp = getLP(fiProvision);

        short valueDate = 233;
        getCalculator(fiProvision, cpIndex, lp);

        Tuple<Boolean, Long> creditLimitInfo = new Tuple<Boolean, Long>(true, 39000l);
        calculator.creditInfo.put(valueDate, creditLimitInfo);

        creditLimitInfo.second = 0l;
        creditLimitInfo.first = false;

        CreditLimitInfo info = new CreditLimitInfo(1l, 1l, valueDate);
        info.setCreditStatus((byte) 1);
        info.setDailyAvailable(1000000l);
        info.setAggregateAvailable(800000l);

        calculator.onCredit(info);

        assertTrue(creditLimitInfo.first);
        //checking for min function
        assertEquals(Long.valueOf(800000l), creditLimitInfo.second);
        assertTrue(calculator.isCreditModified.get());

        calculator.isCreditModified.set(false);
        info.setCreditStatus((byte) 0);
        info.setDailyAvailable(600000l);
        info.setAggregateAvailable(800000l);
        calculator.onCredit(info);
        assertFalse(creditLimitInfo.first);
        //checking for min function
        assertEquals(Long.valueOf(800000l), creditLimitInfo.second);
        assertTrue(calculator.isCreditModified.get());

        calculator.isCreditModified.set(false);
        info.setCreditStatus((byte) 1);
        info.setDailyAvailable(600000l);
        info.setAggregateAvailable(800000l);
        calculator.onCredit(info);
        assertTrue(creditLimitInfo.first);
        //checking for min function
        assertEquals(Long.valueOf(600000l), creditLimitInfo.second);
        assertTrue(calculator.isCreditModified.get());


        //no change to limits
        calculator.isCreditModified.set(false);
        info.setDailyAvailable(600000l);
        info.setAggregateAvailable(600000l);
        calculator.onCredit(info);
        assertTrue(creditLimitInfo.first);
        //checking for min function
        assertEquals(Long.valueOf(600000l), creditLimitInfo.second);
        assertFalse(calculator.isCreditModified.get());

        //change only limits
        calculator.isCreditModified.set(false);
        info.setCreditStatus((byte) 1);
        info.setDailyAvailable(600000l);
        info.setAggregateAvailable(400000l);
        calculator.onCredit(info);
        assertTrue(creditLimitInfo.first);
        //checking for min function
        assertEquals(Long.valueOf(400000l), creditLimitInfo.second);
        assertTrue(calculator.isCreditModified.get());
        calculator.isCreditModified.set(false);
    }

    @Test
    public void testProvisionedQuoteWithCreditDisabled() throws Exception {
        FIProvision fiProvision = getFIProvision();

        Integer cpIndex = getCPIdx(fiProvision);
        LPProvision lp = getLP(fiProvision);
        getCalculator(fiProvision, cpIndex, lp);

        int noOfTiers = 5;
        QuoteC quote = getQuote(cpIndex, noOfTiers);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        //make copy
        QuoteC local = rawRatePool.borrowObject();
        local.readFromArray(quote.buffer());

        calculator.creditInfo.put(local.getValueDate(), new Tuple<Boolean, Long>(false, 39000l));

        calculator.rawRate.set(local);

        ProvisionedQuoteC provisionedQuote = calculator.getProvisionedRate();

        //raw quote remains unchanged
        assertProvisionedQuote(noOfTiers, quote, provisionedQuote);

        //assert For Credit application

        assertEquals(5, provisionedQuote.getPBidTiersNum());
        assertEquals(5, provisionedQuote.getPOfferTiersNum());

        int bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0);
        assertEquals(10000.00, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 1);
        assertEquals(20000.00, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 2);
        assertEquals(40000.00, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 3);
        assertEquals(80000.00, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);

        bidTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 4);
        assertEquals(160000.00, provisionedQuote.getPShowQty(bidTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(bidTierOffset), 0.01);


        int offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0);
        assertEquals(10000.00, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 1);
        assertEquals(20000.00, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 2);
        assertEquals(40000.00, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 3);
        assertEquals(80000.00, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.01);

        offerTierOffset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 4);
        assertEquals(160000.00, provisionedQuote.getPShowQty(offerTierOffset), 0.01);
        assertEquals(PRICE, provisionedQuote.getPPrice(offerTierOffset), 0.1);
    }


    @Test
    public void testProvisionQuoteWithVarryingTiers() throws Exception {
        FIProvision fiProvision = getFIProvision();

        Integer cpIndex = getCPIdx(fiProvision);

        LPProvision lp = getLP(fiProvision);

        getCalculator(fiProvision, cpIndex, lp);

        int noOfTiers = 5;
        QuoteC quote = getQuote(cpIndex, noOfTiers);

        //make copy
        QuoteC local = rawRatePool.borrowObject();
        local.readFromArray(quote.buffer());
        calculator.rawRate.set(local);

        calculator.creditInfo.put(local.getValueDate(), new Tuple<Boolean, Long>(true, 39000l));

        ProvisionedQuoteC provisionedQuote = calculator.getProvisionedRate();

        assertEquals(3, provisionedQuote.getPBidTiersNum());
        assertEquals(3, provisionedQuote.getPOfferTiersNum());

        assertEquals(noOfTiers, provisionedQuote.getBidTiersNum());
        assertEquals(noOfTiers, provisionedQuote.getOfferTiersNum());

        checkForQuoteTiersShrinking(cpIndex);

        checkForQuoteTiersExpanding(cpIndex);
    }


    @Test
    public void testProvisionQuoteWithHighPrecisionPrices() throws Exception {
        FIProvision fiProvision = getFIProvision();

        Integer cpIndex = getCPIdx(fiProvision);

        LPProvision lp = getLP(fiProvision);

        getCalculator(fiProvision, cpIndex, lp);

        int noOfTiers = 1;
        QuoteC quote = getQuote(cpIndex, noOfTiers, 10000.00, 1.1234345d, 1.1234345);

        //make copy
        QuoteC local = rawRatePool.borrowObject();
        local.readFromArray(quote.buffer());
        calculator.rawRate.set(local);
        calculator.creditInfo.put(local.getValueDate(), new Tuple<Boolean, Long>(true, 39000l));

        ProvisionedQuoteC provisionedQuote = calculator.getProvisionedRate();

        assertEquals(1, provisionedQuote.getPBidTiersNum());
        assertEquals(1, provisionedQuote.getPOfferTiersNum());

        assertEquals(noOfTiers, provisionedQuote.getBidTiersNum());
        assertEquals(noOfTiers, provisionedQuote.getOfferTiersNum());

        assertEquals(1.1234d, provisionedQuote.getPrice(provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, 0)),
                0.0000001);
        assertEquals(1.1235d, provisionedQuote.getPrice(provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, 0)),
                0.0000001);
    }

    private void checkForQuoteTiersExpanding(Integer cpIndex) {
        ProvisionedQuoteC provisionedQuote;
        int noOfTiers = 7;
        QuoteC quote = getQuote(cpIndex, noOfTiers);
        QuoteC local = rawRatePool.borrowObject();
        local.readFromArray(quote.buffer());
        calculator.rawRate.set(local);

        provisionedQuote = calculator.getProvisionedRate();
        assertEquals(3, provisionedQuote.getPBidTiersNum());
        assertEquals(3, provisionedQuote.getPOfferTiersNum());

        assertEquals(noOfTiers, provisionedQuote.getBidTiersNum());
        assertEquals(noOfTiers, provisionedQuote.getOfferTiersNum());
    }

    protected void checkForQuoteTiersShrinking(Integer cpIndex) {
        ProvisionedQuoteC provisionedQuote;
        int noOfTiers = 2;
        QuoteC quote = getQuote(cpIndex, noOfTiers);
        QuoteC local = rawRatePool.borrowObject();
        local.readFromArray(quote.buffer());
        calculator.rawRate.set(local);

        provisionedQuote = calculator.getProvisionedRate();
        assertEquals(noOfTiers, provisionedQuote.getPBidTiersNum());
        assertEquals(noOfTiers, provisionedQuote.getPOfferTiersNum());

        assertEquals(noOfTiers, provisionedQuote.getBidTiersNum());
        assertEquals(noOfTiers, provisionedQuote.getOfferTiersNum());
    }


    protected LPProvision getLP(FIProvision fiProvision) {
        Iterator<LPProvision> iterator2 = fiProvision.getLPProvisions().iterator();
        assertTrue(iterator2.hasNext());
        LPProvision lp = iterator2.next();
        return lp;
    }

    protected Integer getCPIdx(FIProvision fiProvision) {
        Iterator<Integer> iterator = fiProvision.getSupportedCcyPairs().iterator();
        assertTrue(iterator.hasNext());
        Integer cpIndex = iterator.next();
        return cpIndex;
    }

    protected FIProvision getFIProvision() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI2);

        FIProvision fiProvision = serverProvision.getFIProvision(1002);
        assertNotNull(fiProvision);
        return fiProvision;
    }

    protected void assertProvisionedQuote(int noOfTiers, QuoteC quote,
                                          ProvisionedQuoteC provisionedQuote) {

        assertEquals(noOfTiers, provisionedQuote.getBidTiersNum());
        assertEquals(noOfTiers, provisionedQuote.getOfferTiersNum());

        double cBLimit = 10000.00;
        double cOLimit = 10000.00;
        for (int i = 0; i < noOfTiers; i++) {
            int bidTierOffset = provisionedQuote.tierOffset(ProvisionedQuoteC.BUY, i);
            assertEquals(cBLimit, quote.getTotalQty(bidTierOffset), 0.01);
            assertEquals(cBLimit, quote.getShowQty(bidTierOffset), 0.01);
            assertEquals(PRICE, quote.getPrice(bidTierOffset), 0.01);

            int offerTierOffset = provisionedQuote.tierOffset(ProvisionedQuoteC.SELL, i);
            assertEquals(cOLimit, quote.getTotalQty(offerTierOffset), 0.01);
            assertEquals(cOLimit, quote.getShowQty(offerTierOffset), 0.01);
            assertEquals(PRICE, quote.getPrice(offerTierOffset), 0.01);
            cBLimit += cBLimit;
            cOLimit = cOLimit * 2;
        }
    }

    protected QuoteC getQuote(Integer cpIndex, int noOfTiers) {
        return this.getQuote(cpIndex, noOfTiers, 10000.00);
    }

    protected QuoteC getQuote(Integer cpIndex, int noOfTiers, double limit) {
        return getQuote(cpIndex, noOfTiers, limit, PRICE, PRICE);
    }

    protected QuoteC getQuote(Integer cpIndex, int noOfTiers, double limit, double bidPrice, double OfferPrice) {
        QuoteC quote = new QuoteC();
        quote.setCategory(1);
        quote.setCcyPairIdx(cpIndex);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setBidTiersNum(noOfTiers);
        quote.setOfferTiersNum(noOfTiers);
        quote.setValueDate((short) 17500);
        quote.setQuoteCreatedTime(System.currentTimeMillis());

        double bLimit = limit;
        double oLimit = limit;
        for (int i = 0; i < noOfTiers; i++) {
            int bidTierOffset = quote.tierOffset(ProvisionedQuoteC.BUY, i);
            quote.setPrice(bidTierOffset, bidPrice);
            quote.setTotalQty(bidTierOffset, bLimit);
            quote.setShowQty(bidTierOffset, bLimit);

            int offerTierOffset = quote.tierOffset(ProvisionedQuoteC.SELL, i);
            quote.setPrice(offerTierOffset, OfferPrice);
            quote.setTotalQty(offerTierOffset, oLimit);
            quote.setShowQty(offerTierOffset, oLimit);

            bLimit += bLimit;
            oLimit = oLimit * 2;
        }
        return quote;
    }


    public void applyCreditC(ProvisioningCalculator calculator, CreditLimitInfo info,ProvisionedQuoteC provisionedQuote) {

        Tuple<Boolean, Long> credit = new Tuple<Boolean, Long>(true, (long) 5000);
        long daily = info.getDailyAvailable();
        long agg = info.getAggregateAvailable();

        if(daily < agg)
            credit.second = daily;
        else
            credit.second = agg;

        calculator.applyCredit(provisionedQuote, credit);
    }

}
