package com.integral.mdf.rate;

import com.integral.mdf.data.*;
import com.integral.provision.MDFAggregationType;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map.Entry;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;

public class TestFBA extends PriceAggregationTest {

    public static final String LP22 = "lp2";
    public static final String LP12 = "lp1";
    public static final String LP32 = "lp3";
    public static final String LP41 = "lp4";
    public static final String LP51 = "lp5";
    public static final String LP61 = "lp6";
    public static final String LP71 = "lp7";
    public static final String LP81 = "lp8";
    public static final String LP91 = "lp9";
    public static final String LP10 = "lp10";
    public static final String LP11 = "lp11";
    public static final String EUR_USD = "EUR/USD";

    @Test
    public void testFBAFromSQLPs () throws Exception {

        System.out.println("===== START : testFBAFromSQLPs =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testFBAFromSQLPs ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : testFBAFromSQLPs =====");
    }


    @Test
    public void testFBAFromSQLPCausingInvertedTiers () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote

        System.out.println("===== START : testFBAFromSQLPCausingInvertedTiers =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.19, 1.20);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testFBAFromSQLPCausingInvertedTiers ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.19, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : testFBAFromSQLPCausingInvertedTiers =====");

    }

    @Test
    public void testFBA6LPsLastQuoteCausingInvertedTiers () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote

        System.out.println("===== START : testFBAFromSQLPCausingInvertedTiers6LPs =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //Last quote causes the book to be inverted and this quote which is oldest will be removed
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP61, fiProvision, 1, 1.1888, 1.1890);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP6 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.1881, 1.1891);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1,1.1882, 1.1890);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP2 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 1, 1.1883, 1.1889);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP41, fiProvision, 1, 1.1884, 1.1888);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP4 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP51, fiProvision, 1, 1.1885, 1.1887);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP5 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====aggregate quote in testFBAFromSQLP6LPs ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.1885, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.00001);
        assertEquals(1.1884, aggregate.getBidPrices()[1], 0.000001);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.00001);
        assertEquals(1.1883, aggregate.getBidPrices()[2], 0.000001);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.00001);
        assertEquals(1.1882, aggregate.getBidPrices()[3], 0.000001);
        assertEquals(1000.00, aggregate.getBidQtys()[3], 0.00001);
        assertEquals(1.1881, aggregate.getBidPrices()[4], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[4], 0.00001);
        assertEquals(5, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.1887, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(1.1888, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(1.1889, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(1.189, aggregate.getOfferPrices()[3], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[3], 0.00001);
        assertEquals(1.1891, aggregate.getOfferPrices()[4], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[4], 0.00001);
        assertEquals(5, aggregate.getNumOffers());

        System.out.println("===== END : testFBAFromSQLPCausingInvertedTiers6LPs =====");

    }

    // Not working
    @Test
    public void testFBAWithLPsEqualToMaxAggregationAttempts () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote
        System.out.println("===== START : testFBAWithLPsEqualToMaxAggregationAttempts =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);
        //ServerProvisionImpl serverprovisionImpl = new ServerProvisionImpl();
        serverProvision.setMaxAggregationAttempts(10);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.1881, 1.1897);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1,1.1882, 1.1896);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 1, 1.1883, 1.1895);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP41, fiProvision, 1, 1.1884, 1.1894);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP4 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP51, fiProvision, 1, 1.1885, 1.1893);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP5 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP61, fiProvision, 1, 1.1886, 1.1892);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP6 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP71, fiProvision, 1, 1.1885, 1.1892);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP7 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP81, fiProvision, 1, 1.1884, 1.1891);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP81 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP91, fiProvision, 1, 1.1883, 1.1890);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP91 quote==="+quote.toString());


        String  bidNoLPs = "bidNoLPs[1, 2, 2, 2, 1, 1, 0, 0, 0, 0]";
        String  offerNoLPs = "offerNoLPs[1, 1, 2, 1, 1, 1, 1, 1, 0, 0]";
        String  bidPrices = "bidPrices=[1.1886, 1.1885, 1.1884, 1.1883, 1.1882, 1.1881, 0.0, 0.0, 0.0, 0.0]";
        String  bidQtys = "bidQtys=[1000.0, 2000.0, 2000.0, 2000.0, 1000.0, 1000.0, 0.0, 0.0, 0.0, 0.0]";
        String  offerPrices="offerPrices=[1.189, 1.1891, 1.1892, 1.1893, 1.1894, 1.1895, 1.1896, 1.1897, 0.0, 0.0]";
        String  offerQtys = "offerQtys=[1000.0, 1000.0, 2000.0, 1000.0, 1000.0, 1000.0, 1000.0, 1000.0, 0.0, 0.0]";

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====1 aggregate quote in testFBAWithLPsMoreThanmaxAggregationAttempts ==="+aggregate.toString());
        assertEquals(6, aggregate.getNumBids());
        assertEquals(8, aggregate.getNumOffers());
        String aggString =  aggregate.toString();
        System.out.println("aggString"+aggString);
        int startindex=0;
        int lastindex=0;

        startindex = aggString.lastIndexOf("bidPrices");
        lastindex = aggString.indexOf("],",startindex);
        String bidPricesActual = aggString.substring(startindex,lastindex+1);

        startindex = aggString.lastIndexOf("bidQtys");
        lastindex = aggString.indexOf("],",startindex);
        String bidQtysActual = aggString.substring(startindex,lastindex+1);

        startindex = aggString.lastIndexOf("offerPrices");
        lastindex = aggString.indexOf("],",startindex);
        String offerPricesActual = aggString.substring(startindex,lastindex+1);

        startindex = aggString.lastIndexOf("offerQtys");
        lastindex = aggString.indexOf("],",startindex);
        String offerQtysActual = aggString.substring(startindex,lastindex+1);

        startindex = aggString.lastIndexOf("bidNoLPs");
        lastindex = aggString.indexOf("],",startindex);
        String bidNoLPsactual= aggString.substring(startindex,lastindex+1);

        startindex = aggString.lastIndexOf("offerNoLPs");
        lastindex = aggString.indexOf("],",startindex);
        String offerNoLpsactual = aggString.substring(startindex,lastindex+1);

        System.out.println("bidNoLPsactual aggregate\n" +bidNoLPsactual);
        System.out.println("offerNoLpsactual aggregate\n" +offerNoLpsactual);

        assertEquals(bidNoLPsactual, bidNoLPs);
        assertEquals(offerNoLpsactual, offerNoLPs);
        assertEquals(bidPricesActual, bidPrices);
        assertEquals(bidQtysActual, bidQtys);
        assertEquals(offerPricesActual, offerPrices);
        assertEquals(offerQtysActual, offerQtys);

        //Last quote causes the book to be inverted and this quote which is oldest will be removed
        quote = getQuoteC(serverProvision, EUR_USD, LP10, fiProvision, 1, 1.1891, 1.1892);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP10 quote==="+quote.toString());
        PriceBook aggregate1 = book1.aggregate();
        System.out.println("=====2 aggregate quote in testFBAWithLPsMoreThanmaxAggregationAttempts 1 ==="+aggregate1.toString());

        String bidNoLPs1 = "bidNoLPs[1, 1, 2, 2, 1, 1, 1, 0, 0, 0]";
        String offerNoLPs1 = "offerNoLPs[1, 3, 1, 1, 1, 1, 1, 0, 0, 0]";
        String  bidPrices1 = "bidPrices=[1.1891, 1.1886, 1.1885, 1.1884, 1.1883, 1.1882, 1.1881, 0.0, 0.0, 0.0]";
        String  bidQtys1 = "bidQtys=[1000.0, 1000.0, 2000.0, 2000.0, 1000.0, 1000.0, 1000.0, 0.0, 0.0, 0.0]";
        String  offerPrices1 ="offerPrices=[1.1891, 1.1892, 1.1893, 1.1894, 1.1895, 1.1896, 1.1897, 0.0, 0.0, 0.0]";
        String  offerQtys1 = "offerQtys=[1000.0,3000.0, 1000.0, 1000.0, 1000.0, 1000.0, 1000.0, 0.0, 0.0, 0.0]";
        System.out.println("==========offerNoLPs"+offerNoLPs1);

        assertEquals(7, aggregate1.getNumBids());
        assertEquals(7, aggregate1.getNumOffers());
        aggString =  aggregate1.toString();
        System.out.println("aggString"+aggString);
        startindex = aggString.lastIndexOf("bidNoLPs");
        lastindex = aggString.indexOf("],",startindex);

        System.out.println("========= aggString ==" + aggString);
        String bidNoLPsactual1= aggString.substring(startindex,lastindex+1);
        startindex = aggString.lastIndexOf("offerNoLPs");
        lastindex = aggString.indexOf("],",startindex);
        String  offerNoLpsactual1 = aggString.substring(startindex,lastindex+1);
        System.out.println("bidNoLPsactual aggregate\n" +bidNoLPsactual);
        System.out.println("offerNoLpsactual aggregate\n" +offerNoLpsactual);

        startindex = aggString.lastIndexOf("bidPrices");
        lastindex = aggString.indexOf("],",startindex);
        String bidPricesActual1 = aggString.substring(startindex,lastindex+1);

        startindex = aggString.lastIndexOf("bidQtys");
        lastindex = aggString.indexOf("],",startindex);
        String bidQtysActual1 = aggString.substring(startindex,lastindex+1);

        startindex = aggString.lastIndexOf("offerPrices");
        lastindex = aggString.indexOf("],",startindex);
        String offerPricesActual1 = aggString.substring(startindex,lastindex+1);

        startindex = aggString.lastIndexOf("offerQtys");
        lastindex = aggString.indexOf("],",startindex);
        String offerQtysActual1 = aggString.substring(startindex,lastindex+1);

        System.out.println("==========offerNoLPs"+offerNoLPs1);
        System.out.println("==========offerNoLpsactual1==="+offerNoLpsactual1);

        assertEquals(bidNoLPs1, bidNoLPsactual1);
        assertEquals(offerNoLPs1, offerNoLpsactual1);
        assertEquals(bidPrices1, bidPricesActual1);
        assertEquals(bidQtys1, bidQtysActual1);
        assertEquals(offerPrices1, offerPricesActual1);
        assertEquals(offerQtys1, offerQtysActual1);

        //Latest quote should have been displayed after removing old quotes in 10 iterations
        assertEquals(1, aggregate1.getNumBids());
        assertEquals(1, aggregate1.getNumOffers());
        assertEquals(1.1891, aggregate1.getBidPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate1.getBidQtys()[0], 0.00001);
        assertEquals(1.1892, aggregate1.getOfferPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate1.getOfferQtys()[0], 0.00001);
        System.out.println("===== END : testFBAFromSQLPCausingInvertedTiers6LPs =====");

    }

    // Not working
    @Test
    public void testFBAWithLPsMoreThanMaxAggregationAttempts () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote

        System.out.println("===== START : testFBAFromSQLPCausingInvertedTiers6LPs =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);
        serverProvision.setMaxAggregationAttempts(12);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.1881, 1.1897);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1,1.1882, 1.1896);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP2 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 1, 1.1883, 1.1895);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP3 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP41, fiProvision, 1, 1.1884, 1.1894);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP4 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP51, fiProvision, 1, 1.1885, 1.1893);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP5 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP61, fiProvision, 1, 1.1886, 1.1892);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP6 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP71, fiProvision, 1, 1.1885, 1.1892);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP7 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP81, fiProvision, 1, 1.1884, 1.1891);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP81 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP91, fiProvision, 1, 1.1883, 1.1890);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP91 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP10, fiProvision, 1, 1.1882, 1.1886);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP10 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====aggregate quote in testFBAWithLPsMoreThanmaxAggregationAttempts 1 ==="+aggregate.toString());

        assertEquals(6, aggregate.getNumBids());
        assertEquals(9, aggregate.getNumOffers());

        //Last quote causes the book to be inverted and this quote which is oldest will be removed
        quote = getQuoteC(serverProvision, EUR_USD, LP11, fiProvision, 1, 1.1888, 1.1890);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP11 quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("=====aggregate quote in testFBAWithLPsMoreThanmaxAggregationAttempts ==="+aggregate.toString());

        // Actual result
        //bidPrices=[1.1886, 1.1885, 1.1884, 1.1883, 1.1882, 1.1881, 0.0, 0.0, 0.0, 0.0],
        // bidQtys=[1000.0, 2000.0, 2000.0, 2000.0, 2000.0, 1000.0, 0.0, 0.0, 0.0, 0.0],
        // bidNoLPs[1, 2, 2, 2, 2, 1, 0, 0, 0, 0],
        // offerPrices=[1.1886, 1.189, 1.1891, 1.1892, 1.1893, 1.1894, 1.1895, 1.1896, 1.1897, 0.0],
        // offerQtys=[1000.0, 1000.0, 1000.0, 2000.0, 1000.0, 1000.0, 1000.0, 1000.0, 1000.0, 0.0],
        // offerNoLPs[1, 1, 1, 2, 1, 1, 1, 1, 1, 0],
        //Latest quote should have been displayed after removing old quotes in 10 iterations

        assertEquals(7, aggregate.getNumBids());
        assertEquals(8, aggregate.getNumOffers());
        assertEquals(1.1888, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.00001);
        assertEquals(1.1886, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.00001);
        assertEquals(1.1885, aggregate.getBidPrices()[2], 0.00001);
        assertEquals(2000.00, aggregate.getBidQtys()[2], 0.00001);
        assertEquals(1.1884, aggregate.getBidPrices()[3], 0.00001);
        assertEquals(2000.00, aggregate.getBidQtys()[3], 0.00001);
        assertEquals(1.1883, aggregate.getBidPrices()[4], 0.00001);
        assertEquals(2000.00, aggregate.getBidQtys()[4], 0.00001);
        assertEquals(1.1882, aggregate.getBidPrices()[5], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[5], 0.00001);
        assertEquals(1.1881, aggregate.getBidPrices()[6], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[6], 0.00001);

        assertEquals(1.189, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(1.1891, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(1.1892, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(2000.00, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(1.1893, aggregate.getOfferPrices()[3], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[3], 0.00001);
        assertEquals(1.1894, aggregate.getOfferPrices()[4], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[4], 0.00001);
        assertEquals(1.1895, aggregate.getOfferPrices()[5], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[5], 0.00001);
        assertEquals(1.1896, aggregate.getOfferPrices()[6], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[6], 0.00001);
        assertEquals(1.1897, aggregate.getOfferPrices()[7], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[7], 0.00001);

        System.out.println("===== END : testFBAFromSQLPCausingInvertedTiers6LPs =====");

    }

    // Not working
    @Test
    public void testFBAFromSQLPCausingInvertedTiers6LPs () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote

        System.out.println("===== START : testFBAFromSQLPCausingInvertedTiers6LPs =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.1881, 1.1891);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1,1.1882, 1.1890);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 1, 1.1883, 1.1889);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP41, fiProvision, 1, 1.1884, 1.1888);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP4 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP51, fiProvision, 1, 1.1885, 1.1887);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP5 quote==="+quote.toString());

        //Last quote causes the book to be inverted
        quote = getQuoteC(serverProvision, EUR_USD, LP61, fiProvision, 1, 1.1888, 1.1890);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP6 quote==="+quote.toString());
        PriceBook aggregate = book1.aggregate();
        System.out.println("=====aggregate quote in testFBAFromSQLP6LPs ==="+aggregate.toString());

        //Actual result
        // bidPrices=[1.1885, 1.1884, 1.1883, 1.1882, 1.1881, 0.0, 0.0, 0.0, 0.0, 0.0],
        // bidQtys=[1000.0, 1000.0, 1000.0, 1000.0, 1000.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        // bidNoLPs[1, 1, 1, 1, 1, 0, 0, 0, 0, 0],
        // offerPrices=[1.1887, 1.1888, 1.1889, 1.189, 1.1891, 0.0, 0.0, 0.0, 0.0, 0.0],
        // offerQtys=[1000.0, 1000.0, 1000.0, 1000.0, 1000.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        // offerNoLPs[1, 1, 1, 1, 1, 0, 0, 0, 0, 0],

        //assert For bid price
        assertEquals(1.1888, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.0001);
        assertEquals(1.1884, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.0001);
        assertEquals(1.1883, aggregate.getBidPrices()[2], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.0001);
        assertEquals(1.1882, aggregate.getBidPrices()[3], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[3], 0.0001);
        assertEquals(1.1881, aggregate.getBidPrices()[4], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[4], 0.0001);
        assertEquals(5, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.1888, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(1.1889, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(1.189, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(2000.00, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(1.1891, aggregate.getOfferPrices()[3], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[3], 0.00001);
        assertEquals(4, aggregate.getNumOffers());

        System.out.println("===== END : testFBAFromSQLPCausingInvertedTiers6LPs =====");

    }

    // Not working
    @Test
    public void testFBAFromSQLPLastRateCausingInvertedTiers6LPs () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote

        System.out.println("===== START : testFBAFromSQLPCausingInvertedTiers6LPs =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.1881, 1.1891);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1,1.1882, 1.1890);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 1, 1.1883, 1.1889);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP41, fiProvision, 1, 1.1884, 1.1888);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP4 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP51, fiProvision, 1, 1.1885, 1.1887);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP5 quote==="+quote.toString());
        PriceBook aggregate = book1.aggregate();
        System.out.println("=====1 aggregate quote in testFBAFromSQLP6LPs ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.1885, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.00001);
        assertEquals(1.1884, aggregate.getBidPrices()[1], 0.000001);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.00001);
        assertEquals(1.1883, aggregate.getBidPrices()[2], 0.000001);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.00001);
        assertEquals(1.1882, aggregate.getBidPrices()[3], 0.000001);
        assertEquals(1000.00, aggregate.getBidQtys()[3], 0.00001);
        assertEquals(1.1881, aggregate.getBidPrices()[4], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[4], 0.00001);
        assertEquals(5, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.1887, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(1.1888, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(1.1889, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(1.189, aggregate.getOfferPrices()[3], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[3], 0.00001);
        assertEquals(1.1891, aggregate.getOfferPrices()[4], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[4], 0.00001);
        assertEquals(5, aggregate.getNumOffers());


        //Last quote causes the book to be inverted
        quote = getQuoteC(serverProvision, EUR_USD, LP61, fiProvision, 1, 1.1888, 1.1890);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP6 quote==="+quote.toString());
        aggregate = book1.aggregate();
        System.out.println("=====2 aggregate quote in testFBAFromSQLP6LPs ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.1888, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.00001);
        assertEquals(1.1884, aggregate.getBidPrices()[1], 0.000001);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.00001);
        assertEquals(1.1883, aggregate.getBidPrices()[2], 0.000001);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.00001);
        assertEquals(1.1882, aggregate.getBidPrices()[3], 0.000001);
        assertEquals(1000.00, aggregate.getBidQtys()[3], 0.00001);
        assertEquals(1.1881, aggregate.getBidPrices()[4], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[4], 0.00001);
        assertEquals(5, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.1888, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(1.1889, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(1.189, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(2000.00, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(1.1891, aggregate.getOfferPrices()[3], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[3], 0.00001);
        assertEquals(4, aggregate.getNumOffers());

        System.out.println("===== END : testFBAFromSQLPCausingInvertedTiers6LPs =====");

    }

    @Test
    public void testFBAFromSQLPLastRateCausingInvertedTiers6LPsBid () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote

        System.out.println("===== START : testFBAFromSQLPLastRateCausingInvertedTiers6LPsBid =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.1881, 1.1891);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1,1.1882, 1.1890);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 1, 1.1883, 1.1889);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP41, fiProvision, 1, 1.1884, 1.1888);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP4 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP51, fiProvision, 1, 1.1885, 1.1887);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP5 quote==="+quote.toString());
        PriceBook aggregate = book1.aggregate();
        System.out.println("=====1 aggregate quote in testFBAFromSQLPLastRateCausingInvertedTiers6LPsBid ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.1885, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.00001);
        assertEquals(1.1884, aggregate.getBidPrices()[1], 0.000001);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.00001);
        assertEquals(1.1883, aggregate.getBidPrices()[2], 0.000001);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.00001);
        assertEquals(1.1882, aggregate.getBidPrices()[3], 0.000001);
        assertEquals(1000.00, aggregate.getBidQtys()[3], 0.00001);
        assertEquals(1.1881, aggregate.getBidPrices()[4], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[4], 0.00001);
        assertEquals(5, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.1887, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(1.1888, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(1.1889, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(1.189, aggregate.getOfferPrices()[3], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[3], 0.00001);
        assertEquals(1.1891, aggregate.getOfferPrices()[4], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[4], 0.00001);
        assertEquals(5, aggregate.getNumOffers());

        //Last quote causes the book to be inverted
        quote = getQuoteC(serverProvision, EUR_USD, LP61, fiProvision, 1, 1.1881, 1.1884);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP6 quote==="+quote.toString());
        aggregate = book1.aggregate();
        System.out.println("=====2 aggregate quote in testFBAFromSQLPLastRateCausingInvertedTiers6LPsBid ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.1884, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.0001);
        assertEquals(1.1883, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.0001);
        assertEquals(1.1882, aggregate.getBidPrices()[2], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.0001);
        assertEquals(1.1881, aggregate.getBidPrices()[3], 0.00001);
        assertEquals(2000.00, aggregate.getBidQtys()[3], 0.0001);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.1884, aggregate.getOfferPrices()[0], 0.000001);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.000001);
        assertEquals(1.1888, aggregate.getOfferPrices()[1], 0.000001);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.000001);
        assertEquals(1.1889, aggregate.getOfferPrices()[2], 0.000001);
        assertEquals(1000.00, aggregate.getOfferQtys()[2], 0.000001);
        assertEquals(1.1890, aggregate.getOfferPrices()[3], 0.000001);
        assertEquals(1000.00, aggregate.getOfferQtys()[3], 0.000001);
        assertEquals(1.1891, aggregate.getOfferPrices()[4], 0.000001);
        assertEquals(1000.00, aggregate.getOfferQtys()[4], 0.000001);
        assertEquals(5, aggregate.getNumOffers());
        System.out.println("===== END : testFBAFromSQLPLastRateCausingInvertedTiers6LPsBid =====");

    }

    //first rate causing inverted tiers  testFBAFromSQLPFirstRateCausingInvertedTiers6LPs
    @Test
    public void testFBAFromSQLPOldestQuote () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote

        System.out.println("===== START : testFBAFromSQLPOldestQuote =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.1881, 1.1891);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1,1.1882, 1.1890);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 1, 1.1883, 1.1889);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP41, fiProvision, 1, 1.1884, 1.1888);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP4 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP51, fiProvision, 1, 1.1885, 1.1887);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP5 quote==="+quote.toString());
        PriceBook aggregate = book1.aggregate();
        System.out.println("=====1 aggregate quote in testFBAFromSQLP6LPs ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.1885, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.00001);
        assertEquals(1.1884, aggregate.getBidPrices()[1], 0.000001);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.00001);
        assertEquals(1.1883, aggregate.getBidPrices()[2], 0.000001);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.00001);
        assertEquals(1.1882, aggregate.getBidPrices()[3], 0.000001);
        assertEquals(1000.00, aggregate.getBidQtys()[3], 0.00001);
        assertEquals(1.1881, aggregate.getBidPrices()[4], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[4], 0.00001);
        assertEquals(5, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.1887, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(1.1888, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(1.1889, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(1.189, aggregate.getOfferPrices()[3], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[3], 0.00001);
        assertEquals(1.1891, aggregate.getOfferPrices()[4], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[4], 0.00001);
        assertEquals(5, aggregate.getNumOffers());


        //Last quote causes the book to be inverted
        quote = getQuoteC(serverProvision, EUR_USD, LP61, fiProvision, 1, 1.1888, 1.1890);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP6 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP51, fiProvision, 1, 1.1885, 1.1887);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP5 quote==="+quote.toString());
        aggregate = book1.aggregate();
        System.out.println("=====3 aggregate quote in testFBAFromSQLP6LPs ==="+aggregate.toString());


        //assert For bid price
        assertEquals(1.1885, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.00001);
        assertEquals(1.1884, aggregate.getBidPrices()[1], 0.000001);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.00001);
        assertEquals(1.1883, aggregate.getBidPrices()[2], 0.000001);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.00001);
        assertEquals(1.1882, aggregate.getBidPrices()[3], 0.000001);
        assertEquals(1000.00, aggregate.getBidQtys()[3], 0.00001);
        assertEquals(1.1881, aggregate.getBidPrices()[4], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[4], 0.00001);
        assertEquals(5, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.1887, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(1.1888, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(1.1889, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(1.189, aggregate.getOfferPrices()[3], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[3], 0.00001);
        assertEquals(1.1891, aggregate.getOfferPrices()[4], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[4], 0.00001);
        assertEquals(5, aggregate.getNumOffers());

        System.out.println("===== END : testFBAFromSQLPCausingInvertedTiers6LPs =====");

    }

    @Test
    public void testFBAFromSQLpGivingInvertedTiers () throws Exception {
        // Quote causing inverted quote should be dropped,
        //noOfTiers is 1 since MDF has to send quote with 0 rate and 1 tier size to represent inactive quote to MDG,
        //otherwise MDG will not be able to recognize inactive quote

        System.out.println("===== START : testFBAFromSQLpGivingInvertedTiers =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.24, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testFBAFromSQLpGivingInvertedTiers ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : testFBAFromSQLpGivingInvertedTiers =====");

    }



    @Test
    public void testFBAFromSQLpsGivingOneSidedBidRate () throws Exception {

        System.out.println("===== START : testFBAFromSQLpsGivingOneSidedBidRate =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testFBAFromSQLpsGivingOneSidedBidRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.001);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        //assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);

        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);
        //The book is sorted at the offer side as well
        assertEquals(0, aggregate.getOfferQtys()[0],0.001);
        assertEquals(0, aggregate.getNumOffers());
        System.out.println("===== END : testFBAFromSQLpsGivingOneSidedBidRate =====");

    }



    @Test
    public void testFBAFromSQLpsGivingOneSidedOfferRate () throws Exception {

        System.out.println("===== START : testFBAFromSQLpsGivingOneSidedOfferRate =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //public QuoteC getOneSidedQuoteC(ServerProvision serverProvision, String ccyp,
        //        String lpName, FIProvision fi, int noOfTiers, double startPrice, boolean isBid) {


        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.21, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testFBAFromSQLpsGivingOneSidedOfferRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(0.00, aggregate.getBidQtys()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21, aggregate.getOfferPrices()[0], 0.001);

        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);
        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0],0.001);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : testFBAFromSQLpsGivingOneSidedOfferRate =====");

    }


    @Test
    public void testFBAFromSQLpsGivingOneSidedBidOfferRate () throws Exception {

        System.out.println("===== START : testFBAFromSQLpsGivingOneSidedBidOfferRate =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testFBAFromSQLpsGivingOneSidedBidOfferRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000, aggregate.getBidQtys()[0], 0.001);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.001);

        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0],0.001);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : testFBAFromSQLpsGivingOneSidedBidOfferRate =====");

    }



    @Test
    public  void testFBAFromMQLPs () throws Exception {

        System.out.println("========= START: testFBAFromMQLPs =========== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl Impl = (FIProvisionImpl) fiProvision;
        Impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(rateBook.getFIIndex(), fiProvision.getIndex());
            }

            RateBook book1 = getBook(serverProvision, fiProvision, manager);
            RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

            Assert.assertNotNull(book1);
            //Assertion to verify that the book is not same across LPs
            Assert.assertSame("Book is not same across LPs", book1, book2);

            //Provision another FI
            service.provision(TESTFI2);

            QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 5, 1.21, 1.24);
            quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
            manager.handleRate(quote);
            System.out.println("========quote from LP1===========" + quote.toString());

            QuoteC quote1 = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 7, 1.21, 1.24);
            quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
            manager.handleRate(quote1);
            System.out.println("============quote from LP2=========" + quote1.toString());

            PriceBook aggregate = book1.aggregate();
            System.out.println("============aggregated quote==========" + aggregate.toString());

            //assert for bid price
            Assert.assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
            Assert.assertEquals(1.2,aggregate.getBidPrices()[1],0.001);
            Assert.assertEquals(1.19,aggregate.getBidPrices()[2], 0.001);
            Assert.assertEquals(1.18,aggregate.getBidPrices()[3],0.001);
            Assert.assertEquals(1.17,aggregate.getBidPrices()[4],0.001);
            Assert.assertEquals(1.16,aggregate.getBidPrices()[5],0.001);
            Assert.assertEquals(1.15,aggregate.getBidPrices()[6],0.001);

            Assert.assertEquals(2000,aggregate.getBidQtys()[0],0.01);
            Assert.assertEquals(4000, aggregate.getBidQtys()[1],0.01);
            Assert.assertEquals(6000,aggregate.getBidQtys()[2],0.01);
            Assert.assertEquals(8000,aggregate.getBidQtys()[3],0.01);
            Assert.assertEquals(10000,aggregate.getBidQtys()[4],0.01);
            Assert.assertEquals(6000,aggregate.getBidQtys()[5],0.01);
            Assert.assertEquals(7000,aggregate.getBidQtys()[6],0.01);

            Assert.assertEquals(1.24,aggregate.getOfferPrices()[0],0.001);
            Assert.assertEquals(1.25,aggregate.getOfferPrices()[1],0.001);
            Assert.assertEquals(1.26,aggregate.getOfferPrices()[2],0.001);
            Assert.assertEquals(1.27,aggregate.getOfferPrices()[3],0.001);
            Assert.assertEquals(1.28,aggregate.getOfferPrices()[4],0.001);
            Assert.assertEquals(1.29,aggregate.getOfferPrices()[5],0.001);
            Assert.assertEquals(1.3,aggregate.getOfferPrices()[6],0.001);

            Assert.assertEquals(2000,aggregate.getOfferQtys()[0],0.01);
            Assert.assertEquals(4000,aggregate.getOfferQtys()[1],0.01);
            Assert.assertEquals(6000,aggregate.getOfferQtys()[2],0.01);
            Assert.assertEquals(8000,aggregate.getOfferQtys()[3],0.01);
            Assert.assertEquals(10000,aggregate.getOfferQtys()[4],0.01);
            Assert.assertEquals(6000,aggregate.getOfferQtys()[5],0.01);
            Assert.assertEquals(7000,aggregate.getOfferQtys()[6],0.01);

            System.out.println("===== END : testFBAFromSQLPs =====");

        }
    }


    @Test
    public void testFBAFromMQLPCausingInvertedTiers () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote

        System.out.println("===== START : testFBAFromMQLPCausingInvertedTiers =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);

        sleep(1);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.19, 1.20);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testFBAFromMQLPCausingInvertedTiers ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.19, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.18, aggregate.getBidPrices()[1], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.21, aggregate.getOfferPrices()[1], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());
        System.out.println("===== END : testFBAFromMQLPCausingInvertedTiers =====");

    }


    @Test
    public void testFBAFromMQLpGivingInvertedTiers () throws Exception {
        // Quote causing inverted quote should be dropped,
        //noOfTiers is 1 since MDF has to send quote with 0 rate and 1 tier size to represent inactive quote to MDG,
        //otherwise MDG will not be able to recognize inactive quote

        System.out.println("===== START : testFBAFromMQLpGivingInvertedTiers =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.24, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testFBAFromMQLpGivingInvertedTiers ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : testFBAFromMQLpGivingInvertedTiers =====");

    }


    @Test
    public void testFBAFromMQLpsGivingOneSidedBidRate () throws Exception {

        System.out.println("===== START : testFBAFromMQLpsGivingOneSidedBidRate =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 4, 1.22, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testFBAFromMQLpsGivingOneSidedBidRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[3], 0.001);

          //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.001);
        assertEquals(5000.00, aggregate.getBidQtys()[2], 0.001);
        assertEquals(7000.00, aggregate.getBidQtys()[3], 0.001);

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);

        //The book is sorted at the offer side as well
        assertEquals(0, aggregate.getOfferQtys()[0],0.001);
        assertEquals(0, aggregate.getNumOffers());
        System.out.println("===== END : testFBAFromMQLpsGivingOneSidedBidRate =====");

    }


    @Test
    public void testFBAFromMQLpsGivingOneSidedOfferRate () throws Exception {


        System.out.println("===== START : testFBAFromMQLpsGivingOneSidedOfferRate =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }



        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testFBAFromMQLpsGivingOneSidedOfferRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(0.00, aggregate.getBidQtys()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1.22, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.23, aggregate.getOfferPrices()[2], 0.001);
        assertEquals(1.24, aggregate.getOfferPrices()[3], 0.01);

        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0],0.001);
        assertEquals(3000.00, aggregate.getOfferQtys()[1],0.001);
        assertEquals(2000.00, aggregate.getOfferQtys()[2],0.001);
        assertEquals(3000.00, aggregate.getOfferQtys()[3],0.001);
        assertEquals(4, aggregate.getNumOffers());

        System.out.println("===== END : testFBAFromMQLpsGivingOneSidedOfferRate =====");

    }


    @Test
    public void testFBAFromMTLpsGivingOneSidedBidOfferRate () throws Exception {

        System.out.println("===== START : testFBAFromMTLpsGivingOneSidedBidOfferRate =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.22, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testFBAFromMTLpsGivingOneSidedBidOfferRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000, aggregate.getBidQtys()[0], 0.001);
        assertEquals(2000, aggregate.getBidQtys()[1], 0.001);
        assertEquals(3000, aggregate.getBidQtys()[2], 0.001);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1.23, aggregate.getOfferPrices()[1], 0.001);

        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0],0.001);
        assertEquals(2000.00, aggregate.getOfferQtys()[1],0.001);
        assertEquals(2, aggregate.getNumOffers());
        System.out.println("===== END : testFBAFromMTLpsGivingOneSidedBidOfferRate =====");

    }


    @Test
    public void testFBAFromImbalancedTiers() throws Exception {

        System.out.println("=========START: testFBAFromImbalancedTiers==============");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 4, 2, 1.21, 1.24);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 4, 2, 1.21, 1.24);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====aggregate quote in testFBAFromImbalancedTiers ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);


        //testing the aggregation
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(6000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(8000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());
    }

    @Test
    public void testFBAFromMTLPs () throws Exception {

        // Incase of MT quote from LP, single tier will be taken for aggregation
        System.out.println("===== START : testFBAFromMTLPs =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 5, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 7, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testFBAFromSQLPs ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : testFBAFromSQLPs =====");

    }


    @Test
    public void testFBAFromMTLPsCausingInvertedTiers () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote
        System.out.println("===== START : testFBAFromMTLPsCausingInvertedTiers =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.19, 1.20);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testFBAFromMTLPsCausingInvertedTiers ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.19, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : testFBAFromMTLPsCausingInvertedTiers =====");

    }



    @Test
    public void testFBAFromMTLpsGivingOneSidedBidRate () throws Exception {

        System.out.println("===== START : testFBAFromMTLpsGivingOneSidedBidRate =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 4, 1.22, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testFBAFromMTLpsGivingOneSidedBidRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.001);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        //assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);

        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);
        //The book is sorted at the offer side as well
        assertEquals(0, aggregate.getOfferQtys()[0],0.001);
        assertEquals(0, aggregate.getNumOffers());
        System.out.println("===== END : testFBAFromMTLpsGivingOneSidedBidRate =====");

    }


    @Test
    public void testFBAFromMTLpsGivingOneSidedOfferRate () throws Exception {


        System.out.println("===== START : testFBAFromMTLpsGivingOneSidedOfferRate =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }



        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testFBAFromMQLpsGivingOneSidedOfferRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(0.00, aggregate.getBidQtys()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1.22, aggregate.getOfferPrices()[1], 0.01);

        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0],0.001);
        assertEquals(1000.00, aggregate.getOfferQtys()[1],0.001);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testFBAFromMQLpsGivingOneSidedOfferRate =====");

    }

    @Test
    public void testFBAFromMQLpsGivingOneSidedBidOfferRate () throws Exception {

        System.out.println("===== START : testFBAFromSQLpsGivingOneSidedBidOfferRate =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.22, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testFBAFromMQLpsGivingOneSidedBidOfferRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000, aggregate.getBidQtys()[0], 0.001);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.001);

        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0],0.001);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : testFBAFromMQLpsGivingOneSidedBidOfferRate =====");

    }


    @Test
    public void testFBAFromMTandMQLP() throws Exception {

        System.out.println("===== START : testFBAFromMTandMQLP =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 5, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 7, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testFBAFromMTLpsGivingOneSidedOfferRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.001);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[4], 0.001);
        assertEquals(1.16, aggregate.getBidPrices()[5], 0.01);
        assertEquals(1.15, aggregate.getBidPrices()[6], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(6000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(7000.00, aggregate.getBidQtys()[6], 0.01);
        assertEquals(7, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.28, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(1.29, aggregate.getOfferPrices()[5], 0.01);
        assertEquals(1.30, aggregate.getOfferPrices()[6], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(7000.00, aggregate.getOfferQtys()[6], 0.01);
        assertEquals(7, aggregate.getNumOffers());

        System.out.println("===== END : testFBAFromMTLpsGivingOneSidedOfferRate =====");

    }

    @Test
    public void testFullBookAggregationLargerAmount() throws Exception {

        System.out.println("==============START:testFullBookAggregationLargerAmount==================== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.21,1.24,1000,1000);
        //return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.21, 1.24);
        //	        return getQuoteC(serverProvision, ccyp, lpName, fi, noOfTiers, startBid, startOffer, 1000.0d, 1000.0d);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 6,1.21d, 1.24d, 2000d, 2000d);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testRBAFromMTLpsGivingOneSidedOfferRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.16, aggregate.getBidPrices()[5], 0.01);


        //testing the aggregation
        assertEquals(3000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(7000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(9000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(11000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(13000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.28, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(1.29, aggregate.getOfferPrices()[5], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(3000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(7000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(9000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(11000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(13000.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumOffers());
    }


    @Test
    public void MEzeroliquidity_FullBookFixed() throws Exception {
        //As of now,.. 0 liquidity sent from ME comes as 0 only from MDF as well
        //Change will be made to drop this quote with 0 liquidity,. then the test case will fail
        System.out.println("==============START:MEzeroliquidity==================== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.22,1.24,1000,1000);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3,1.21d, 1.24d, 1000d, 1000d);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 3,1.22,1.24,0,1000);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregate second quote in MEzeroliquidity ==="+aggregate.toString());

        assertNotEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        assertEquals(2, aggregate.getBookId());

    }


    @Test
    public void MEzeroliquidity_TierTwo() throws Exception {
        //As of now,.. 0 liquidity sent from ME comes as 0 only from MDF as well
        //Change will be made to drop this quote with 0 liquidity,. then the test case will fail
        System.out.println("==============START:MEzeroliquidity_TierTwo==================== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.22,1.34,1000,1000);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3,1.22d, 1.34d, 1000d, 1000d);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(6000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.34, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.35, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.36, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());

        double [] startbid1 = {1.22,1.21,1.20};
        double [] startoffer1 = {1.34,1.35,1.36};
        double [] bidliq1 = {1000,0.0,3000};
        double [] offerliq1 = {1000,2000,3000};

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteCArray(serverProvision, EUR_USD, LP22, fiProvision, 3, 3,startbid1,startoffer1,bidliq1,offerliq1);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregate second quote in MEzeroliquidity_TierTwo ==="+aggregate.toString());

        assertEquals(1.22, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(6000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.34, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.35, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.36, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000.0, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(6000.0, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        assertEquals(2, aggregate.getBookId());

    }


    @Test
    public void MEzeroliquidity_TierFour() throws Exception {
        //As of now,.. 0 liquidity sent from ME comes as 0 only from MDF as well
        //Change will be made to drop this quote with 0 liquidity,. then the test case will fail
        System.out.println("==============START:MEzeroliquidity_TierFour==================== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.22,1.34,1000,1000);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3,1.21d, 1.33d, 1000d, 1000d);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.33, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.34, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.35, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.36, aggregate.getOfferPrices()[3], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());

        double [] startbid = {1.22,1.21,1.20,1.19};
        double [] startoffer = {1.33,1.34,1.35,1.36};
        double [] bidliq = {1000,2000,0.0,0.0};
        double [] offerliq = {1000,2000,0.0,0.0};

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteCArray(serverProvision, EUR_USD, LP12, fiProvision, 4, 3,startbid,startoffer,bidliq,offerliq);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregate second quote in MEzeroliquidity_TierTwo ==="+aggregate.toString());

        assertEquals(1.22, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.33, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.34, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.35, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000.0, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.0, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        assertEquals(2, aggregate.getBookId());

    }


    @Test
    public void testFBAtentiersFromTwoLPs () throws Exception {

        System.out.println("===== START : testFBAtentiersFromTwoLPs =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //Cannot publish more than 8 tiers for a single LP, since the public static final int ALIGNMENT = SIZE_OF_LONG; defined in BitUtil.java
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 8, 1.21, 1.31);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 4, 1.28, 1.38);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testFBAtentiersFromTwoLPs ==="+aggregate.toString());

        //assert For bid price
        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1.28, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.16, aggregate.getBidPrices()[9], 0.001);
        assertEquals(6000.00, aggregate.getBidQtys()[9], 0.01);
        assertEquals(10, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.31, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.4, aggregate.getOfferPrices()[9], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[9], 0.01);
        assertEquals(10, aggregate.getNumOffers());
        System.out.println("===== END : testFBAtentiersFromTwoLPs =====");
    }


    @Test
    public void testFBAFromSQLPs_samebidoffer () throws Exception {

        System.out.println("===== START : testFBAFromSQLPs_samebidoffer =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.19, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testFBAFromSQLPs_samebidoffer ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[1], 0.001);
        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        //Test agg after second quote
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.19, 1.26);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("=====second aggregate quote in testFBAFromSQLPs_samebidoffer ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[1], 0.001);
        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.01);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testFBAFromSQLPs_samebidoffer =====");
    }

    // Not working
    @Test
    public void testFBAMaxAggAttemptsSameAsStreams () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote
        System.out.println("===== START : testFBAMaxAggAttemptsSameAsStreams =====");
        ServerProvision serverProvision = doDefaultProvision();
        serverProvision.setMaxAggregationAttempts(4);
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.1881, 1.1891);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 1, 1.1883, 1.1887);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP3 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1,1.1882, 1.1890);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP2 quote==="+quote.toString());

        //Last quote causes the book to be inverted and this quote which is oldest will be removed
        quote = getQuoteC(serverProvision, EUR_USD, LP61, fiProvision, 1, 1.1888, 1.1892);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP6 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====aggregate quote in testFBAMaxAggAttemptsSameAsStreams ==="+aggregate.toString());

        //Actual
        //bidPrices=[1.1883, 1.1882, 1.1881, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        // bidQtys=[1000.0, 1000.0, 1000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        // bidNoLPs[1, 1, 1, 0, 0, 0, 0, 0, 0, 0],
        // offerPrices=[1.1887, 1.189, 1.1891, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        // offerQtys=[1000.0, 1000.0, 1000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], offerNoLPs[1, 1, 1, 0, 0, 0, 0, 0, 0, 0]

        //assert For bid price
        assertEquals(3, aggregate.getNumBids());
        assertEquals(1.1888, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.00001);
        assertEquals(1.1882, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.00001);
        assertEquals(1.1881, aggregate.getBidPrices()[2], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.00001);

        //assert For offer price
        assertEquals(3, aggregate.getNumOffers());
        assertEquals(1.1890, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(1.1891, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(1.1892, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[2], 0.00001);

        System.out.println("===== END : testFBAMaxAggAttemptsSameAsStreams =====");

    }

    // Not working
    @Test
    public void testFBAMaxAggAttemptsMoreThanStreams () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote
        System.out.println("===== START : testFBAMaxAggAttemptsMoreThanStreams =====");
        ServerProvision serverProvision = doDefaultProvision();
        serverProvision.setMaxAggregationAttempts(5);
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.1881, 1.1891);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1,1.1882, 1.1890);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP2 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 1, 1.1883, 1.1887);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP3 quote==="+quote.toString());

        //Last quote causes the book to be inverted and this quote which is oldest will be removed
        quote = getQuoteC(serverProvision, EUR_USD, LP61, fiProvision, 1, 1.1888, 1.1892);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP6 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====aggregate quote in testFBAMaxAggAttemptsMoreThanStreams ==="+aggregate.toString());

        // Actual output
        // bidPrices=[1.1883, 1.1882, 1.1881, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        // bidQtys=[1000.0, 1000.0, 1000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        // bidNoLPs[1, 1, 1, 0, 0, 0, 0, 0, 0, 0],
        // offerPrices=[1.1887, 1.189, 1.1891, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        // offerQtys=[1000.0, 1000.0, 1000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        // offerNoLPs[1, 1, 1, 0, 0, 0, 0, 0, 0, 0]

        //assert For bid price
        assertEquals(3, aggregate.getNumBids());
        assertEquals(1.1888, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.00001);
        assertEquals(1.1882, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.00001);
        assertEquals(1.1881, aggregate.getBidPrices()[2], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.00001);

        //assert For offer price
        assertEquals(3, aggregate.getNumOffers());
        assertEquals(1.189, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(1.1891, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(1.1892, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[2], 0.00001);
        System.out.println("===== END : testFBAMaxAggAttemptsMoreThanStreams =====");

    }

    // Not working
    @Test
    public void testFBAMaxAggAttemptsLessThanStreams () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote
        System.out.println("===== START : testFBAMaxAggAttemptsLessThanStreams =====");
        ServerProvision serverProvision = doDefaultProvision();
        serverProvision.setMaxAggregationAttempts(3);
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.1881, 1.1891);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1,1.1882, 1.1890);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP2 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 1, 1.1883, 1.1887);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP3 quote==="+quote.toString());

        //Last quote causes the book to be inverted and this quote which is oldest will be removed
        quote = getQuoteC(serverProvision, EUR_USD, LP51, fiProvision, 1, 1.1884, 1.1893);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP5 quote==="+quote.toString());

        //Last quote causes the book to be inverted and this quote which is oldest will be removed
        quote = getQuoteC(serverProvision, EUR_USD, LP61, fiProvision, 1, 1.1888, 1.1892);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP6 quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();
        System.out.println("=====aggregate quote in testFBAMaxAggAttemptsLessThanStreams ==="+aggregate.toString());

        // bidPrices=[1.1884, 1.1883, 1.1882, 1.1881, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        // bidQtys=[1000.0, 1000.0, 1000.0, 1000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        // bidNoLPs[1, 1, 1, 1, 0, 0, 0, 0, 0, 0],
        // offerPrices=[1.1887, 1.189, 1.1891, 1.1893, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        // offerQtys=[1000.0, 1000.0, 1000.0, 1000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        // offerNoLPs[1, 1, 1, 1, 0, 0, 0, 0, 0, 0],

        //assert For bid price
        assertEquals(4, aggregate.getNumBids());
        assertEquals(1.1888, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.00001);
        assertEquals(1.1884, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.00001);
        assertEquals(1.1882, aggregate.getBidPrices()[2], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.00001);
        assertEquals(1.1881, aggregate.getBidPrices()[3], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[3], 0.00001);

        //assert For offer price
        assertEquals(4, aggregate.getNumOffers());
        assertEquals(1.1890, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(1.1891, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(1.1892, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(1.1893, aggregate.getOfferPrices()[3], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[3], 0.00001);

        System.out.println("===== END : testFBAMaxAggAttemptsLessThanStreams =====");

    }
    @Test
    // Not working as expected
    public void testFBAMaxAggAttemptsLessThanStreams1 () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote
        System.out.println("===== START : testFBAMaxAggAttemptsLessThanStreams1 =====");
        ServerProvision serverProvision = doDefaultProvision();
        serverProvision.setMaxAggregationAttempts(4);
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.1881, 1.1891);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1,1.1882, 1.1890);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP2 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 1, 1.1883, 1.1887);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP3 quote==="+quote.toString());

        //Last quote causes the book to be inverted and this quote which is oldest will be removed
        quote = getQuoteC(serverProvision, EUR_USD, LP51, fiProvision, 1, 1.1884, 1.1893);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP5 quote==="+quote.toString());

        //Last quote causes the book to be inverted and this quote which is oldest will be removed
        quote = getQuoteC(serverProvision, EUR_USD, LP61, fiProvision, 1, 1.1888, 1.1892);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP6 quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();
        System.out.println("=====aggregate quote in testFBAMaxAggAttemptsLessThanStreams1 ==="+aggregate.toString());

        // Actual result
        // bidPrices=[1.1884, 1.1883, 1.1882, 1.1881, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        // bidQtys=[1000.0, 1000.0, 1000.0, 1000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        // bidNoLPs[1, 1, 1, 1, 0, 0, 0, 0, 0, 0],
        // offerPrices=[1.1887, 1.189, 1.1891, 1.1893, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        // offerQtys=[1000.0, 1000.0, 1000.0, 1000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        // offerNoLPs[1, 1, 1, 1, 0, 0, 0, 0, 0, 0],

        //assert For bid price
        assertEquals(4, aggregate.getNumBids());
        assertEquals(1.1888, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.00001);
        assertEquals(1.1884, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.00001);
        assertEquals(1.1882, aggregate.getBidPrices()[2], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.00001);
        assertEquals(1.1881, aggregate.getBidPrices()[3], 0.00001);
        assertEquals(1000.00, aggregate.getBidQtys()[3], 0.00001);

        //assert For offer price
        assertEquals(4, aggregate.getNumOffers());
        assertEquals(1.1890, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(1.1891, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(1.1892, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(1.1893, aggregate.getOfferPrices()[3], 0.00001);
        assertEquals(1000.00, aggregate.getOfferQtys()[3], 0.00001);

        System.out.println("===== END : testFBAMaxAggAttemptsLessThanStreams1 =====");

    }
    @Test
    public void TC1_customAgg_MTproviders_requestedSize () throws Exception {

        System.out.println("===== START : TC1_customAgg_MTproviders_requestedSize =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, null, 2000, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());
    }
    @Test
    public void TC2_customAgg_MTMQproviders_requestedSize () throws Exception {

        System.out.println("===== START : TC2_customAgg_MTMQproviders_requestedSize =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, null, 2000, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.001);
        assertEquals(5000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());



    }
    @Test
    public void TC3_customAgg_higherRequestedSize () throws Exception {
    // requested size is greater than the available liquidity, this results in blank book
        System.out.println("===== START : TC3_customAgg_higherRequestedSize =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, null, 4000, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
    }
    @Test
    public void TC13_customAgg_requestedSizeInBetweenTiers () throws Exception {
        // requested size is greater than the available liquidity, this results in blank book
        System.out.println("===== START : TC13_customAgg_requestedSizeInBetweenTiers =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, null, 2500, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2, aggregate.getBidPrices()[0], 0.001);
        assertEquals(3000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[1], 0.001);
        assertEquals(3000, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.26, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(3000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[1], 0.001);
        assertEquals(3000, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());
    }
    @Test
    public void TC4_customAgg_MTMQproviders_higherRequestedSize () throws Exception {
        // since requested size is more than the MT tier size, MT provider is not considered in FB aggregation
        System.out.println("===== START : TC4_customAgg_MTMQproviders_higherRequestedSize =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, null, 4000, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());

    }
    @Test
    public void TC5_customAgg_MQproviders_requestedSize () throws Exception {
        // requestSize is ignored incase of MQ providers
        System.out.println("===== START : TC5_customAgg_MQproviders_requestedSize =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, null, 2000, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.001);
        assertEquals(5000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[3], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[3], 0.01);

        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());
    }
    @Test
    public void TC10_customAgg_negativeRequestedSize () throws Exception {
        // negative requestedSize is not validated, and tier1 of MT provider is considered
        // this gets validated in UIG, so not very imp
        System.out.println("===== START : TC10_customAgg_negativeRequestedSize =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, null, -2000, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());



    }
    @Test
    public void TC6_customAgg_multipleProviders () throws Exception {

        System.out.println("===== START : TC6_customAgg_multipleProviders =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, providers, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.001);
        assertEquals(5000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[3], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[3], 0.01);

        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());
    }
    @Test
    public void TC7_customAgg_invalidProviders () throws Exception {
// check with rejeev
        // ideally it should ignore the invalid LP and consider the correct LPs.
        // But right now, it is considering the LP that is not there in  providerList
        System.out.println("===== START : TC7_customAgg_invalidProviders =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add("abc");

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, providers, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
    }
    @Test
    public void TC13_customAgg_invalidProvider () throws Exception {
        // When an invalid provider is the only provider, it is dropped and considers all providers (like "providers"=[])

        System.out.println("===== START : TC13_customAgg_invalidProvider =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        List<String> providers = new ArrayList<String>();
        providers.add("abc");

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, providers, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.001);
        assertEquals(5000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[3], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());
    }
    @Test
    public void TC8_customAgg_termCcy () throws Exception {

        System.out.println("===== START : TC8_customAgg_termCcy =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, providers, 0, true);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1220.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(3630.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.001);
        assertEquals(6000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[3], 0.001);
        assertEquals(3570.00, aggregate.getBidQtys()[3], 0.01);

        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1240.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(3750.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(6300.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(3810.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());
    }
    @Test
    public void TC9_customAgg_withAllTags () throws Exception {

        System.out.println("===== START : TC9_customAgg_withAllTags =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, providers, 2000, true);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1220.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2420.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.001);
        assertEquals(6000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(3750.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(2520.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(3810.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());



    }
    @Test
    public void TC11_customAgg_multipleSubscriptions () throws Exception {

        System.out.println("===== START : TC11_customAgg_multipleSubscriptions =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("12", fiProvision, cpIndex, null, providers, 2000, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 8, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 8, 1.22, 1.23);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());


        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.001);
        assertEquals(5000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(8, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.23, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(8, aggregate.getNumOffers());

        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.27, 1.29);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.28, 1.29);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        aggregate = rateBook.aggregate();
        System.out.println("=====Second aggregated quote ==="+aggregate.toString());

        assertEquals(1.28, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.27, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.26, aggregate.getBidPrices()[2], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.29, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.3, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

    }
    @Test
    public void TC12_customAgg_multipleSubscriptions () throws Exception {
    // 2 MQ providers with resulting book more than 10 tiers, but final book is limited to 10 tiers

        System.out.println("===== START : TC11_customAgg_multipleSubscriptions =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("12", fiProvision, cpIndex, null, providers, 2000, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 8, 1.25, 1.26);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 8, 1.20, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.25, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.24, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.23, aggregate.getBidPrices()[2], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(1.22, aggregate.getBidPrices()[3], 0.001);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[4], 0.001);
        assertEquals(5000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[5], 0.001);
        assertEquals(7000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[6], 0.001);
        assertEquals(9000.00, aggregate.getBidQtys()[6], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[7], 0.001);
        assertEquals(11000.00, aggregate.getBidQtys()[7], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[8], 0.001);
        assertEquals(4000.00, aggregate.getBidQtys()[8], 0.01);
        assertEquals(1.16, aggregate.getBidPrices()[9], 0.001);
        assertEquals(5000.00, aggregate.getBidQtys()[9], 0.01);
        assertEquals(10, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(1.28, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(7000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(1.29, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(9000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(1.30, aggregate.getOfferPrices()[5], 0.01);
        assertEquals(11000.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(1.31, aggregate.getOfferPrices()[6], 0.01);
        assertEquals(13000.00, aggregate.getOfferQtys()[6], 0.01);
        assertEquals(1.32, aggregate.getOfferPrices()[7], 0.01);
        assertEquals(15000.00, aggregate.getOfferQtys()[7], 0.01);
        assertEquals(1.33, aggregate.getOfferPrices()[8], 0.01);
        assertEquals(8000.00, aggregate.getOfferQtys()[8], 0.01);
        assertEquals(0.0, aggregate.getOfferPrices()[9], 0.01);
        assertEquals(0.00, aggregate.getOfferQtys()[9], 0.01);
        assertEquals(9, aggregate.getNumOffers());

        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.27, 1.29);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());


        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.28, 1.29);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        aggregate = rateBook.aggregate();
        System.out.println("=====Second aggregated quote ==="+aggregate.toString());

        assertEquals(1.28, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.27, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.26, aggregate.getBidPrices()[2], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.29, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.3, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());


    }


}

