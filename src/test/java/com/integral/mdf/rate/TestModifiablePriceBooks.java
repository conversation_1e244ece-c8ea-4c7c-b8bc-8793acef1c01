package com.integral.mdf.rate;

import com.integral.mdf.data.*;
import com.integral.provision.CurrencyPairProvision;
import com.integral.provision.MDFAggregationType;
import com.integral.virtualserver.MDFEntity;
import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.Assert.*;

public class TestModifiablePriceBooks extends PriceAggregationTest {

    public static final String LP22 = "lp2";
    public static final String LP12 = "lp1";
    public static final String EUR_USD = "EUR/USD";

    protected void set3TierRates(QuoteC quote, byte buyOrSell, double p1, double p2, double p3){
        //tier 1
        int tieridx = quote.tierOffset(buyOrSell, 0);
        quote.setPrice(tieridx, p1);
        //tier 2
        tieridx = quote.tierOffset(buyOrSell, 1);
        quote.setPrice(tieridx, p2);
        //tier 3
        tieridx = quote.tierOffset(buyOrSell, 2);
        quote.setPrice(tieridx, p3);
    }

    protected void set4TierRates(QuoteC quote, byte buyOrSell, double p1, double p2, double p3,double p4){
        //tier 1
        int tieridx = quote.tierOffset(buyOrSell, 0);
        quote.setPrice(tieridx, p1);
        //tier 2
        tieridx = quote.tierOffset(buyOrSell, 1);
        quote.setPrice(tieridx, p2);
        //tier 3
        tieridx = quote.tierOffset(buyOrSell, 2);
        quote.setPrice(tieridx, p3);
        //tier 4
        tieridx = quote.tierOffset(buyOrSell, 3);
        quote.setPrice(tieridx, p4);
    }

    /**
     * MultiQuote-MultiQuote VWAP aggregation  - Addtiers.
     * @throws Exception
     */
    @Test
    public void testModifiableVWAPAddTiers() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testModifiableVWAPAddTiers  =====");
        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl)serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(book1.getPricebook().getTiers());
        assertEquals(0,book1.getPricebook().getTiers().length);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====#1 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumOffers());

        //add tiers now
        book1.getPricebook().addTier(1000);
        book1.getPricebook().addTier(2000);
        book1.getPricebook().addTier(3000);

        aggregate = book1.aggregate();
        System.out.println("=====#2 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21206, aggregate.getBidPrices()[1], 0.00001);
        //  assertEquals(1.21207, aggregate.getBidPrices()[2], 0.001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        //  assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
        //     assertEquals(1.2122, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.21224, aggregate.getOfferPrices()[1], 0.0001);


        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        //assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumOffers());

    }


    /**
     * add 1000,2000,3000 - remove 3000
     * MultiQuote-MultiQuote VWAP aggregation  - Addtiers then remove Largest tier amount.
     * @throws Exception
     */
    @Test
    public void testModifiableVWAPAddRemoveLargestTierAmount() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testModifiableVWAPAddRemoveLargestTierAmount  =====");

        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl)serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(book1.getPricebook().getTiers());
        assertEquals(0,book1.getPricebook().getTiers().length);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====#1 aggregate quote in testModifiableVWAPAddRemoveLargestTierAmount  ==="+aggregate.toString());

        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumOffers());

        //add tiers now
        book1.getPricebook().addTier(1000);
        book1.getPricebook().addTier(2000);
        book1.getPricebook().addTier(3000);

        aggregate = book1.aggregate();
        System.out.println("=====#2 aggregate quote in testModifiableVWAPAddRemoveLargestTierAmount  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21206, aggregate.getBidPrices()[1], 0.00001);
        //  assertEquals(1.21207, aggregate.getBidPrices()[2], 0.001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        //  assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
        //     assertEquals(1.2122, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.21224, aggregate.getOfferPrices()[1], 0.0001);


        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        //assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumOffers());


        //remove last tiers
        book1.getPricebook().removeTier(3000);
        aggregate = book1.aggregate();
        System.out.println("=====#3 aggregate quote in testModifiableVWAPAddRemoveLargestTierAmount  ==="+aggregate.toString());

        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testModifiableVWAPAddRemoveLargestTierAmount  =====");
    }


    /**
     * add 1000,2000,3000 - remove 1000
     * MultiQuote-MultiQuote VWAP aggregation  - Addtiers then remove smallest tier amount.
     * @throws Exception
     */
    @Test
    public void testModifiableVWAPAddRemoveSmallestTierAmount() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testModifiableVWAPAddRemoveSmallestTierAmount  =====");


        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl)serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(book1.getPricebook().getTiers());
        assertEquals(0,book1.getPricebook().getTiers().length);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====#1 aggregate quote in testModifiableVWAPAddRemoveSmallestTierAmount  ==="+aggregate.toString());

        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumOffers());

        //add tiers now
        book1.getPricebook().addTier(1000);
        book1.getPricebook().addTier(2000);
        book1.getPricebook().addTier(3000);

        aggregate = book1.aggregate();
        System.out.println("=====#2 aggregate quote in testModifiableVWAPAddRemoveSmallestTierAmount  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21206, aggregate.getBidPrices()[1], 0.00001);
        //  assertEquals(1.21207, aggregate.getBidPrices()[2], 0.001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        //  assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
        //     assertEquals(1.2122, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.21224, aggregate.getOfferPrices()[1], 0.0001);


        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        //assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumOffers());


        //remove last tiers
        book1.getPricebook().removeTier(1000);
        aggregate = book1.aggregate();
        System.out.println("=====#3 aggregate quote in testModifiableVWAPAddRemoveSmallestTierAmount  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1.21206, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(1.21224, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testModifiableVWAPAddRemoveSmallestTierAmount  =====");
    }


    @Test
    public void testModifiableVWAPAddRemoveLargestTierAmount_BidOnly() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers
        System.out.println("===== START : testModifiableVWAPAddRemoveLargestTierAmount  =====");
        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl)serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(book1.getPricebook().getTiers());
        assertEquals(0,book1.getPricebook().getTiers().length);

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, true);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====#1 aggregate quote in testModifiableVWAPAddRemoveLargestTierAmount  ==="+aggregate.toString());

        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumOffers());

        //add tiers now
        book1.getPricebook().addTier(1000);
        book1.getPricebook().addTier(2000);
        book1.getPricebook().addTier(3000);

        aggregate = book1.aggregate();
        System.out.println("=====#2 aggregate quote in testModifiableVWAPAddRemoveLargestTierAmount  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21206, aggregate.getBidPrices()[1], 0.00001);
        //  assertEquals(1.21207, aggregate.getBidPrices()[2], 0.001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        //  assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(0, aggregate.getNumOffers());

        //remove last tiers
        book1.getPricebook().removeTier(3000);
        aggregate = book1.aggregate();
        System.out.println("=====#3 aggregate quote in testModifiableVWAPAddRemoveLargestTierAmount  ==="+aggregate.toString());

        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(0, aggregate.getNumOffers());

        System.out.println("===== END : testModifiableVWAPAddRemoveLargestTierAmount  =====");
    }


    @Test  //Imbalanced tiers, MT, merge on offer side only
        public void testModifiableVWAPAddTiers_merge() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers
        System.out.println("===== START : testModifiableVWAPAddTiers_merge  =====");
        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl)serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(book1.getPricebook().getTiers());
        assertEquals(0,book1.getPricebook().getTiers().length);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.25, 1.26);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2125,1.2124,0.0);
        set3TierRates(quote,QuoteC.SELL,1.2126,1.2127,0.0);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        set3TierRates(quote,QuoteC.BUY,1.2122,1.2121,0.0);
        set3TierRates(quote,QuoteC.SELL,1.2125,1.2126,0.0);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====#1 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumOffers());

        //add tiers now
        book1.getPricebook().addTier(1000);
        book1.getPricebook().addTier(2000);
        book1.getPricebook().addTier(3000);

        aggregate = book1.aggregate();
        System.out.println("=====#2 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2125, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.2124, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.212333, aggregate.getBidPrices()[2], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2125, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.2126, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(0.0, aggregate.getOfferPrices()[2], 0.00001);


        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(0.0, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(2, aggregate.getNumOffers());

    }


    @Test //Large tiers, quote gets dropped, since no liquidity
    public void testModifiableVWAPlargeTiers() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testModifiableVWAPlargeTiers  =====");
        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl)serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(book1.getPricebook().getTiers());
        assertEquals(0,book1.getPricebook().getTiers().length);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====#1 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumOffers());

        //add tiers now
        book1.getPricebook().addTier(12000);
        book1.getPricebook().addTier(13000);
        book1.getPricebook().addTier(15000);
        book1.getPricebook().addTier(18000);

        aggregate = book1.aggregate();
        System.out.println("=====#2 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21192, aggregate.getBidPrices()[0], 0.00001);
        assertNotEquals(1.21205, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(0.0, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(12000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(0.0, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21245, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(12000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(0.0, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        book1.getPricebook().addTier(18000);
        aggregate = book1.aggregate();
        System.out.println("=====#3 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21192, aggregate.getBidPrices()[0], 0.00001);
        assertNotEquals(1.21205, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(0.0, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(12000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(0.0, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21245, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(12000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(0.0, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumOffers());


    }

    /**
     * Same book should be maintained since the higher tier is only added not removed
     * adding 0 as a tier, agg book has only qty, nothing in price!!!
     * PLT-3585
     * @throws Exception
     */
    @Test
    public void testModifiableVWAPAddTiers_maxtiers0tiers() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testModifiableVWAPAddTiers_maxtiers0tiers  =====");
        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl)serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000}); //1000,2000,3000
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(book1.getPricebook().getTiers());
        assertEquals(0,book1.getPricebook().getTiers().length);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====#1 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumOffers());

        //add tiers now
        book1.getPricebook().addTier(1000);
        book1.getPricebook().addTier(2000);
        book1.getPricebook().addTier(3000);

        aggregate = book1.aggregate();
        System.out.println("=====#2 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21206, aggregate.getBidPrices()[1], 0.00001);
        //  assertEquals(1.21207, aggregate.getBidPrices()[2], 0.001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        //  assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
        //     assertEquals(1.2122, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.21224, aggregate.getOfferPrices()[1], 0.0001);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        //assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        //add tiers now
        book1.getPricebook().addTier(18000);
        aggregate = book1.aggregate();
        System.out.println("=====#3 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21206, aggregate.getBidPrices()[1], 0.00001);
         assertEquals(1.21192, aggregate.getBidPrices()[2], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(12000.00, aggregate.getBidQtys()[2], 0.01); //max available liquidity is 12k (6k+6k)
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.21224, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(1.21245, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(12000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());

        boolean result = book1.getPricebook().addTier(0);//this will fail now
        assertFalse(result);
        book1.getPricebook().addTier(0);
        book1.getPricebook().addTier(0);
        book1.getPricebook().addTier(0);

//        book1.getPricebook().addTier(0);
//        book1.getPricebook().addTier(0);
        aggregate = book1.aggregate();
        System.out.println("=====#4 new 2 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21206, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.21192, aggregate.getBidPrices()[2], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(12000.00, aggregate.getBidQtys()[2], 0.01); //max available liquidity is 12k (6k+6k)
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.21224, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(1.21245, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(12000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());


    }


    @Test  //same tiers added twice, will be merged, remove middle tier
    public void testModifiableVWAPFourtiers_merge() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testModifiableVWAPFourtiers_same  =====");
        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl)serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(book1.getPricebook().getTiers());
        assertEquals(0,book1.getPricebook().getTiers().length);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====#1 aggregate quote in testModifiableVWAPFourtiers_same  ==="+aggregate.toString());

        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumOffers());

        //add tiers now
        book1.getPricebook().addTier(1000);
        book1.getPricebook().addTier(2000);
        book1.getPricebook().addTier(3000);
        book1.getPricebook().addTier(4000);
        book1.getPricebook().addTier(4000);
        book1.getPricebook().addTier(5000);

        aggregate = book1.aggregate();
        System.out.println("=====#2 aggregate quote in testModifiableVWAPFourtiers_same  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21206, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.21205, aggregate.getBidPrices()[2], 0.001);
        assertEquals(1.21202, aggregate.getBidPrices()[3], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.21224, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(1.21225, aggregate.getOfferPrices()[2], 0.0001);
        assertEquals(1.21228, aggregate.getOfferPrices()[3], 0.0001);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());


        book1.getPricebook().removeTier(3000);
        aggregate = book1.aggregate();
        System.out.println("=====#3 aggregate quote in testModifiableVWAPFourtiers_same  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21205, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.21202, aggregate.getBidPrices()[2], 0.001);
        assertEquals(0.0, aggregate.getBidPrices()[3], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(0.0, aggregate.getBidQtys()[3], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.21225, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(1.21228, aggregate.getOfferPrices()[2], 0.0001);
        assertEquals(0.0, aggregate.getOfferPrices()[3], 0.0001);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(0.0, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(3, aggregate.getNumOffers());

    }


    @Test
    public void testModifiableVWAPnewTiers() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers
        System.out.println("===== START : testModifiableMTFOKAddTiers  =====");
        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl)serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(book1.getPricebook().getTiers());
        assertEquals(0,book1.getPricebook().getTiers().length);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        //add tiers now
        book1.getPricebook().addTier(1000);
        book1.getPricebook().addTier(2000);
        book1.getPricebook().addTier(3000);

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====#1 aggregate quote in testModifiableMTFOKAddTiers  ==="+aggregate.toString());

        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.000001);
        assertEquals(1.21206, aggregate.getBidPrices()[1], 0.000001);
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.000001);
        assertEquals(1.21224, aggregate.getOfferPrices()[1], 0.000001);
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

       //add tiers now
        book1.getPricebook().addTier(4000);
        book1.getPricebook().addTier(5000);
        book1.getPricebook().addTier(6000);

        aggregate = book1.aggregate();
        System.out.println("=====#2 aggregate quote in testModifiableMTFOKAddTiers  ==="+aggregate.toString());

        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.000001);
        assertEquals(1.21206, aggregate.getBidPrices()[1], 0.000001);
        assertEquals(1.21205, aggregate.getBidPrices()[2], 0.000001);
        assertEquals(1.21202, aggregate.getBidPrices()[3], 0.000001);
        assertEquals(1.212, aggregate.getBidPrices()[4], 0.000001);
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(6000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(5, aggregate.getNumBids());
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.000001);
        assertEquals(1.21224, aggregate.getOfferPrices()[1], 0.000001);
        assertEquals(1.21225, aggregate.getOfferPrices()[2], 0.000001);
        assertEquals(1.21228, aggregate.getOfferPrices()[3], 0.000001);
        assertEquals(1.2123, aggregate.getOfferPrices()[4], 0.000001);
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(5, aggregate.getNumOffers());

    }


    @Test
    public void testModifiableVWAPAddTiers_NoTiersadmin() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testModifiableVWAPAddTiers  =====");
        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl)serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{});  //Takes default as 0 in admin also
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(book1.getPricebook().getTiers());
        assertEquals(0,book1.getPricebook().getTiers().length);
        System.out.println("\n.....book1......."+book1.getPricebook().getTiers().toString());

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====#1 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumOffers());

        //add tiers now
        book1.getPricebook().addTier(1000);
        book1.getPricebook().removeTier(1000);

        aggregate = book1.aggregate();
        System.out.println("=====#2 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumOffers());

    }

    /**
     * Adding single tier, then removing single tier
     * After that agg gives 0
     * @throws Exception
     */
    @Test
    public void testModifiableVWAPSingleTiers() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testModifiableVWAPSingleTiers  =====");
        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl)serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(book1.getPricebook().getTiers());
        assertEquals(0,book1.getPricebook().getTiers().length);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====#1 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumOffers());

        //add tiers now
        book1.getPricebook().addTier(1000);

        aggregate = book1.aggregate();
        System.out.println("=====#2 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21206, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
        assertNotEquals(1.21224, aggregate.getOfferPrices()[1], 0.0001);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        book1.getPricebook().removeTier(1000);
        aggregate = book1.aggregate();
        System.out.println("=====#3 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumOffers());

    }


    /**
     * Merge two tiers on the offer side, imbalanced tiers
     * remove the merged middle tier and check aggregation, only bid side rate changes
     * remove first tier again and check aggregation, both bid side and offer rate changes
     * @throws Exception
     */
    @Test
    public void testModifiableVWAPFromMultiTierLPs_merge2T () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers
        System.out.println("===== START : testModifiableVWAPFromMultiTierLPs_merge2T =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);
        //set node type to OnDemand
        ((ServerProvisionImpl)serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        book1.getPricebook().addTier(1000);
        book1.getPricebook().addTier(2000);
        book1.getPricebook().addTier(3000);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.25, 1.26);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2125,1.2124,0.0);
        set3TierRates(quote,QuoteC.SELL,1.2126,1.2127,0.0);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        set3TierRates(quote,QuoteC.BUY,1.2122,1.2121,0.0);
        set3TierRates(quote,QuoteC.SELL,1.2125,1.2126,0.0);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiTierLPs_merge2T ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2125, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.2124, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.212333, aggregate.getBidPrices()[2], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2125, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.2126, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(0.0, aggregate.getOfferPrices()[2], 0.00001);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(0.0, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(2, aggregate.getNumOffers());


        book1.getPricebook().removeTier(2000);
        aggregate = book1.aggregate();
        System.out.println("=====2: aggregate quote in testVWAPFromMultiTierLPs_merge2T ==="+aggregate.toString());


        //assert For bid price
        assertEquals(1.2125, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.212333, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(0.0, aggregate.getBidPrices()[2], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(0.0, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2125, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.2126, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(0.0, aggregate.getOfferPrices()[2], 0.00001);


        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(0.0, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(2, aggregate.getNumOffers());

        book1.getPricebook().removeTier(1000);
        aggregate = book1.aggregate();
        System.out.println("=====3: aggregate quote in testVWAPFromMultiTierLPs_merge2T ==="+aggregate.toString());

        //assert For bid price
        assertNotEquals(1.2125, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.212333, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(0.0, aggregate.getBidPrices()[2], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertNotEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(0.0, aggregate.getBidQtys()[2], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertNotEquals(1.2125, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.2126, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(0.0, aggregate.getOfferPrices()[2], 0.00001);


        //The book is sorted at the offer side as well
        assertNotEquals(1000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(3000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(0.0, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(1, aggregate.getNumOffers());



        System.out.println("===== END : testVWAPFromMultiTierLPs_merge2T =====");
    }


    /**
     * Aggregate again after second rate update, without changing tiers only agg
     *     * @throws Exception
     */
    @Test
    public void testModifiableVWAPAddTiers_newrate() throws Exception {

        System.out.println("===== START : testModifiableVWAPAddTiers_newrate  =====");
        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl)serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(book1.getPricebook().getTiers());
        assertEquals(0,book1.getPricebook().getTiers().length);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====#1 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumOffers());

        //add tiers now
        book1.getPricebook().addTier(1000);
        book1.getPricebook().addTier(2000);
        book1.getPricebook().addTier(3000);

        aggregate = book1.aggregate();
        System.out.println("=====#2 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21206, aggregate.getBidPrices()[1], 0.00001);
        //  assertEquals(1.21207, aggregate.getBidPrices()[2], 0.001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        //  assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
        //     assertEquals(1.2122, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.21224, aggregate.getOfferPrices()[1], 0.0001);


        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        //assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumOffers());


        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

         aggregate = book1.aggregate();
         System.out.println("=====#3 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        //      assertEquals(1.2121, aggregate.getBidPrices()[1], 0.001);
        assertEquals(1.21203, aggregate.getBidPrices()[1], 0.00001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        //       assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
        //       assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.21227, aggregate.getOfferPrices()[1], 0.0001);


        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        //       assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);

        assertEquals(2, aggregate.getNumOffers());



    }

    /**
     * Aggregate again after second rate update, change tiers,
     * @throws Exception
     */
    @Test
    public void testModifiableVWAPAddTiers_newrateaddtiers() throws Exception {

        System.out.println("===== START : testModifiableVWAPAddTiers_newrate  =====");
        ServerProvision serverProvision = doDefaultProvision();
        //set node type to OnDemand
        ((ServerProvisionImpl)serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(book1.getPricebook().getTiers());
        assertEquals(0,book1.getPricebook().getTiers().length);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====#1 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(0, aggregate.getNumOffers());

        //add tiers now
        book1.getPricebook().addTier(1000);
        book1.getPricebook().addTier(2000);
        book1.getPricebook().addTier(3000);

        aggregate = book1.aggregate();
        System.out.println("=====#2 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21206, aggregate.getBidPrices()[1], 0.00001);
        //  assertEquals(1.21207, aggregate.getBidPrices()[2], 0.001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        //  assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
        //     assertEquals(1.2122, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.21224, aggregate.getOfferPrices()[1], 0.0001);


        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        //assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumOffers());


        book1.getPricebook().removeTier(1000);
        book1.getPricebook().addTier(2000);
        book1.getPricebook().addTier(5000);

        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.31, 1.32);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.31, 1.34);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("=====#3 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.31, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.30333, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.302, aggregate.getBidPrices()[2], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.33, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.34, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.342, aggregate.getOfferPrices()[2], 0.0001);


        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());


    }

    /**
     * Lp gives 0 liquidity so that agg is 0,
     * second rate update on LP gives 0, so that quote is dropped,
     * LP2 rate is aggregated which gives higher liquidity
      * @throws Exception
     */
    @Test
    public void testModifiableVWAPAddTiers_zeroliquidity() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testModifiableVWAPAddTiers  =====");
        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl)serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(book1.getPricebook().getTiers());
        assertEquals(0,book1.getPricebook().getTiers().length);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22, 0, 0);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24, 0, 0 );
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====#1 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1, aggregate.getNumOffers());

        //add tiers now
        book1.getPricebook().addTier(1000);
        book1.getPricebook().addTier(2000);
        book1.getPricebook().addTier(3000);

        aggregate = book1.aggregate();
        System.out.println("=====#2 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1, aggregate.getNumOffers());

        //second rate update, one LP gives liquidity, other give 0
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.22, 0, 0);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.21, 1.24, 5000, 5000 );
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("=====#2 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertEquals(3000, aggregate.getBidQtys()[0], 0.001);
        assertEquals(1, aggregate.getNumBids());
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(3000, aggregate.getOfferQtys()[0], 0.001);
        assertEquals(1, aggregate.getNumOffers());
    }

    /**
     * Publish rates from LP1 and LP2 such that agg book is inverted, so first quote gets dropped, and latest one is retained
     * Publish second rate update again and see if agg continues fine
     * @throws Exception
     */
    @Test
    public void testModifiableVWAP_Inverted() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testModifiableVWAPAddTiers  =====");
        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl)serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(book1.getPricebook().getTiers());
        assertEquals(0,book1.getPricebook().getTiers().length);

        //2 LPs causing inverted rates
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, 1.24, 2000,2000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        sleep(1);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.19, 1.20,2000,2000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        //add tiers now
        book1.getPricebook().addTier(1000);
        book1.getPricebook().addTier(2000);
        book1.getPricebook().addTier(3000);

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====#1 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        //LP1 quote is dropped, and LP2 quote is aggregated in Vwap
        assertEquals(1.19, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.18, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.00001);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.00001);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.21, aggregate.getOfferPrices()[1], 0.00001);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        //Publish valid quote from both LPs and see if aggregating fine
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("=====#1 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21206, aggregate.getBidPrices()[1], 0.00001);
        //  assertEquals(1.21207, aggregate.getBidPrices()[2], 0.001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        //  assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
        //     assertEquals(1.2122, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.21224, aggregate.getOfferPrices()[1], 0.0001);


        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        //assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumOffers());
    }

    /**
     * Publish rates from LP1 and LP2 such that agg book has same price for both bid and offer for tier
     * Publish second rate update again and see if agg continues fine
     * @throws Exception
     */
    @Test
    public void testModifiableVWAP_Samebidoffer() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testModifiableVWAPAddTiers  =====");
        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl)serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(book1.getPricebook().getTiers());
        assertEquals(0,book1.getPricebook().getTiers().length);

        //2 LPs causing inverted rates
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, 1.24, 2000,2000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.19, 1.21,2000,2000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        //add tiers now
        book1.getPricebook().addTier(1000);
        book1.getPricebook().addTier(2000);
        book1.getPricebook().addTier(3000);

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====#1 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        //LP1 quote is dropped, and LP2 quote is aggregated in Vwap
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.00001);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.00001);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.22, aggregate.getOfferPrices()[1], 0.00001);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        //Publish valid quote from both LPs and see if aggregating fine
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("=====#1 aggregate quote in testModifiableVWAPAddTiers  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21206, aggregate.getBidPrices()[1], 0.00001);
        //  assertEquals(1.21207, aggregate.getBidPrices()[2], 0.001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        //  assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
        //     assertEquals(1.2122, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.21224, aggregate.getOfferPrices()[1], 0.0001);


        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        //assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumOffers());
    }

    @Test
    public void testModifiableVWAPSingleLP() throws Exception {

        System.out.println("===== START : testModifiableVWAPSingleLP  =====");
        ServerProvision serverProvision = doDefaultProvision();

        //set node type to OnDemand
        ((ServerProvisionImpl)serverProvision).setNodeType(MDFEntity.NODE_ONDEMAND);

        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{5000000.0,10000000.0,15000000.0});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //Tiers defined in liquidity rules (avbl via FIProvision) to book should be ignored. As MDF
        // will wait for users to send tier in subscription.
        assertNotNull(book1.getPricebook().getTiers());
        assertEquals(0,book1.getPricebook().getTiers().length);

        double [] startbid1 = {1.18853,1.18852,1.18851,1.1885};
        double [] startoffer1 = {1.18861,1.18862,1.18863,1.18864};
        double [] bidliq1 = {1000000.0,2000000.0,3000000.0,4000000.0};
        double [] offerliq1 = {1000000.0,2000000.0,3000000.0,4000000.0};

        QuoteC quote = getQuoteCArray(serverProvision, EUR_USD, LP12, fiProvision, 4, 4,startbid1, startoffer1, bidliq1, offerliq1);

        //tiers
        set4TierRates(quote,QuoteC.BUY,1.18853,1.18852,1.18851,1.1885);
        set4TierRates(quote,QuoteC.SELL,1.18861,1.18862,1.18863,1.18864);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        //add tiers now
        book1.getPricebook().addTier(5000000.0);
        book1.getPricebook().addTier(10000000.0);
        book1.getPricebook().addTier(15000000.0);

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====#2 aggregate quote in testModifiableVWAPSingleLP  ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.18851, aggregate.getBidPrices()[0], 0.000001);
        assertEquals(0.0, aggregate.getBidPrices()[1], 0.00001);
        //  assertEquals(1.21207, aggregate.getBidPrices()[2], 0.001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(10000000.0,aggregate.getBidQtys()[0], 0.01);
        assertEquals(0.0, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.18863, aggregate.getOfferPrices()[0], 0.00001);
         assertEquals(0.0, aggregate.getOfferPrices()[1], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(10000000.0, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(0.0, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumOffers());

    }


}
