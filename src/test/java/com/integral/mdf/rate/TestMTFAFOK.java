package com.integral.mdf.rate;

import com.integral.mdf.data.*;
import com.integral.provision.MDFAggregationType;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;
import java.util.Map;
import java.util.Set;

import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;

public class TestMTFAFOK extends PriceAggregationTest {
    public static final String LP22 = "lp2";
    public static final String LP12 = "lp1";
    public static final String LP32 = "lp3";
    public static final String EUR_USD = "EUR/USD";


    @Test
    public void testMTFAFOK_MTlps () throws Exception {
    // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFAFOK_MTlps =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.20, 1.23);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAFOK_MTlps ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);

        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.23, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);

        assertEquals(3, aggregate.getNumOffers());
        System.out.println("===== END : testMTFAFOK_MTlps =====");
    }

    @Test
    public void testMTFAFOK_MTlps_withDiffLimits () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFAFOK_MTlps_withDiffLimits =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.20, 1.23, 2000, 2000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAFOK_MTlps_withDiffLimits ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);

        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.23, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);

        assertEquals(2, aggregate.getNumOffers());
        System.out.println("===== END : testMTFAFOK_MTlps_withDiffLimits =====");
    }

    @Test
    public void testMTFAFOK_MTlps_withDiffLimits_1 () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFAFOK_MTlps_withDiffLimits_1 =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{3000,4000,5000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.22, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.20, 1.23, 2000, 2000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAFOK_MTlps_withDiffLimits_1 ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.20, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.18, aggregate.getBidPrices()[1], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(3000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[1], 0.01);

        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(3000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[1], 0.01);

        assertEquals(2, aggregate.getNumOffers());
        System.out.println("===== END : testMTFAFOK_MTlps_withDiffLimits_1 =====");
    }

    @Test
    public void testMTFAFOK_MTlps_withDiffLimits_2 () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFAFOK_MTlps_withDiffLimits_2 =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{4000,5000,6000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.22, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.20, 1.23, 2000, 2000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFAFOK_MTlps_withDiffLimits_2 ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.18, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(4000.00, aggregate.getBidQtys()[0], 0.01);

        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(4000.00, aggregate.getOfferQtys()[0], 0.01);

        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : testMTFAFOK_MTlps_withDiffLimits_2 =====");
    }

    @Test
    public void testMTFAFOK_AggregationHigherTierLimit() throws Exception {
        // Configured tiers are higher than the LP limits, book will  be with the available limit in LPs highest tier
       System.out.println("===== START : testMTFAFOK_AggregationHigherTierLimit =====");

       ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{10000,20000,30000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("=====LP1 quote==="+quote.toString());


        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("=====LP2 quote==="+quote.toString());

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====aggregated testMTFAFOK_AggregationHigherTierLimit==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.16, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(6000.00, aggregate.getBidQtys()[0], 0.01);

        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.29, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(6000.00, aggregate.getOfferQtys()[0], 0.01);

        assertEquals(1, aggregate.getNumOffers());

       System.out.println("===== END : testMTFAFOK_AggregationHigherTierLimit =====");
    }

    @Test
    public void testMTFAFOK_AggregationHigherTierLimit_1() throws Exception {
        // Configured tiers are higher than the LP limits, book will be with the available limit in LPs highest tier, but it should not take from multiple LPs
        System.out.println("===== START : testMTFAFOK_AggregationHigherTierLimit_1 =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{6000,7000,8000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("=====LP1 quote==="+quote.toString());


        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("=====LP2 quote==="+quote.toString());

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====aggregated testMTFAFOK_AggregationHigherTierLimit_1==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.16, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(6000.00, aggregate.getBidQtys()[0], 0.01);

        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.29, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(6000.00, aggregate.getOfferQtys()[0], 0.01);

        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testMTFAFOK_AggregationHigherTierLimit_1 =====");
    }

    @Test
    public void testMTFAFOK_AggregationHigherQuoteLimit() throws Exception {
        // When LP quote (1st tier liquidity) is higher than the configured tiers, single tier with complete configured liquidity is shown
        System.out.println("===== START : testMTFAFOK_AggregationHigherQuoteLimit =====");
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{100,200,300});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("aggregated quote testMTFAFOK_AggregationHigherQuoteLimit= " + aggregate.toString());

        //assert For bid price
        assertEquals(1, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(300.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);

        System.out.println("===== END : testMTFAFOK_AggregationHigherQuoteLimit =====");
    }

   @Test
    public void testMTFAFOK_OneSidedBid() throws Exception {

       System.out.println("===== START : testMTFAFOK_OneSidedBid =====");

       ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.21d,500d,true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.22d,500d,true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("Aggregated book testMTFAFOK_OneSidedBid = "+aggregate.toString());

        //assert For bid price
        assertEquals(3, aggregate.getNumBids());
        assertEquals(0, aggregate.getNumOffers());
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.001);

        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);

       System.out.println("===== END : testMTFAFOK_OneSidedBid =====");
    }

    @Test
    public void testMTFAFOK_OneSidedBid_LiqFromMultipleLPs () throws Exception {

        System.out.println("===== START : testMTFAFOK_OneSidedBid_LiqFromMultipleLPs =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision,3,1.20d,2000d,true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision,3,1.22d,500d,true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("Aggregated book testMTFAFOK_OneSidedBid_LiqFromMultipleLPs = "+aggregate.toString());

        //assert For bid price
        assertEquals(3, aggregate.getNumBids());
        assertEquals(0, aggregate.getNumOffers());
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.001);

        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);

        System.out.println("===== END : testMTFAFOK_OneSidedBid_LiqFromMultipleLPs =====");
    }

    @Test
    public void testMTFAFOK_OneSidedOffer() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{100,200,300});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.24d,1000d,false);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.24d,1000d,false);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("book="+aggregate.toString());

        //assert For bid price
        assertEquals(0, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
        //   assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        //   assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
    }

    @Test
    public void testMTFAFOKAggregationOneSidedBidMultiTier() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{100,200,300});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.21d,1000d,true);
        quote.setQuoteType(QuoteC.MULTI_TIER);

        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.21d,1000d,true);
        quote.setQuoteType(QuoteC.MULTI_TIER);


        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("book="+aggregate.toString());

        //assert For bid price
        assertEquals(1, aggregate.getNumBids());
        assertEquals(0, aggregate.getNumOffers());
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(300.00, aggregate.getBidQtys()[0], 0.01);
        //   assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        //   assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
    }
   @Test
    public void testMTFAFOKAggregationOneSidedOfferMultiQuote() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{100,200,300});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.24d,1000d,false);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.24d,1000d,false);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("book="+aggregate.toString());

        //assert For bid price
        assertEquals(0, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
        //   assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        //   assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
    }
    @Test
    public void testMTFAFOKAggregationOneSidedOfferMultiTier() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{100,200,300});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.24d,1000d,false);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.24d,1000d,false);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("book="+aggregate.toString());

        //assert For bid price
        assertEquals(0, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
        //   assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        //   assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
    }

    @Test
    public void testMTFAFOKAggregation_requestedAmtAcrossMultipleTiers_OneSidedOfferMultiTier() throws Exception {
       // not working as expected, last tier should not be seen for less than the requested amt
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1500,2500,3500});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision,3,1.24d,1000d,false);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision,3,1.24d,1000d,false);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("book="+aggregate.toString());

        //assert For bid price
        assertEquals(0, aggregate.getNumBids());
        assertEquals(2, aggregate.getNumOffers());
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
    }

    @Test
    public void testMTFAFOKAggregation_requestedAmtInLastTier_OneSidedOfferMultiTier() throws Exception {
        // not working as expected, last tier should not be seen for less than the requested amt
        // expected result - 1.26 with 2500
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{2500,3500,4500});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision,3,1.24d,1000d,false);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision,3,1.24d,1000d,false);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("book="+aggregate.toString());

        //assert For bid price
        assertEquals(0, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1.26, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(3000.00, aggregate.getOfferQtys()[0], 0.01);
        //   assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        //   assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
    }

    @Test
    public void testMTFAFOKAggregation_requestedAmtInLastTier_OneSidedOfferMultiTierWithDifferentRates() throws Exception {
        // not working as expected, last tier should not be seen for less than the requested amt
        // expected result - 1.25 with 2500
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        //impl.setAggregationTiers(new double[]{2500,3500,4500});
        impl.setAggregationTiers(new double[]{3500,4500,5500});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision,3,1.24d,1000d,false);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision,3,1.23d,1000d,false);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("book="+aggregate.toString());

        //assert For bid price
        assertEquals(0, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(3000.00, aggregate.getOfferQtys()[0], 0.01);

    }

    @Test
    public void MTFOK_FirstTierZeroLiquidity() throws Exception {
        System.out.println("==============START:MTFOK_FirstTierZeroLiquidity==================== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationTiers(new double[]{1000,2000,3000,4000,5000});


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.22,1.24,1000,1000);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3,1.20d, 1.22d, 2000, 2000);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== first aggregate quote in MTFOK_FirstTierZeroLiquidity ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.23, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 3,1.22,1.24,0,1000);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregate second quote in MTFOK_FirstTierZeroLiquidity ==="+aggregate.toString());

        // Uncomment based on the feedback from Peeyush
        //assertNotEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.23, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        assertEquals(2, aggregate.getBookId());



        //assertNotEquals(1.22, aggregate.getBidPrices()[0], 0.001);



        assertEquals(1.21, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.23, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        assertEquals(2, aggregate.getBookId());


        //LP1 send an updated quote with 0 liquidity on offer side, LP2 quote remains as it is
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 3,1.22,1.24,1000,0);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);
        System.out.println("=====LP1 third quote with 0 liquidity on offer side==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregated third quote in MTFOK_FirstTierZeroLiquidity ==="+aggregate.toString());

        //assertNotEquals(1.22, aggregate.getBidPrices()[0], 0.001);

        assertEquals(1.22, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertNotEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.23, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        assertEquals(3, aggregate.getBookId());

        System.out.println("==============END : MTFOK_FirstTierZeroLiquidity==================== ");

    }



    @Test
    public void MTFOK_MiddleTierZeroLiquidity() throws Exception {
        System.out.println("==============START:MTFOK_MiddleTierZeroLiquidity==================== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationTiers(new double[]{1000,2000,3000,4000,5000});


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.22,1.24,1000,1000);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3,1.20d, 1.22d, 2000, 2000);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== first aggregate quote in MTFOK_FirstTierZeroLiquidity ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        System.out.println("============== after bid===========");
        //assert For offer price
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.23, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());

        double [] startbid1 = {1.22,1.21,1.20};
        double [] startoffer1 = {1.34,1.35,1.36};
        double [] bidliq1 = {1000,0.0,3000};
        double [] offerliq1 = {1000,2000,3000};

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteCArray(serverProvision, EUR_USD, LP12, fiProvision, 3, 3,startbid1, startoffer1, bidliq1, offerliq1);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregate second quote in MTFOK_MiddleTierZeroLiquidity ==="+aggregate.toString());

        //assertNotEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.23, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        assertEquals(2, aggregate.getBookId());


        //LP1 send an updated quote with 0 liquidity on offer side, LP2 quote remains as it is
        double [] startbid2 = {1.22,1.21,1.20};
        double [] startoffer2 = {1.34,1.35,1.36};
        double [] bidliq2 = {1000,2000.0,3000};
        double [] offerliq2 = {1000,0,3000};

        quote = getQuoteCArray(serverProvision, EUR_USD, LP12, fiProvision, 3, 3,startbid2,startoffer2, bidliq2,offerliq2);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);
        System.out.println("=====LP1 third quote with 0 liquidity on offer side==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregated third quote in MTFOK_MiddleTierZeroLiquidity ==="+aggregate.toString());

        //assertNotEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertNotEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.23, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        assertEquals(3, aggregate.getBookId());

        System.out.println("============== END : MTFOK_MiddleTierZeroLiquidity ==================== ");


    }

    @Test
    public void MTFOK_LastTierZeroLiquidity() throws Exception {
        //As of now,.. 0 liquidity sent from ME comes as 0 only from MDF as well
        //Change will be made to drop this quote with 0 liquidity,. then the test case will fail
        System.out.println("==============START:MTFOK_LastTierZeroLiquidity==================== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationTiers(new double[]{1000,2000,3000,4000});

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 5, 1.22,1.33,1000,1000);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 5,1.21d, 1.34d, 1000d, 1000d);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.33, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.34, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.35, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.36, aggregate.getOfferPrices()[3], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());

        double [] startbid = {1.22,1.21,1.20,1.19};
        double [] startoffer = {1.33,1.34,1.35,1.36};
        double [] bidliq = {1000,2000,3000.0,0.0};
        double [] offerliq = {1000,2000,3000.0,4000.0};

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteCArray(serverProvision, EUR_USD, LP12, fiProvision, 4, 4,startbid,startoffer,bidliq,offerliq);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregate second quote in MEzeroliquidity_TierTwo ==="+aggregate.toString());

        assertEquals(1.22, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
 //       assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.33, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.34, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.35, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.36, aggregate.getOfferPrices()[3], 0.01);

        //The book is sorted at the offer side as well
        assertNotEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.0, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.0, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(4000.0, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());
        assertEquals(2, aggregate.getBookId());


        //LP1 send an updated quote with 0 liquidity on offer side, LP2 quote remains as it is
        double [] startbid2 = {1.22,1.21,1.20,1.19};
        double [] startoffer2 = {1.33,1.34,1.35,1.36};
        double [] bidliq2 = {1000,2000,3000.0,4000.0};
        double [] offerliq2 = {1000,2000,3000.0,0};

        quote = getQuoteCArray(serverProvision, EUR_USD, LP12, fiProvision, 4, 4,startbid2,startoffer2, bidliq2,offerliq2);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);
        System.out.println("=====LP1 third quote with 0 liquidity on offer side==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregated third quote in MTFOK_MiddleTierZeroLiquidity ==="+aggregate.toString());

        //assertNotEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertNotEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.33, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.34, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.35, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.37, aggregate.getOfferPrices()[3], 0.01);

        //The book is sorted at the offer side as well
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[3], 0.01);

        assertEquals(4, aggregate.getNumOffers());
        assertEquals(3, aggregate.getBookId());

        System.out.println("==============END : MTFOK_LastTierZeroLiquidity==================== ");

    }

    @Test
    public void testMTFAFOKAggregationInvertedRatesFromMQLPs() throws Exception {
        System.out.println("===== START : testMTFAFOKAggregationInvertedRatesFromMQLPs =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //      QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 5, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);

        //      quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 5, 1.17, 1.20);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("=====aggregated quote==="+ aggregate.toString());

        //assert For bid price
        assertEquals(1.17, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.16, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.15, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.20, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.21, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.22, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        System.out.println("===== END : testMTFAFOKAggregationInvertedRatesFromMQLPs =====");
    }

    @Test
    public void testMTFAFOKAggregationInvertedRatesFromMQLPs1() throws Exception {
        System.out.println("===== START : testMTFAFOKAggregationInvertedRatesFromMQLPs1 =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //      QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 5, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);

        //      quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 5, 1.17, 1.20);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        //      quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP2 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 5, 1.16, 1.18);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);

        System.out.println("=====LP3 quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();

        System.out.println("=====aggregated quote==="+ aggregate.toString());

        //assert For bid price
        assertEquals(1.17, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.16, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.15, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.18, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.19, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        System.out.println("===== END : testMTFAFOKAggregationInvertedRatesFromMQLPs1 =====");
    }

    @Test
    public void testMTFAFOKAggregationInvertedRatesFromMQLPs2() throws Exception {
        System.out.println("===== START : testMTFAFOKAggregationInvertedRatesFromMQLPs2 =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{2000,4000,5000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //      QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);

        //      quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 4, 1.17, 1.20);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        //      quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP2 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 5, 1.16, 1.18);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);

        System.out.println("=====LP3 quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();

        System.out.println("=====aggregated quote==="+ aggregate.toString());

        //assert For bid price
        assertEquals(1.16, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.14, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.12, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.19, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.21, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.22, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        System.out.println("===== END : testMTFAFOKAggregationInvertedRatesFromMQLPs2 =====");
    }

    @Test
    public void testMTFAFOKAggregationInvertedRatesFromMQLPs3() throws Exception {
        System.out.println("===== START : testMTFAFOKAggregationInvertedRatesFromMQLPs3 =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1500,2500,3500});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.FA_MULTI_TIER);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //      QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 5, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);

        //      quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 5, 1.17, 1.20);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        //      quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP2 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 5, 1.16, 1.18);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        quote.setFullAmountStream(true);
        manager.handleRate(quote);

        System.out.println("=====LP3 quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();

        System.out.println("=====aggregated quote==="+ aggregate.toString());

        //assert For bid price
        assertEquals(1.16, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.15, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.14, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3500.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.19, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.20, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.21, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3500.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        System.out.println("===== END : testMTFAFOKAggregationInvertedRatesFromMQLPs3 =====");
    }

}
