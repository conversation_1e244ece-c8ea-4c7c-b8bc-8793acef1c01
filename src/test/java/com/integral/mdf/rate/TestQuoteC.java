package com.integral.mdf.rate;

import org.junit.Test;

import com.integral.mdf.data.QuoteC;

public class TestQuoteC {
	
	@Test
	public void testQuoteC(){
		
		QuoteC quote = new QuoteC();

		quote.setPrice(quote.bidTiersOffset(), 1.21);

		quote.setBidTiersNum(quote.getBidTiersNum()+1);
		int bid1 = quote.tierOffset(QuoteC.BUY, 0);
		quote.setPrice(bid1, 1.21);
		quote.setTotalQty(bid1, 1000.0d);
		quote.setShowQty(bid1, 1000.0d);
		
		quote.setBidTiersNum(quote.getBidTiersNum()+1);
		bid1 = quote.tierOffset(QuoteC.BUY, 1);
		quote.setPrice(bid1, 1.20);
		quote.setTotalQty(bid1, 1000.0d);
		quote.setShowQty(bid1, 1000.0d);
		
		quote.setOfferTiersNum(quote.getOfferTiersNum()+1);
		int offer1 = quote.tierOffset(QuoteC.SELL, 0);
		quote.setPrice(offer1, 1.22);
		quote.setTotalQty(offer1, 1000.0d);
		quote.setShowQty(offer1, 1000.0d);

		System.out.println(quote.toString());
		
	}
	
}
