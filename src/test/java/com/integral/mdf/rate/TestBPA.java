package com.integral.mdf.rate;

import com.integral.mdf.data.*;
import com.integral.provision.CurrencyPairProvision;
import com.integral.provision.MDFAggregationType;
import org.junit.Assert;
import org.junit.Test;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;

public class TestBPA extends PriceAggregationTest {

    public static final String LP22 = "lp2";
    public static final String LP12 = "lp1";
    public static final String LP32 = "lp3";
    public static final String LP41 = "lp4";
    public static final String LP51 = "lp5";
    public static final String LP61 = "lp6";
    public static final String EUR_USD = "EUR/USD";

    @Test
    public void TC1_BPAFromSQLPs () throws Exception {

        System.out.println("===== START : TC1_BPAFromSQLPs =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, 1.25);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in TC1_BPAFromSQLPs ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : TC1_BPAFromSQLPs =====");
    }

    @Test
    public void TC2_BPAFromSQLPCausingInvertedTiers () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote

        System.out.println("===== START : TC2_BPAFromSQLPCausingInvertedTiers =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        sleep(1);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.19, 1.20);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in TC2_BPAFromSQLPCausingInvertedTiers ==="+aggregate.toString());

        //assert For bid price
        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1.19, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : TC2_BPAFromSQLPCausingInvertedTiers =====");

    }

    @Test
    public void TC3_BPAFromSQLP4LPs () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote
        System.out.println("===== START : TC3_BPAFromSQLP4LPs =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.28);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1,1.22, 1.30 );
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 1, 1.23, 1.29);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP41, fiProvision, 1, 1.24, 1.31);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP4 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in TC3_BPAFromSQLP4LPs ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.24, aggregate.getBidPrices()[0], 0.001);
        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.28, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : TC3_BPAFromSQLP4LPs =====");

    }

    @Test
    public void TC4_BPAFromSQLP6LPs () throws Exception {
        System.out.println("===== START : TC4_BPAFromSQLP6LPs =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        System.out.println("RateDistributionManager" +manager);
        manager.createRateBooks(fiProvision);
        System.out.println("RateDistributionManager 2" +manager);
        System.out.println("manager.rateChannels.size()" + manager.rateChannels.size());
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.28);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());


        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1,1.22, 1.30 );
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

         quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 1, 1.23, 1.29);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote==="+quote.toString());



        quote = getQuoteC(serverProvision, EUR_USD, LP41, fiProvision, 1, 1.24, 1.31);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP4 quote==="+quote.toString());



        quote = getQuoteC(serverProvision, EUR_USD, LP51, fiProvision, 1, 1.23, 1.31);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP5 quote==="+quote.toString());



        quote = getQuoteC(serverProvision, EUR_USD, LP61, fiProvision, 1, 1.24, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP6 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in TC4_BPAFromSQLP6LPs ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.24, aggregate.getBidPrices()[0], 0.001);
        // old comment - testing the aggregation for merging the quotes from provider 1 & 2
        // current behaviour - when price is same, liquidity is not combined. Just one of the quote is picked up
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : TC4_BPAFromSQLP6LPs =====");

    }

    @Test
    public void TC5_BPAFromMQLPCausingInvertedTiers () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote

        System.out.println("===== START : TC5_BPAFromMQLPCausingInvertedTiers =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);
        sleep(1);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.19, 1.20);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in TC5_BPAFromMQLPCausingInvertedTiers ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.19, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : TC5_BPAFromMQLPCausingInvertedTiers =====");

    }

    @Test
    public void TC6_BPAFromMTLPs () throws Exception {

        // Incase of MT quote from LP, single tier will be taken for aggregation
        System.out.println("===== START : TC6_BPAFromMTLPs =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 5, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 7, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in TC6_BPAFromMTLPs ==="+aggregate.toString());

        //Merging of tiers with same Best price doesnt happen in BPA
        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : TC6_BPAFromMTLPs =====");

    }

    @Test
    public void TC7_BPAFromMTLPsCausingInvertedTiers () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote
        System.out.println("===== START : TC7_BPAFromMTLPsCausingInvertedTiers =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.19, 1.20);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in TC7_BPAFromMTLPsCausingInvertedTiers ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.19, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : TC7_BPAFromMTLPsCausingInvertedTiers =====");

    }

    @Test
    public void TC8_BPAAggregationLargerAmount() throws Exception {
    // When rates are same, provider tier with higher liquidity is considered in BP
        System.out.println("==============START:TC8_BPAAggregationLargerAmount==================== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 6,1.21d, 1.24d, 2000d, 2000d);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in TC8_BPAAggregationLargerAmount ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : TC8_BPAAggregationLargerAmount =====");
    }

    /**
     * 2021-01-28 15:29:33,263 QuoteC [index =0 getVersion()=1, getMessageType()=24, getVenueIdx()=33951, getProviderIdx()=35697, getStreamIdx()=314, getQuoteType()=0, getCategory()=512, getCcyPairIdx()=6094849, getQuoteId()=134861
     * 9859744, getSessionId()=-**********,valuedate=18660, bidTiersOffset()=57, offerTiersOffset()=113, getBidTiersNum()=2, getOfferTiersNum()=2, effectiveTime=1611847773254][ bids={price=0.71641,totalQty=1000000.0,showQty=1000000
     * .0,cnt=0}{price=0.71631,totalQty=1000000.0,showQty=1000000.0,cnt=0}][ offers={price=0.0,totalQty=0.0,showQty=0.0,cnt=0}{price=0.0,totalQty=0.0,showQty=0.0,cnt=0}]
     * @throws Exception
     *
     * 
     *
     */
    @Test
    public void testBPAOneSidedMalformedBidRate() throws Exception {
        //Fails since num of offers value expected is 0, comes as 1
        //Full book same test case gives value as 0
        System.out.println("===== START : testBPAOneSidedMalformedBidRate =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
       // QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate after only quote#1 in testBPAOneSidedMalformedBidRate ==="+aggregate.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, true);
        quote.setOfferTiersNum(1);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testBPAOneSidedMalformedBidRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.001);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.001);
        //assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);

        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);
        System.out.println("=====aggregate.getNumOffers()[0]=="+aggregate.getNumOffers());
        assertEquals(1000, aggregate.getOfferQtys()[0],0.001);
        assertEquals(1, aggregate.getNumOffers()); //incorrect value incase of BPA. Ideally it should have been 0, discussed with eng and looks like this is needed to display top tier with 0 value
        System.out.println("===== END : testBPAOneSidedMalformedBidRate =====");

    }

    @Test
    public void TC9_BPAFromSQLpsGivingOneSidedBidRate () throws Exception {
        //Fails since num of offers value expected is 0, comes as 1
       //Full book same test case gives value as 0
        System.out.println("===== START : TC9_BPAFromSQLpsGivingOneSidedBidRate =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in TC9_BPAFromSQLpsGivingOneSidedBidRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.001);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        //assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);

        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);
        System.out.println("=====aggregate.getNumOffers()[0]=="+aggregate.getNumOffers());
        assertEquals(0, aggregate.getOfferQtys()[0],0.001);
        assertEquals(1, aggregate.getNumOffers()); //incorrect value incase of BPA. Ideally it should have been 0, discussed with eng and looks like this is needed to display top tier with 0 value
        System.out.println("===== END : TC9_BPAFromSQLpsGivingOneSidedBidRate =====");

    }

    @Test
    public  void TC10_BPAFromMQLPs () throws Exception {

        System.out.println("========= START: TC10_BPAFromMQLPs =========== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl Impl = (FIProvisionImpl) fiProvision;
        Impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(rateBook.getFIIndex(), fiProvision.getIndex());
            }
        }

            RateBook book1 = getBook(serverProvision, fiProvision, manager);
            RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

            Assert.assertNotNull(book1);
            //Assertion to verify that the book is not same across LPs
            Assert.assertSame("Book is not same across LPs", book1, book2);

            //Provision another FI
            //service.provision(TESTFI2);

            QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 5, 1.21, 1.24);
            //quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
            manager.handleRate(quote);
            System.out.println("========quote from LP1===========" + quote.toString());

            quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 7, 1.21, 1.24);
            //quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
            manager.handleRate(quote);
            System.out.println("============quote from LP2=========" + quote.toString());

            PriceBook aggregate = book1.aggregate();
            System.out.println("============aggregated quote TC10_BPAFromMQLPs ==========" + aggregate.toString());

            //assert for bid price
            Assert.assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
            Assert.assertEquals(1000,aggregate.getBidQtys()[0],0.01);
            Assert.assertEquals(1.24,aggregate.getOfferPrices()[0],0.001);
            Assert.assertEquals(1000,aggregate.getOfferQtys()[0],0.01);

            System.out.println("===== END : TC10_BPAFromMQLPs =====");

        }

    @Test
    public void TC11_BPAFromMTandMQLP() throws Exception {

        System.out.println("===== START : TC11_BPAFromMTandMQLP =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 5, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 7, 1.23, 1.29);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in TC11_BPAFromMTandMQLP ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.23, aggregate.getBidPrices()[0], 0.01);
       //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : TC11_BPAFromMTandMQLP =====");

    }

    @Test
    public void TC12_BPAFromImbalancedTiers() throws Exception {

        System.out.println("=========START: TC12_BPAFromImbalancedTiers==============");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 4, 2, 1.23, 1.30);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 4, 2, 1.21, 1.24);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====aggregate quote in TC12_BPAFromImbalancedTiers ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.23, aggregate.getBidPrices()[0], 0.01);
         //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
       System.out.println("===== END : TC12_BPAFromImbalancedTiers =====");

   }

   //Throws exception when the aggregation method is BEST PRICE, needs to be checked
    @Test
    public void TC13_BPAFromMQLpGivingInvertedTiers () throws Exception {
        // Quote causing inverted quote should be dropped,
        //noOfTiers is 1 since MDF has to send quote with 0 rate and 1 tier size to represent inactive quote to MDG,
        //otherwise MDG will not be able to recognize inactive quote

        System.out.println("===== START : TC13_BPAFromMQLpGivingInvertedTiers =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.24, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());


            PriceBook aggregate = book1.aggregate();
           // System.out.println("===== aggregate quote in TC13_BPAFromMQLpGivingInvertedTiers ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : TC13_BPAFromMQLpGivingInvertedTiers =====");

    }

    @Test //Throws exception when the aggregation method is BEST PRICE
    public void TC14_BPAFromSQLpGivingInvertedTiers () throws Exception {
        // Quote causing inverted quote should be dropped,
        //noOfTiers is 1 since MDF has to send quote with 0 rate and 1 tier size to represent inactive quote to MDG,
        //otherwise MDG will not be able to recognize inactive quote

        System.out.println("===== START : TC14_BPAFromSQLpGivingInvertedTiers =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.24, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in TC14_BPAFromSQLpGivingInvertedTiers ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : TC14_BPAFromSQLpGivingInvertedTiers =====");

    }

    @Test  //Test case fixed and re-written testMEzeroliquidity_BestPriceFixed
    public void TC15_MEzeroliquidity() throws Exception {
        //As of now,.. 0 liquidity sent from ME comes as 0 only from MDF as well
        //Change will be made to drop this quote with 0 liquidity,. then the test case will fail
        System.out.println("==============START:TC15_MEzeroliquidity==================== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.22,1.24,1000,1000);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3,1.21d, 1.24d, 1000d, 1000d);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote TC15_MEzeroliquidity ==="+aggregate.toString());

        //assert bid tier
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert offer tier
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 3,1.22,1.24,0,1000);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregate second quote in TC15_MEzeroliquidity ==="+aggregate.toString());

        //bid tier
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert offer tier
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(2, aggregate.getBookId());
        System.out.println("===== END : TC15_MEzeroliquidity =====");
    }

    @Test
    public void TC16_MEzeroliquidity_BestPriceFixed() throws Exception {
        //As of now,.. 0 liquidity sent from ME comes as 0 only from MDF as well
        //Change will be made to drop this quote with 0 liquidity,. then the test case will fail
        System.out.println("==============START:TC16_MEzeroliquidity_BestPriceFixed==================== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.22,1.24,1000,1000);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3,1.21d, 1.24d, 1000d, 1000d);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        //quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 3,1.22,1.24,0,1000);
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 3,1.22,1.24,0,0);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregate second quote in TC16_MEzeroliquidity_BestPriceFixed ==="+aggregate.toString());

        assertNotEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.01);

        //testing the aggregation
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        assertEquals(2, aggregate.getBookId());

        System.out.println("===== END : TC16_MEzeroliquidity_BestPriceFixed =====");

    }

    @Test
    public void TC17_MEzeroliquidity_BestPriceFixed() throws Exception {
        //As of now,.. 0 liquidity sent from ME comes as 0 only from MDF as well
        //Change will be made to drop this quote with 0 liquidity,. then the test case will fail
        System.out.println("==============START:TC17_MEzeroliquidity_BestPriceFixed==================== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.23,1.24,1000,1000);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3,1.24d, 1.25d, 1000d, 1000d);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.24, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        sleep(1);
        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        //quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 3,1.22,1.24,0,1000);
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 3,1.22,1.24,0,0);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregate second quote in TC17_MEzeroliquidity_BestPriceFixed ==="+aggregate.toString());

        assertNotEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.24, aggregate.getBidPrices()[0], 0.01);

        //testing the aggregation
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        assertEquals(2, aggregate.getBookId());

        System.out.println("===== END : TC17_MEzeroliquidity_BestPriceFixed =====");
    }

    @Test //PLT-3271 fixed
    public void TC18_BPAFromSQLPs_samebidoffer () throws Exception {

        System.out.println("===== START : TC18_BPAFromSQLPs_samebidoffer =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.19, 1.21);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in TC18_BPAFromSQLPs_samebidoffer ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());
        //assert For offer price
        assertEquals(1.21, aggregate.getOfferPrices()[0], 0.01);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        //Test aggregation after second quote
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.19, 1.23);
        manager.handleRate(quote);
        System.out.println("=====LP2 second quote ==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== second aggregate quote in TC18_BPAFromSQLPs_samebidoffer ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());
        //assert For offer price
        assertEquals(1.23, aggregate.getOfferPrices()[0], 0.01);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : TC18_BPAFromSQLPs_samebidoffer =====");
    }

    @Test
    public  void TC19_BPAFromMQLPs_samebidoffer () throws Exception {

        System.out.println("========= START: TC19_BPAFromMQLPs_samebidoffer =========== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl Impl = (FIProvisionImpl) fiProvision;
        Impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(rateBook.getFIIndex(), fiProvision.getIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        //Assertion to verify that the book is not same across LPs
        Assert.assertSame("Book is not same across LPs", book1, book2);

        //Provision another FI
        //service.provision(TESTFI2);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        //quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("========quote from LP1===========" + quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.19, 1.21);
        //quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("============quote from LP2=========" + quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("============aggregated quote TC19_BPAFromMQLPs_samebidoffer==========" + aggregate.toString());

        //assert for bid price
        Assert.assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        Assert.assertEquals(1000,aggregate.getBidQtys()[0],0.01);
        Assert.assertEquals(1.21,aggregate.getOfferPrices()[0],0.001);
        Assert.assertEquals(1000,aggregate.getOfferQtys()[0],0.01);
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1, aggregate.getNumBids());

        System.out.println("===== END : TC19_BPAFromMQLPs_samebidoffer =====");

    }

    @Test
    public void TC20_BPAOneSidedRateLastToAggregate() throws Exception {
        System.out.println("===== START : TC20_BPAOneSidedRateLastToAggregate =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in TC20_BPAOneSidedRateLastToAggregate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.001);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1000, aggregate.getOfferQtys()[0],0.001);
        assertEquals(1, aggregate.getNumOffers()); //in case of BPA

        System.out.println("===== END : TC20_BPAOneSidedRateLastToAggregate =====");

    }

    /**PLT-3679 TOB  Three LPs, First two LPs give valid rates,
     * LP3 gives one-sided bid rate, offer rate should be picked from the other LPs (LP1 or LP2)
     * after aggregation, bid shud have lowest then offer shud have highest
      * @throws Exception
     */
    @Test
    public void TC21_BPAlastonesidedrate3LPsBid () throws Exception {

        // Incase of MT quote from LP, single tier will be taken for aggregation
        System.out.println("===== START : TC21_BPAlastonesidedrate3LPsBid =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();
        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.30487, 1.30526, 1000000, 1000000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        double [] startbid1 = {1.30465,1.3045,1.30442};
        double [] startoffer1 = {1.30524,1.30539,1.30546};
        double [] bidliq1 = {1000000,3000000,5000000};
        double [] offerliq1 = {1000000,3000000,5000000};

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteCArray(serverProvision, EUR_USD, LP22, fiProvision, 3, 3,startbid1, startoffer1, bidliq1, offerliq1);
        manager.handleRate(quote);
        System.out.println("=====LP2 second quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 1, 1.30521, 1000000,true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote 2==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====1 :aggregate quote in TC21_BPAlastonesidedrate3LPsBid ==="+aggregate.toString());

        //assert for bid price
        Assert.assertEquals(1.30521, aggregate.getBidPrices()[0], 0.000001);
        Assert.assertEquals(1000000,aggregate.getBidQtys()[0],0.00001);
        Assert.assertEquals(1.30524,aggregate.getOfferPrices()[0],0.000001);
        Assert.assertEquals(1000000,aggregate.getOfferQtys()[0],0.00001);
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1, aggregate.getNumBids());
    }

    @Test
    public void TC22_BPAfirstonesidedrateOffer () throws Exception {

        // Incase of MT quote from LP, single tier will be taken for aggregation
        System.out.println("===== START : TC22_BPAfirstonesidedrateOffer =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);
        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();
        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.30487, 1000000,false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote ==="+quote.toString());

        double [] startbid1 = {1.30465,1.3045,1.30442};
        double [] startoffer1 = {1.30524,1.30539,1.30546};
        double [] bidliq1 = {1000000,3000000,5000000};
        double [] offerliq1 = {1000000,3000000,5000000};

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteCArray(serverProvision, EUR_USD, LP22, fiProvision, 3, 3,startbid1, startoffer1, bidliq1, offerliq1);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====1 :aggregate quote in TC22_BPAfirstonesidedrateOffer ==="+aggregate.toString());

        //assert for bid price
        Assert.assertEquals(1.30465, aggregate.getBidPrices()[0], 0.00001);
        Assert.assertEquals(1000000,aggregate.getBidQtys()[0],0.00001);
        Assert.assertEquals(1.30487,aggregate.getOfferPrices()[0],0.00001);
        Assert.assertEquals(1000000,aggregate.getOfferQtys()[0],0.00001);
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1, aggregate.getNumBids());
    }

    @Test
    public void TC23_BPAfirstonesidedrateBid () throws Exception {
        System.out.println("===== START : TC23_BPAfirstonesidedrateBid =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);
        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();
        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.30487, 1000000,true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote ==="+quote.toString());

        double [] startbid1 = {1.30465,1.3045,1.30442};
        double [] startoffer1 = {1.30524,1.30539,1.30546};
        double [] bidliq1 = {1000000,3000000,5000000};
        double [] offerliq1 = {1000000,3000000,5000000};

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteCArray(serverProvision, EUR_USD, LP22, fiProvision, 3, 3,startbid1, startoffer1, bidliq1, offerliq1);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====1 :aggregate quote in TC23_BPAfirstonesidedrateBid ==="+aggregate.toString());

        //assert for bid price
        Assert.assertEquals(1.30487, aggregate.getBidPrices()[0], 0.00001);
        Assert.assertEquals(1000000,aggregate.getBidQtys()[0],0.00001);
        Assert.assertEquals(1.30524,aggregate.getOfferPrices()[0],0.00001);
        Assert.assertEquals(1000000,aggregate.getOfferQtys()[0],0.00001);
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1, aggregate.getNumBids());
    }

    @Test
    public void TC24_BPAfirstonesidedrateSameBid () throws Exception {

        // Incase of MT quote from LP, single tier will be taken for aggregation
        System.out.println("===== START : TC24_BPAfirstonesidedrateSameBid =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);
        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();
        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.30524, 1000000,true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote ==="+quote.toString());

        double [] startbid1 = {1.30465,1.3045,1.30442};
        double [] startoffer1 = {1.30524,1.30539,1.30546};
        double [] bidliq1 = {1000000,3000000,5000000};
        double [] offerliq1 = {1000000,3000000,5000000};

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteCArray(serverProvision, EUR_USD, LP22, fiProvision, 3, 3,startbid1, startoffer1, bidliq1, offerliq1);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====1 :aggregate quote in TC24_BPAfirstonesidedrateSameBid ==="+aggregate.toString());

        //assert for bid price
        Assert.assertEquals(1.30524, aggregate.getBidPrices()[0], 0.00001);
        Assert.assertEquals(1000000,aggregate.getBidQtys()[0],0.00001);
        Assert.assertEquals(1.30524,aggregate.getOfferPrices()[0],0.00001);
        Assert.assertEquals(1000000,aggregate.getOfferQtys()[0],0.00001);
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1, aggregate.getNumBids());
    }

    @Test
    public void TC25_BPAfirstonesidedrateOfferinverted () throws Exception {

        System.out.println("===== START : TC25_BPAfirstonesidedrateOfferinverted =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);
        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();
        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.30387, 1000000,false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote ==="+quote.toString());

        double [] startbid1 = {1.30465,1.3045,1.30442};
        double [] startoffer1 = {1.30524,1.30539,1.30546};
        double [] bidliq1 = {1000000,3000000,5000000};
        double [] offerliq1 = {1000000,3000000,5000000};

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteCArray(serverProvision, EUR_USD, LP22, fiProvision, 3, 3,startbid1, startoffer1, bidliq1, offerliq1);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====1 :aggregate quote in TC25_BPAfirstonesidedrateOfferinverted ==="+aggregate.toString());

        //first rate is dropped, since it causes the book to be inverted
        Assert.assertEquals(1.30465, aggregate.getBidPrices()[0], 0.00001);
        Assert.assertEquals(1000000,aggregate.getBidQtys()[0],0.00001);
        Assert.assertEquals(1.30524,aggregate.getOfferPrices()[0],0.00001);
        Assert.assertEquals(1000000,aggregate.getOfferQtys()[0],0.00001);
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1, aggregate.getNumBids());
    }

    //rate causing book to be inverted is dropped,  second rate is retained
    @Test
    public void TC26_BPAlastonesidedrateOfferinverted () throws Exception {

        System.out.println("===== START : TC26_BPAlastonesidedrateOfferinverted =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);
        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();
        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        double [] startbid1 = {1.30465,1.3045,1.30442};
        double [] startoffer1 = {1.30524,1.30539,1.30546};
        double [] bidliq1 = {1000000,3000000,5000000};
        double [] offerliq1 = {1000000,3000000,5000000};

        QuoteC quote = getQuoteCArray(serverProvision, EUR_USD, LP22, fiProvision, 3, 3, startbid1, startoffer1, bidliq1, offerliq1);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());
        sleep(1);
        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.30387, 1000000,false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote ==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====1 :aggregate quote in TC26_BPAlastonesidedrateOfferinverted ==="+aggregate.toString());

        Assert.assertEquals(0.0, aggregate.getBidPrices()[0], 0.00001);
        Assert.assertEquals(0.0,aggregate.getBidQtys()[0],0.00001);
        Assert.assertEquals(1.30387,aggregate.getOfferPrices()[0],0.00001);
        Assert.assertEquals(1000000,aggregate.getOfferQtys()[0],0.00001);
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1, aggregate.getNumBids());
    }

    //rate causing book to be inverted is dropped,  one-sided rate is retained
    @Test
    public void TC27_BPAfirstonesidedrateBidInverted () throws Exception {

        System.out.println("===== START : TC27_BPAfirstonesidedrateBidInverted =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);
        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();
        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 1, 1.30678, 1000000,true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote ==="+quote.toString());

        double [] startbid1 = {1.30465,1.3045,1.30442};
        double [] startoffer1 = {1.30524,1.30539,1.30546};
        double [] bidliq1 = {1000000,3000000,5000000};
        double [] offerliq1 = {1000000,3000000,5000000};

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteCArray(serverProvision, EUR_USD, LP22, fiProvision, 3, 3,startbid1, startoffer1, bidliq1, offerliq1);
        sleep(1);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====1 :aggregate quote in TC27_BPAfirstonesidedrateBidInverted ==="+aggregate.toString());

        //Lp1 rate is dropped, since it causes the book to be inverted
        //assert for bid price
        Assert.assertEquals(1.30465, aggregate.getBidPrices()[0], 0.00001);
        Assert.assertEquals(1000000,aggregate.getBidQtys()[0],0.00001);
        Assert.assertEquals(1.30524,aggregate.getOfferPrices()[0],0.00001);
        Assert.assertEquals(1000000,aggregate.getOfferQtys()[0],0.00001);
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1, aggregate.getNumBids());
    }

    @Test
    public void TC28_BPAfirstonesidedrate3LPsBid () throws Exception {

        // Incase of MT quote from LP, single tier will be taken for aggregation
        System.out.println("===== START : TC28_BPAfirstonesidedrate3LPsBid =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);
        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);


        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();
        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 1, 1.30488, 1000000,true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote ==="+quote.toString());

        double [] startbid1 = {1.30465,1.3045,1.30442};
        double [] startoffer1 = {1.30524,1.30539,1.30546};
        double [] bidliq1 = {1000000,3000000,5000000};
        double [] offerliq1 = {1000000,3000000,5000000};

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteCArray(serverProvision, EUR_USD, LP22, fiProvision, 3, 3,startbid1, startoffer1, bidliq1, offerliq1);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.30487, 1.30526, 1000000, 1000000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====1 :aggregate quote in TC28_BPAfirstonesidedrate3LPsBid ==="+aggregate.toString());

        //assert for bid price
        Assert.assertEquals(1.30488, aggregate.getBidPrices()[0], 0.00001);
        Assert.assertEquals(1000000,aggregate.getBidQtys()[0],0.00001);
        Assert.assertEquals(1.30524,aggregate.getOfferPrices()[0],0.00001);
        Assert.assertEquals(1000000,aggregate.getOfferQtys()[0],0.00001);
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1, aggregate.getNumBids());
    }

    @Test
    public void TC29_BPAfirstonesidedrate3LPsOffer () throws Exception {

        // Incase of MT quote from LP, single tier will be taken for aggregation
        System.out.println("===== START : TC29_BPAfirstonesidedrate3LPsOffer =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);
        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();
        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 1, 1.30521, 1000000,false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote ==="+quote.toString());

        double [] startbid1 = {1.30465,1.3045,1.30442};
        double [] startoffer1 = {1.30524,1.30539,1.30546};
        double [] bidliq1 = {1000000,3000000,5000000};
        double [] offerliq1 = {1000000,3000000,5000000};

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteCArray(serverProvision, EUR_USD, LP22, fiProvision, 3, 3,startbid1, startoffer1, bidliq1, offerliq1);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.30487, 1.30526, 1000000, 1000000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====1 :aggregate quote in TC29_BPAfirstonesidedrate3LPsOffer ==="+aggregate.toString());

        Assert.assertEquals(1.30487, aggregate.getBidPrices()[0], 0.00001);
        Assert.assertEquals(1000000,aggregate.getBidQtys()[0],0.00001);
        Assert.assertEquals(1.30521,aggregate.getOfferPrices()[0],0.00001);
        Assert.assertEquals(1000000,aggregate.getOfferQtys()[0],0.00001);
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1, aggregate.getNumBids());
    }

    //fails - The older quote is dropped, instead of aggregating both
    @Test
    public void TC30_BPAbothonesidedrateBidOffer () throws Exception {

        // Incase of MT quote from LP, single tier will be taken for aggregation
        System.out.println("===== START : TC30_BPAbothonesidedrateBidOffer =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);
        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();
        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.30487, 1000000,true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote ==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.30524, 1000000,false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote ==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====1 :aggregate quote in TC30_BPAbothonesidedrateBidOffer ==="+aggregate.toString());

        //assert for bid price
        Assert.assertEquals(1.30487, aggregate.getBidPrices()[0], 0.00001);
        Assert.assertEquals(1000000,aggregate.getBidQtys()[0],0.00001);
        Assert.assertEquals(1.30524,aggregate.getOfferPrices()[0],0.00001);
        Assert.assertEquals(1000000,aggregate.getOfferQtys()[0],0.00001);
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1, aggregate.getNumBids());
    }

    @Test
    public void TC31_BPAbothonesidedrate2Bid () throws Exception {

        // Incase of MT quote from LP, single tier will be taken for aggregation
        System.out.println("===== START : TC31_BPAbothonesidedrate2Bid =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);
        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();
        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.30487, 1000000,true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote ==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.30524, 1000000,true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote ==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====1 :aggregate quote in TC31_BPAbothonesidedrate2Bid ==="+aggregate.toString());

        //assert for bid price
        Assert.assertEquals(1.30524, aggregate.getBidPrices()[0], 0.00001);
        Assert.assertEquals(1000000,aggregate.getBidQtys()[0],0.00001);
        Assert.assertEquals(0.0,aggregate.getOfferPrices()[0],0.00001);
        Assert.assertEquals(0.0,aggregate.getOfferQtys()[0],0.00001);
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1, aggregate.getNumBids());
    }

    @Test
    public void TC32_BPAbothonesidedrate2BidfirstTOB () throws Exception {

        // Incase of MT quote from LP, single tier will be taken for aggregation
        System.out.println("===== START : TC32_BPAbothonesidedrate2BidfirstTOB =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);
        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();
        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.30524, 1000000,true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote ==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.30487, 1000000,true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote ==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====1 :aggregate quote in TC32_BPAbothonesidedrate2BidfirstTOB ==="+aggregate.toString());

        //assert for bid price
        Assert.assertEquals(1.30524, aggregate.getBidPrices()[0], 0.00001);
        Assert.assertEquals(1000000,aggregate.getBidQtys()[0],0.00001);
        Assert.assertEquals(0.0,aggregate.getOfferPrices()[0],0.00001);
        Assert.assertEquals(0.0,aggregate.getOfferQtys()[0],0.00001);
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1, aggregate.getNumBids());
    }

    @Test
    public void TC33_BPAfirstonesidedrateOfferliq0 () throws Exception {

        // Incase of MT quote from LP, single tier will be taken for aggregation
        System.out.println("===== START : TC33_BPAfirstonesidedrateOfferliq0 =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);
        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();
        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.30487, 0.0,false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote ==="+quote.toString());

        double [] startbid1 = {1.30465,1.3045,1.30442};
        double [] startoffer1 = {1.30524,1.30539,1.30546};
        double [] bidliq1 = {1000000,3000000,5000000};
        double [] offerliq1 = {1000000,3000000,5000000};

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteCArray(serverProvision, EUR_USD, LP22, fiProvision, 3, 3,startbid1, startoffer1, bidliq1, offerliq1);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====1 :aggregate quote in TC33_BPAfirstonesidedrateOfferliq0 ==="+aggregate.toString());

        Assert.assertEquals(1.30465, aggregate.getBidPrices()[0], 0.00001);
        Assert.assertEquals(1000000,aggregate.getBidQtys()[0],0.00001);
        Assert.assertEquals(1.30524,aggregate.getOfferPrices()[0],0.00001);
        Assert.assertEquals(1000000,aggregate.getOfferQtys()[0],0.00001);
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1, aggregate.getNumBids());
    }

    @Test
    public void TC34_BPAOneSidedRateLastToAggregateliq0() throws Exception {
        //Fails since num of offers value expected is 0, comes as 1
        //Full book same test case gives value as 0
        System.out.println("===== START : TC34_BPAOneSidedRateLastToAggregateliq0 =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24, 1000.00, 0.0);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in TC34_BPAOneSidedRateLastToAggregateliq0 ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.001);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(0.0, aggregate.getOfferQtys()[0],0.001);
        assertEquals(1, aggregate.getNumOffers()); //in case of BPA
        System.out.println("===== END : TC34_BPAOneSidedRateLastToAggregateliq0 =====");

    }

    //add scenario with 3LPs and one-sided rate in the middle
    @Test
    public void TC35_BPAonesidedrateMiddle3LPsBid () throws Exception {

        // Incase of MT quote from LP, single tier will be taken for aggregation
        System.out.println("===== START : TC35_BPAonesidedrateMiddle3LPsBid =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);
        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);


        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();
        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        double [] startbid1 = {1.30465,1.3045,1.30442};
        double [] startoffer1 = {1.30524,1.30539,1.30546};
        double [] bidliq1 = {1000000,3000000,5000000};
        double [] offerliq1 = {1000000,3000000,5000000};

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        QuoteC quote = getQuoteCArray(serverProvision, EUR_USD, LP22, fiProvision, 3, 3,startbid1, startoffer1, bidliq1, offerliq1);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 1, 1.30488, 1000000,true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote ==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.30487, 1.30526, 1000000, 1000000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====1 :aggregate quote  ==="+aggregate.toString());

        //assert for bid price
        Assert.assertEquals(1.30488, aggregate.getBidPrices()[0], 0.00001);
        Assert.assertEquals(1000000,aggregate.getBidQtys()[0],0.00001);
        Assert.assertEquals(1.30524,aggregate.getOfferPrices()[0],0.00001);
        Assert.assertEquals(1000000,aggregate.getOfferQtys()[0],0.00001);
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1, aggregate.getNumBids());
    }

    @Test
    public void TC36_BPAInactiveQuote () throws Exception {

        System.out.println("===== START : TC35_BPAInactiveQuote =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, 1.25);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 0, 0);
        manager.handleRate(quote);
        System.out.println("=====Inactive quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregate quote after inactive quote is sent from LP1 ==="+aggregate.toString());

        //assert only LP1 rates are removed, LP2 rates will get aggregated
        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 0, 0);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        //Now both the rates are removed and inactive quote is sent to mdf
        aggregate = book1.aggregate();
        System.out.println("===== aggregate quote after inactive quote is sent from LP2 als0 ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
    }

    @Test
    public void TC40_customAgg_withoutTags () throws Exception {
    // failing when all tags are not present. So null or 0 should be given
        // when providers are not given, both the providers are considered.
        // RequestedSize is considered only in FBA aggregation
        System.out.println("===== START : TC40_ProviderFilter =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        //RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null);
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, null, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
    }

    @Test
    public void TC41_customAgg_withReqSize () throws Exception {
// requested size is considered only in FBA.
        System.out.println("===== START : TC41_customAgg_withReqSize =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, Collections.singletonList(LP12), 2000, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
    }

    @Test
    public void TC42_customAgg_withMultipleProviders () throws Exception {

        System.out.println("===== START : TC42_customAgg_withMultipleProviders =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);
        //RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, Collections.singletonList(providers), 2000, false);
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, providers, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
    }

    @Test
    public void TC47_customAgg_withInvalidProvider () throws Exception {

        System.out.println("===== START : TC47_customAgg_withInvalidProvider =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add("abc");
        //RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, Collections.singletonList(providers), 2000, false);
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, providers, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
    }

    @Test
    public void TC48_customAgg_withOnluInvalidProvider () throws Exception {

        System.out.println("===== START : TC48_customAgg_withOnluInvalidProvider =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        List<String> providers = new ArrayList<String>();
        providers.add("abc");
        //RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, Collections.singletonList(providers), 2000, false);
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, providers, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
    }

    @Test
    public void TC43_customAgg_withTermCcy () throws Exception {

        System.out.println("===== START : TC43_customAgg_withTermCcy =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, null, 0, true);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1220, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1240, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
    }

    @Test
    public void TC44_customAgg_withInvalidReqSize () throws Exception {
    // ignores requestedSize in TOB
        System.out.println("===== START : TC44_customAgg_withInvalidReqSize =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, Collections.singletonList(LP12), -1, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
    }
    @Test
    public void TC45_customAgg_withMultipleMQProviders () throws Exception {

        System.out.println("===== START : TC45_customAgg_withMultipleMQProviders =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, null, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
    }

    @Test
    public void TC46_customAgg_withMultipleMTMQProviders () throws Exception {

        System.out.println("===== START : TC46_customAgg_withMultipleMTMQProviders =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, null, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 3, 1.23, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP3 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.23, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
    }

}
