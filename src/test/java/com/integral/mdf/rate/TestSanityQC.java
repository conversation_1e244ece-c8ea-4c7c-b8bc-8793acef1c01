package com.integral.mdf.rate;

import com.integral.mdf.Util;
import com.integral.mdf.data.*;
import com.integral.mdf.rate.provisioning.ProvisioningCalculator;
import com.integral.provision.LPProvision;
import com.integral.provision.MDFAggregationType;
import com.integral.mdf.data.FIProvision;
import com.integral.mdf.data.FIProvisionImpl;
import com.integral.mdf.data.ServerProvision;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;


public class TestSanityQC extends PriceAggregationTest {

    public static String EUR_USD = "EUR/USD";
    public static String USD_JPY = "USD/JPY";
    public static String LP12 = "lp1";
    public static String LP22 = "lp2";
    private static final int PRECISION = 4;
    private static final double PRICE = 1.12d;
    private ProvisioningCalculator calculator;
    // All currencies supported by its LP organizations cumulative
    public Map<Integer, Set<Integer>> lpSupportedCcyPairs =  new HashMap<Integer, Set<Integer>>();


    @Test
    public void TestQCPrecisionHigherMDF() throws Exception {
        //Higher precision quote 9 is lowered to 5 for EUR_USD, rounding off done by mdf
        //ME or Rex-ems sends the 9 precision rates in QuoteC and mdf does the precision rounding
        System.out.println("===== START : TestQCPrecisionHigherMDF =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        System.out.println("========================== FIProvision ======================");
        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);
        //impl.setLPSupportedCcyPairs(lpSupportedCcyPairs);

        System.out.println("====================RateDistribution Manager=================");
        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        //book1.switchStream();
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);
        for (RateBook rBook : manager.rateBooks.values()) {

            Assert.assertTrue(rBook.isActive());
        }

        System.out.println("===============GetQuoteCPrecision EUR_USD======================");
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1, 1.2457893823,  1.4457893825 );
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=========================LP1 precisioned quote===============" + quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("========Aggregated Quote=============" + aggregate.toString());

        Assert.assertEquals(1.2457, aggregate.getBidPrices()[0], 0.001);
        Assert.assertEquals(1.4458, aggregate.getOfferPrices()[0], 0.001);
        System.out.println("=================END: TestQCPrecisionHigherMDF=========================");


    }


    @Test
    public void TestQCPrecisionLowerMDF() throws Exception {
        //Lower precision rate sent by LP gets published as it is by MDF, no padding of zeros done - as expected
        System.out.println("===== START : TestQCPrecisionLowerMDF =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);
        for (RateBook rBook : manager.rateBooks.values()) {

            Assert.assertTrue(rBook.isActive());
        }

        System.out.println("===============GetQuoteCPrecision======================");
        //QuoteC quote = getQuoteCPrecision(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.2, 1.3, 100.0);
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.2, 1.3);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=========================LP1 precisioned quote===============" + quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("========Aggregated Quote=============" + aggregate.toString());

        Assert.assertEquals(1.2, aggregate.getBidPrices()[0], 0.001);
        Assert.assertEquals(1.3, aggregate.getOfferPrices()[0], 0.001);
        Assert.assertEquals(1.20, aggregate.getBidPrices()[0], 0);
        Assert.assertEquals(1.30, aggregate.getOfferPrices()[0], 0);
        System.out.println("=================END: TestQCPrecisionLowerMDF=========================");

    }


    @Test
    public void TestQCPrecisionTwo() throws Exception {
        //Uses rounding off function
        System.out.println("===== START : TestQCPrecisionTwo =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);
        for (RateBook rBook : manager.rateBooks.values()) {

            Assert.assertTrue(rBook.isActive());
        }

        System.out.println("===============GetQuoteCPrecision======================");
        QuoteC quote = getQuoteCPrecision(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21567, 1.24567, 100.0);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=========================LP1 precisioned quote===============" + quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("========Aggregated Quote=============" + aggregate.toString());

        Assert.assertEquals(1.22, aggregate.getBidPrices()[0], 0.01);
        Assert.assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
        System.out.println("=================END: TestQCPrecisionTwo========================");


/*
//    QuoteC quote = getHighPrecisionQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.214586, 1.244586);
//    QuoteC quote = getHighPrecisionQuoteC(serverProvision,USD_JPY,LP22,fiProvision)    ;
//    quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
//    manager.handleRate(quote);
//    System.out.println("=====LP1 quote==="+quote.toString());*/

/*
//    Integer cpIndex = getCPIdx(fiProvision);
//    LPProvision lp = getLP(fiProvision);
//
//    System.out.println("\n=====================Calculator==========================");
//    calculator = new ProvisioningCalculator("Test", "Test", lp, fiProvision, Util.getBaseCurrencyIndex(cpIndex),
//            Util.getVarCurrencyIndex(cpIndex), rawRatePool, PRECISION, -1, true);
   // ProvisioningCalculator("  ")

//    quote = getQuoteC(serverProvision, USD_JPY, LP22, fiProvision, 1, 1.214586, 1.244586);
//    quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
//    manager.handleRate(quote);
//    System.out.println("=====LP2 quote==="+quote.toString());


}*/

    }


    @Test
    public void TestQCPrecisionThree() throws Exception {
        //Uses rounding off function
        System.out.println("===== START : TestQCPrecisionThree =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);
        for (RateBook rBook : manager.rateBooks.values()) {

            Assert.assertTrue(rBook.isActive());
        }

        System.out.println("===============GetQuoteCPrecision======================");
        QuoteC quote = getQuoteCPrecision(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21567, 1.24567, 1000.0);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=========================LP1 precisioned quote===============" + quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("========Aggregated Quote=============" + aggregate.toString());

        Assert.assertEquals(1.216, aggregate.getBidPrices()[0], 0.001);
        Assert.assertEquals(1.246, aggregate.getOfferPrices()[0], 0.001);
        System.out.println("=================END: TestQCPrecisionThree=========================");

    }

    //Add more Test Cases after defining precisions for other ccy pairs - USD_JPY


}
