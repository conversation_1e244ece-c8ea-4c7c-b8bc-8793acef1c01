package com.integral.mdf.rate;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map.Entry;
import java.util.Set;

import com.integral.mdf.data.*;
import com.integral.mdf.rate.TestPriceAggregation.TestPriceBookSink;

import org.eclipse.jetty.util.log.Log;
import org.junit.Assert;
import org.junit.Test;

import com.google.gson.Gson;
import com.integral.mdf.DefaultProvisioningTest;
import com.integral.mdf.PriceBookSink;
import com.integral.mdf.Util;
import com.integral.model.OracleEntity;
import com.integral.notifications.StreamUpdate;
import com.integral.provision.LPProvision;
import com.integral.provision.MDFAggregationType;

public class TestRBA extends PriceAggregationTest { //DefaultProvisioningTest {

    private static final String LP22 = "lp2";
    private static final String LP12 = "lp1";
    private static final String EUR_USD = "EUR/USD";

    @Test
    public void testRBAFromSQLPs () throws Exception {

        System.out.println("===== START : testRBAFromSQLPs =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testRBAFromSQLPs ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());
        System.out.println("===== END : testRBAFromSQLPs =====");

    }


    @Test
    public void testRBAFromSQLPCausingInvertedTiers () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote

        System.out.println("===== START : testRBAFromSQLPCausingInvertedTiers =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        sleep(1);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.19, 1.20);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testRBAFromSQLPCausingInvertedTiers ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.19, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : testRBAFromSQLPCausingInvertedTiers =====");

    }


    @Test
    public void testRBAFromSQLpGivingInvertedTiers () throws Exception {
        // Quote causing inverted quote should be dropped,
        //noOfTiers is 1 since MDF has to send quote with 0 rate and 1 tier size to represent inactive quote to MDG,
        //otherwise MDG will not be able to recognize inactive quote

        System.out.println("===== START : testRBAFromSQLpGivingInvertedTiers =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.24, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testRBAFromSQLpGivingInvertedTiers ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : testRBAFromSQLpGivingInvertedTiers =====");

    }


    @Test
    public void testRBAFromSQLpsGivingOneSidedBidRate () throws Exception {

        System.out.println("===== START : testRBAFromSQLpsGivingOneSidedBidRate =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testRBAFromSQLpsGivingOneSidedBidRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.001);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        //assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);

        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);
        //The book is sorted at the offer side as well
        assertEquals(0, aggregate.getOfferQtys()[0],0.001);
        assertEquals(0, aggregate.getNumOffers());
        System.out.println("===== END : testRBAFromSQLpsGivingOneSidedBidRate =====");

    }


    @Test
    public void testRBAFromSQLpsGivingOneSidedOfferRate () throws Exception {

        System.out.println("===== START : testRBAFromSQLpsGivingOneSidedOfferRate =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //public QuoteC getOneSidedQuoteC(ServerProvision serverProvision, String ccyp,
        //        String lpName, FIProvision fi, int noOfTiers, double startPrice, boolean isBid) {


        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testRBAFromSQLpsGivingOneSidedOfferRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(0.00, aggregate.getBidQtys()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1.22, aggregate.getOfferPrices()[1], 0.01);

        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0],0.001);
        assertEquals(1000.00, aggregate.getOfferQtys()[1],0.001);
        assertEquals(2, aggregate.getNumOffers());
        System.out.println("===== END : testRBAFromSQLpsGivingOneSidedOfferRate =====");

    }


    @Test
    public void testRBAFromSQLpsGivingOneSidedBidOfferRate () throws Exception {

        System.out.println("===== START : testRBAFromSQLpsGivingOneSidedBidOfferRate =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testRBAFromSQLpsGivingOneSidedBidOfferRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000, aggregate.getBidQtys()[0], 0.001);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.001);

        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0],0.001);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : testRBAFromSQLpsGivingOneSidedBidOfferRate =====");

    }


    @Test
    public void testRBAFromMQLPs () throws Exception {


        System.out.println("===== START : testRBAFromMQLPs =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 5, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 7, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testRBAFromSQLPs ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[2], 0.001);
        assertEquals(1.2, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[4], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[5], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[6], 0.001);
        assertEquals(1.18, aggregate.getBidPrices()[7], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[8], 0.001);
        assertEquals(1.17, aggregate.getBidPrices()[9], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[6], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[7], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[8], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[9], 0.01);
        assertEquals(10, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[5], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[6], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[7], 0.01);
        assertEquals(1.28, aggregate.getOfferPrices()[8], 0.01);
        assertEquals(1.28, aggregate.getOfferPrices()[9], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[6], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[7], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[8], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[9], 0.01);
        assertEquals(10, aggregate.getNumOffers());
        System.out.println("===== END : testRBAFromSQLPs =====");

    }


    @Test
    public void testRBAFromMQLPCausingInvertedTiers () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote

        System.out.println("===== START : testRBAFromMQLPCausingInvertedTiers =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        sleep(1);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.19, 1.20);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testRBAFromMQLPCausingInvertedTiers ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.19, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.18, aggregate.getBidPrices()[1], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.21, aggregate.getOfferPrices()[1], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());
        System.out.println("===== END : testRBAFromMQLPCausingInvertedTiers =====");

    }

    @Test
    public void testRBAFromMQLpGivingInvertedTiers () throws Exception {
        // Quote causing inverted quote should be dropped,
        //noOfTiers is 1 since MDF has to send quote with 0 rate and 1 tier size to represent inactive quote to MDG,
        //otherwise MDG will not be able to recognize inactive quote

        System.out.println("===== START : testRBAFromMQLpGivingInvertedTiers =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.24, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testRBAFromMQLpGivingInvertedTiers ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : testRBAFromMQLpGivingInvertedTiers =====");

    }


    @Test
    public void testRBAFromMQLpsGivingOneSidedBidRate () throws Exception {


        System.out.println("===== START : testRBAFromMQLpsGivingOneSidedBidRate =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 4, 1.22, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testRBAFromMQLpsGivingOneSidedBidRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[2], 0.001);
        assertEquals(1.20, aggregate.getBidPrices()[3], 0.001);
        assertEquals(1.20, aggregate.getBidPrices()[4], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[5], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[6], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[3], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[4], 0.001);
        assertEquals(4000.00, aggregate.getBidQtys()[5], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[6], 0.001);
        assertEquals(7, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        //assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);

        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);
        //The book is sorted at the offer side as well
        assertEquals(0, aggregate.getOfferQtys()[0],0.001);
        assertEquals(0, aggregate.getNumOffers());
        System.out.println("===== END : testRBAFromMQLpsGivingOneSidedBidRate =====");

    }


    @Test
    public void testRBAFromMQLpsGivingOneSidedOfferRate () throws Exception {


        System.out.println("===== START : testRBAFromMQLpsGivingOneSidedOfferRate =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }



        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testRBAFromMQLpsGivingOneSidedOfferRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(0.00, aggregate.getBidQtys()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1.22, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.22, aggregate.getOfferPrices()[2], 0.001);
        assertEquals(1.23, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[4], 0.001);

        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0],0.001);
        assertEquals(2000.00, aggregate.getOfferQtys()[1],0.001);
        assertEquals(1000.00, aggregate.getOfferQtys()[2],0.001);
        assertEquals(2000.00, aggregate.getOfferQtys()[3],0.001);
        assertEquals(3000.00, aggregate.getOfferQtys()[4],0.001);
        assertEquals(5, aggregate.getNumOffers());

        System.out.println("===== END : testRBAFromMQLpsGivingOneSidedOfferRate =====");

    }


    @Test
    public void testRBAFromMTLpsGivingOneSidedBidOfferRate () throws Exception {


        System.out.println("===== START : testRBAFromMTLpsGivingOneSidedBidOfferRate =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.22, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testRBAFromMTLpsGivingOneSidedBidOfferRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000, aggregate.getBidQtys()[0], 0.001);
        assertEquals(2000, aggregate.getBidQtys()[1], 0.001);
        assertEquals(3000, aggregate.getBidQtys()[2], 0.001);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1.23, aggregate.getOfferPrices()[1], 0.001);

        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0],0.001);
        assertEquals(2000.00, aggregate.getOfferQtys()[1],0.001);
        assertEquals(2, aggregate.getNumOffers());
        System.out.println("===== END : testRBAFromMTLpsGivingOneSidedBidOfferRate =====");

    }


    @Test
    public void testRBAFromImbalancedTiers() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 4, 2, 1.21, 1.24);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 4, 2, 1.21, 1.24);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();

        System.out.println("=====aggregate quote in testRBAFromImbalancedTiers ==="+aggregate.toString());


        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[5], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[6], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[7], 0.01);
        assertEquals(0.0, aggregate.getBidPrices()[8], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[6], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[7], 0.01);
        assertEquals(8, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[3], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());
    }


    @Test
    public void testRBAFromMTLPs () throws Exception {

        // Incase of MT quote from LP, single tier will be taken for aggregation

        System.out.println("===== START : testRBAFromMTLPs =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 5, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 7, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testRBAFromSQLPs ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());
        System.out.println("===== END : testRBAFromSQLPs =====");

    }

    @Test
    public void testRBAFromMTLPsCausingInvertedTiers () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote

        System.out.println("===== START : testRBAFromMTLPsCausingInvertedTiers =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.19, 1.20);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testRBAFromMTLPsCausingInvertedTiers ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.19, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : testRBAFromMTLPsCausingInvertedTiers =====");

    }


    @Test
    public void testRBAFromMTLpsGivingOneSidedBidRate () throws Exception {


        System.out.println("===== START : testRBAFromMTLpsGivingOneSidedBidRate =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 4, 1.22, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testRBAFromMTLpsGivingOneSidedBidRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.001);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.001);
        //assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);

        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);
        //The book is sorted at the offer side as well
        assertEquals(0, aggregate.getOfferQtys()[0],0.001);
        assertEquals(0, aggregate.getNumOffers());
        System.out.println("===== END : testRBAFromMTLpsGivingOneSidedBidRate =====");

    }


    @Test
    public void testRBAFromMTLpsGivingOneSidedOfferRate () throws Exception {


        System.out.println("===== START : testRBAFromMTLpsGivingOneSidedOfferRate =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }



        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testRBAFromMQLpsGivingOneSidedOfferRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(0.00, aggregate.getBidQtys()[0], 0.001);
        assertEquals(0, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1.22, aggregate.getOfferPrices()[1], 0.01);

        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0],0.001);
        assertEquals(1000.00, aggregate.getOfferQtys()[1],0.001);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testRBAFromMQLpsGivingOneSidedOfferRate =====");

    }


    @Test
    public void testRBAFromMQLpsGivingOneSidedBidOfferRate () throws Exception {


        System.out.println("===== START : testRBAFromSQLpsGivingOneSidedBidOfferRate =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.22, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testRBAFromMQLpsGivingOneSidedBidOfferRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000, aggregate.getBidQtys()[0], 0.001);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.001);

        System.out.println("=====aggregate.getOfferQtys()[0]=="+aggregate.getOfferQtys()[0]);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0],0.001);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : testRBAFromMQLpsGivingOneSidedBidOfferRate =====");

    }

    @Test
    public void testRBAFromMTandMQLP() throws Exception {
        System.out.println("===== START : testRBAFromMTandMQLP =====");
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 5, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 7, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("===== aggregate quote in testRBAFromMTLpsGivingOneSidedOfferRate ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[2], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[3], 0.001);
        assertEquals(1.18, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[5], 0.001);
        assertEquals(1.16, aggregate.getBidPrices()[6], 0.01);
        assertEquals(1.15, aggregate.getBidPrices()[7], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6000.00, aggregate.getBidQtys()[6], 0.01);
        assertEquals(7000.00, aggregate.getBidQtys()[7], 0.01);
        assertEquals(8, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(1.28, aggregate.getOfferPrices()[5], 0.01);
        assertEquals(1.29, aggregate.getOfferPrices()[6], 0.01);
        assertEquals(1.30, aggregate.getOfferPrices()[7], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[6], 0.01);
        assertEquals(7000.00, aggregate.getOfferQtys()[7], 0.01);
        assertEquals(8, aggregate.getNumOffers());

        System.out.println("===== END : testRBAFromMTLpsGivingOneSidedOfferRate =====");

    }


    @Test
    public void MEzeroliquidityFixed() throws Exception {
        //As of now,.. 0 liquidity sent from ME comes as 0 only from MDF as well
        //Change will be made to drop this quote with 0 liquidity,. then the test case will fail
        System.out.println("==============START:MEzeroliquidityBase==================== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.22,1.24,1000,1000);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3,1.21d, 1.24d, 1000d, 1000d);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[5], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[5], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[5], 0.01);

        assertEquals(6, aggregate.getNumOffers());

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 3,1.22,1.24,0,1000);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregate second quote in MEzeroliquidityBase ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[4], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(5, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[5], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumOffers());
    }


    @Test
    public void MEzeroliquidity_TierTwoRBA() throws Exception {
        //As of now,.. 0 liquidity sent from ME comes as 0 only from MDF as well
        //Change will be made to drop this quote with 0 liquidity,. then the test case will fail
        System.out.println("==============START:MEzeroliquidity_TierTwoRBA==================== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.23,1.34,1000,1000);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3,1.21d, 1.36d, 1000d, 1000d);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.23, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.22, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[4], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(5, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.34, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.35, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.36, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.37, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.38, aggregate.getOfferPrices()[4], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(5, aggregate.getNumOffers());

        double [] startbid1 = {1.21,1.20,1.19};
        double [] startoffer1 = {1.36,1.37,1.38};
        double [] bidliq1 = {1000,0.0,2000,3000,4000};
        double [] offerliq1 = {1000,0.0,2000,3000,4000};

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteCArray(serverProvision, EUR_USD, LP22, fiProvision, 3, 3,startbid1,startoffer1,bidliq1,offerliq1);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregate second quote in MEzeroliquidity_TierTwoRBA ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.23, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.22, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[3], 0.01);


        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.34, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.35, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.36, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.38, aggregate.getOfferPrices()[3], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());
        assertEquals(2, aggregate.getBookId());

    }


    @Test
    public void MEzeroliquidity_TierThreeRBA() throws Exception {
        //As of now,.. 0 liquidity sent from ME comes as 0 only from MDF as well
        //Change will be made to drop this quote with 0 liquidity,. then the test case will fail
        System.out.println("==============START:MEzeroliquidity_TierThreeRBA==================== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.22,1.34,1000,1000);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3,1.21d, 1.33d, 1000d, 1000d);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[5], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.33, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.34, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.34, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.35, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.35, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(1.36, aggregate.getOfferPrices()[5], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumOffers());

        double [] startbid = {1.21,1.20,1.19};
        double [] startoffer = {1.33,1.34,1.35};
        double [] bidliq = {1000,2000,0.0};
        double [] offerliq = {1000,2000,0.0};

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteCArray(serverProvision, EUR_USD, LP12, fiProvision, 3, 3,startbid,startoffer,bidliq,offerliq);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregate second quote in MEzeroliquidity_TierThreeRBA ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[4], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(5, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.33, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.33, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.34, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.34, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.35, aggregate.getOfferPrices()[4], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(5, aggregate.getNumOffers());
        assertEquals(2, aggregate.getBookId());

    }


    @Test //PLT-3271 fixed
    public void testRBAFromMQLPs_Samebidoffer () throws Exception {

        System.out.println("===== START : testRBAFromMQLPs_Samebidoffer =====");
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();
        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 5, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 7, 1.19, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testRBAFromMQLPs_Samebidoffer ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[4], 0.001);
        assertEquals(1.18, aggregate.getBidPrices()[5], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[6], 0.001);
        assertEquals(1.17, aggregate.getBidPrices()[7], 0.01);
        assertEquals(1.16, aggregate.getBidPrices()[8], 0.001);
        assertEquals(1.15, aggregate.getBidPrices()[9], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[6], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[7], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[8], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[9], 0.01);
        assertEquals(10, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.22, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.23, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[5], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[6], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[7], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[8], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[9], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[6], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[7], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[8], 0.01);
        assertEquals(7000.00, aggregate.getOfferQtys()[9], 0.01);
        assertEquals(10, aggregate.getNumOffers());
        System.out.println("===== END : testRBAFromMQLPs_Samebidoffer =====");

    }

    @Test //PLT-3271 fixed
    public void testRBAFromSQLPs_samebidoffer_2quote () throws Exception {

        System.out.println("===== START : testRBAFromSQLPs_samebidoffer_2quote =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();
        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.19, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testRBAFromSQLPs_samebidoffer_2quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[1], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        //Test agg after second quote
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.19, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("=====second aggregate quote in testRBAFromSQLPs_samebidoffer_2quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[1], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testRBAFromSQLPs_samebidoffer_2quote =====");

    }


    @Test
    public void TC1_customAgg_multipleMQProviders () throws Exception {

        System.out.println("===== START : TC1_customAgg_multipleMQProviders =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, providers, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[2], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[3], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[4], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[5], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[5], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumOffers());
    }

    @Test
    public void TC1_customAgg_multipleMTProviders () throws Exception {
    // by default, it will be first tier in a MT provider
        System.out.println("===== START : TC1_customAgg_multipleMTProviders =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, providers, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());
    }

    @Test
    public void TC1_customAgg_multipleProviders () throws Exception {

        System.out.println("===== START : TC1_customAgg_multipleProviders =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, providers, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[2], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[3], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());
    }

    @Test
    public void TC1_customAgg_selectedProvider () throws Exception {

        System.out.println("===== START : TC1_customAgg_selectedProvider =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        //providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, providers, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
    }

    @Test
    public void TC1_customAgg_selectedProvider_1 () throws Exception {

        System.out.println("===== START : TC1_customAgg_selectedProvider_1 =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        List<String> providers = new ArrayList<String>();
        //providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, providers, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
    }

    @Test
    public void TC1_customAgg_invalidProvider () throws Exception {
        // if invalid provider is the only provider, then it is considered as null, all providers are considered
        // if invalid provider is part of other providers, it is ignored and other providers are considered in aggregation
        System.out.println("===== START : TC1_customAgg_invalidProvider =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        List<String> providers = new ArrayList<String>();
        //providers.add(LP12);
        providers.add("abc");

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, providers, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[2], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[3], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());
    }

    @Test
    public void TC1_customAgg_invalidProvider_1 () throws Exception {
        // if invalid provider is the only provider, then it is considered as null, all providers are considered
        // if invalid provider is part of other providers, it is ignored and other providers are considered in aggregation
        System.out.println("===== START : TC1_customAgg_invalidProvider =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add("abc");

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, providers, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
    }

    @Test
    public void TC2_customAgg_multipleMTProviders_requestedSize () throws Exception {
        // by default, it will be first tier in a MT provider
        // requestedSize is ignored
        System.out.println("===== START : TC2_customAgg_multipleMTProviders_requestedSize =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, providers, 2000, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());
    }

    @Test
    public void TC2_customAgg_multipleMQProviders_requestedSize () throws Exception {

        System.out.println("===== START : TC2_customAgg_multipleMQProviders_requestedSize =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, providers, 3000, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[2], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[3], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[4], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[5], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[5], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumOffers());
    }

    @Test
    public void TC3_customAgg_termCcy () throws Exception {

        System.out.println("===== START : TC8_customAgg_termCcy =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, null, providers, 0, true);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1220.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2420.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[2], 0.001);
        assertEquals(1210.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[3], 0.001);
        assertEquals(3600.00, aggregate.getBidQtys()[3], 0.01);

        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1240.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1250.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(2520.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(3810.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());
    }

    @Test
    public void TC11_customAgg_multipleSubscriptions () throws Exception {

        System.out.println("===== START : TC11_customAgg_multipleSubscriptions =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("12", fiProvision, cpIndex, null, providers, 2000, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 8, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 8, 1.22, 1.23);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());


        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[2], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[3], 0.001);
        assertEquals(3000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[4], 0.001);
        assertEquals(4000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[5], 0.001);
        assertEquals(5000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[6], 0.001);
        assertEquals(6000.00, aggregate.getBidQtys()[6], 0.01);
        assertEquals(1.16, aggregate.getBidPrices()[7], 0.001);
        assertEquals(7000.00, aggregate.getBidQtys()[7], 0.01);
        assertEquals(1.15, aggregate.getBidPrices()[8], 0.001);
        assertEquals(8000.00, aggregate.getBidQtys()[8], 0.01);
        assertEquals(9, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.23, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[3], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[4], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[5], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(1.28, aggregate.getOfferPrices()[6], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[6], 0.01);
        assertEquals(1.29, aggregate.getOfferPrices()[7], 0.01);
        assertEquals(7000.00, aggregate.getOfferQtys()[7], 0.01);
        assertEquals(1.30, aggregate.getOfferPrices()[8], 0.01);
        assertEquals(8000.00, aggregate.getOfferQtys()[8], 0.01);
        assertEquals(9, aggregate.getNumOffers());

        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.27, 1.29);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.28, 1.29);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        aggregate = rateBook.aggregate();
        System.out.println("=====Second aggregated quote ==="+aggregate.toString());

        assertEquals(1.28, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.27, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.27, aggregate.getBidPrices()[2], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.29, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.29, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.3, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());

    }

}