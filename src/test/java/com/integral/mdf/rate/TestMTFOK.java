package com.integral.mdf.rate;

import com.integral.mdf.data.*;
import com.integral.provision.MDFAggregationType;
import org.junit.Assert;
import org.junit.Test;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;

public class TestMTFOK extends PriceAggregationTest {


    public static final String LP22 = "lp2";
    public static final String LP12 = "lp1";
    public static final String LP32 = "lp3";
    public static final String EUR_USD = "EUR/USD";

    @Test
    public void testMTFOKFromSQLPs () throws Exception {
    // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFOKFromSQLPs =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFOKFromSQLPs ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : testMTFOKFromSQLPs =====");
    }

    @Test
    public void testMTFOKAggregationFromMQLPs() throws Exception {
        System.out.println("===== START : testMTFOKAggregationFromMQLPs =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);

        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        System.out.println("=====LP2 quote==="+quote.toString());

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("=====aggregated quote==="+ aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        System.out.println("===== END : testMTFOKAggregationFromMQLPs =====");
    }


   @Test
    public void testMTFOKAggregationHigherTierLimit() throws Exception {
        // Configured tiers are higher than the LP limits, book will not be created
       System.out.println("===== START : testMTFOKAggregationHigherTierLimit =====");

       ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{10000,20000,30000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        System.out.println("=====LP1 quote==="+quote.toString());


        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        System.out.println("=====LP2 quote==="+quote.toString());

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====aggregated quote==="+aggregate.toString());

        //assert For bid price
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0, aggregate.getNumOffers());

       System.out.println("===== END : testMTFOKAggregationHigherTierLimit =====");
    }


    @Test
    public void testMTFOKAggregationHigherQuoteLimit() throws Exception {
        // When LP quote (1st tier liquidity) is higher than the configured tiers, single tier with complete configured liquidity is shown
        System.out.println("===== START : testMTFOKAggregationHigherQuoteLimit =====");
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{100,200,300});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);

        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("aggregated quote = " + aggregate.toString());

        //assert For bid price
        assertEquals(1, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(300.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);

        System.out.println("===== END : testMTFOKAggregationHigherQuoteLimit =====");
    }

   @Test
    public void testMTFOKAggregationOneSidedBidMultiQuote() throws Exception {

       System.out.println("===== START : testMTFOKAggregationOneSidedBidMultiQuote =====");

       ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{100,200,300});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.21d,1000d,true);

        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.21d,1000d,true);

        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("book="+aggregate.toString());

        //assert For bid price
        assertEquals(1, aggregate.getNumBids());
        assertEquals(0, aggregate.getNumOffers());
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(300.00, aggregate.getBidQtys()[0], 0.01);

       System.out.println("===== END : testMTFOKAggregationOneSidedBidMultiQuote =====");
    }

//    @Test
    public void testMTFOKAggregationOneSidedBidMultiTier() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{100,200,300});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.21d,1000d,true);
        quote.setQuoteType(QuoteC.MULTI_TIER);

        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.21d,1000d,true);
        quote.setQuoteType(QuoteC.MULTI_TIER);


        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("book="+aggregate.toString());

        //assert For bid price
        assertEquals(1, aggregate.getNumBids());
        assertEquals(0, aggregate.getNumOffers());
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(300.00, aggregate.getBidQtys()[0], 0.01);
        //   assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        //   assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
    }

   @Test
    public void testMTFOKAggregationOneSidedOfferMultiQuote() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{100,200,300});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.24d,1000d,false);

        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.24d,1000d,false);

        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("book="+aggregate.toString());

        //assert For bid price
        assertEquals(0, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
        //   assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        //   assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
    }

    @Test
    public void testMTFOKAggregationOneSidedOfferMultiTier() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{100,200,300});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.24d,1000d,false);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.24d,1000d,false);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("book="+aggregate.toString());

        //assert For bid price
        assertEquals(0, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
        //   assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        //   assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
    }


    @Test
    public void MTFOK_FirstTierZeroLiquidity() throws Exception {
        System.out.println("==============START:MTFOK_FirstTierZeroLiquidity==================== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationTiers(new double[]{1000,2000,3000,4000,5000});


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.22,1.24,1000,1000);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3,1.20d, 1.22d, 2000, 2000);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== first aggregate quote in MTFOK_FirstTierZeroLiquidity ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.23, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 3,1.22,1.24,0,1000);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregate second quote in MTFOK_FirstTierZeroLiquidity ==="+aggregate.toString());

        /* Uncomment based on the feedback from Peeyush
        //assertNotEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.23, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        assertEquals(2, aggregate.getBookId());

*/

        //assertNotEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.23, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        assertEquals(2, aggregate.getBookId());


        //LP1 send an updated quote with 0 liquidity on offer side, LP2 quote remains as it is
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 3,1.22,1.24,1000,0);
        manager.handleRate(quote);
        System.out.println("=====LP1 third quote with 0 liquidity on offer side==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregated third quote in MTFOK_FirstTierZeroLiquidity ==="+aggregate.toString());

        //assertNotEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertNotEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.23, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        assertEquals(3, aggregate.getBookId());

        System.out.println("==============END : MTFOK_FirstTierZeroLiquidity==================== ");

    }


    @Test
    public void MTFOK_MiddleTierZeroLiquidity() throws Exception {
        System.out.println("==============START:MTFOK_MiddleTierZeroLiquidity==================== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationTiers(new double[]{1000,2000,3000,4000,5000});


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.22,1.24,1000,1000);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3,1.20d, 1.22d, 2000, 2000);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== first aggregate quote in MTFOK_FirstTierZeroLiquidity ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        System.out.println("============== after bid===========");
        //assert For offer price
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.23, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());

        double [] startbid1 = {1.22,1.21,1.20};
        double [] startoffer1 = {1.34,1.35,1.36};
        double [] bidliq1 = {1000,0.0,3000};
        double [] offerliq1 = {1000,2000,3000};

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteCArray(serverProvision, EUR_USD, LP12, fiProvision, 3, 3,startbid1, startoffer1, bidliq1, offerliq1);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregate second quote in MTFOK_MiddleTierZeroLiquidity ==="+aggregate.toString());

        //assertNotEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.23, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        assertEquals(2, aggregate.getBookId());


        //LP1 send an updated quote with 0 liquidity on offer side, LP2 quote remains as it is
        double [] startbid2 = {1.22,1.21,1.20};
        double [] startoffer2 = {1.34,1.35,1.36};
        double [] bidliq2 = {1000,2000.0,3000};
        double [] offerliq2 = {1000,0,3000};

        quote = getQuoteCArray(serverProvision, EUR_USD, LP12, fiProvision, 3, 3,startbid2,startoffer2, bidliq2,offerliq2);
        manager.handleRate(quote);
        System.out.println("=====LP1 third quote with 0 liquidity on offer side==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregated third quote in MTFOK_MiddleTierZeroLiquidity ==="+aggregate.toString());

        //assertNotEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertNotEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.23, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        assertEquals(3, aggregate.getBookId());

        System.out.println("============== END : MTFOK_MiddleTierZeroLiquidity ==================== ");


    }


    @Test
    public void MTFOK_LastTierZeroLiquidity() throws Exception {
        //As of now,.. 0 liquidity sent from ME comes as 0 only from MDF as well
        //Change will be made to drop this quote with 0 liquidity,. then the test case will fail
        System.out.println("==============START:MTFOK_LastTierZeroLiquidity==================== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationTiers(new double[]{1000,2000,3000,4000});

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 5, 1.22,1.33,1000,1000);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 5,1.21d, 1.34d, 1000d, 1000d);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.33, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.34, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.35, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.36, aggregate.getOfferPrices()[3], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());

        double [] startbid = {1.22,1.21,1.20,1.19};
        double [] startoffer = {1.33,1.34,1.35,1.36};
        double [] bidliq = {1000,2000,3000.0,0.0};
        double [] offerliq = {1000,2000,3000.0,4000.0};

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteCArray(serverProvision, EUR_USD, LP12, fiProvision, 4, 4,startbid,startoffer,bidliq,offerliq);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregate second quote in MEzeroliquidity_TierTwo ==="+aggregate.toString());

        assertEquals(1.22, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
 //       assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.33, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.34, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.35, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.36, aggregate.getOfferPrices()[3], 0.01);

        //The book is sorted at the offer side as well
        assertNotEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.0, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.0, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(4000.0, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());
        assertEquals(2, aggregate.getBookId());


        //LP1 send an updated quote with 0 liquidity on offer side, LP2 quote remains as it is
        double [] startbid2 = {1.22,1.21,1.20,1.19};
        double [] startoffer2 = {1.33,1.34,1.35,1.36};
        double [] bidliq2 = {1000,2000,3000.0,4000.0};
        double [] offerliq2 = {1000,2000,3000.0,0};

        quote = getQuoteCArray(serverProvision, EUR_USD, LP12, fiProvision, 4, 4,startbid2,startoffer2, bidliq2,offerliq2);
        manager.handleRate(quote);
        System.out.println("=====LP1 third quote with 0 liquidity on offer side==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregated third quote in MTFOK_MiddleTierZeroLiquidity ==="+aggregate.toString());

        //assertNotEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertNotEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.33, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.34, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.35, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.37, aggregate.getOfferPrices()[3], 0.01);

        //The book is sorted at the offer side as well
        assertNotEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[3], 0.01);

        assertEquals(4, aggregate.getNumOffers());
        assertEquals(3, aggregate.getBookId());

        System.out.println("==============END : MTFOK_LastTierZeroLiquidity==================== ");

    }


    @Test
    public void testMTFOKFromSQLPs_samebidoffer () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testMTFOKFromSQLPs_samebidoffer =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.19, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testMTFOKFromSQLPs_samebidoffer ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        //Test agg after second quote
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.19, 1.23);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("=====second aggregate quote in testMTFOKFromSQLPs_samebidoffer ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.23, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testMTFOKFromSQLPs_samebidoffer =====");
    }


    @Test
    public void testMTFOKAggregationFromMQLPs_samebidoffer() throws Exception {
        System.out.println("===== START : testMTFOKAggregationFromMQLPs_samebidoffer =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,2,1.21,1.24,1000,1000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision,2,1.19,1.21,1000,1000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        System.out.println("=====LP2 quote==="+quote.toString());
        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();
        System.out.println("=====aggregated quote==="+ aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(0.0, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(0.0, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.22, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(0.0, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(0.0, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testMTFOKAggregationFromMQLPs_samebidoffer =====");
    }


    @Test
    public void testMTFOKAggregationFromMQLPs1() throws Exception {
        System.out.println("===== START : testMTFOKAggregationFromMQLPs1 =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1500,2500,3500});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);

        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        System.out.println("=====LP2 quote==="+quote.toString());

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("=====aggregated quote==="+ aggregate.toString());

        //assert For bid price
        assertEquals(1.20, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3500.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3500.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        System.out.println("===== END : testMTFOKAggregationFromMQLPs1 =====");
    }

    @Test
    public void testMTFOKAggregationRatesFromMQLPs2() throws Exception {
        System.out.println("===== START : testMTFOKAggregationRatesFromMQLPs2 =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1500,2500,3500});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

  //      QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 5, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);

        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);
        sleep(1);

  //      quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 5, 1.17, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        System.out.println("=====LP2 quote==="+quote.toString());

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("=====aggregated quote==="+ aggregate.toString());

        //assert For bid price
        assertEquals(1.20, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3500.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.22, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.23, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3500.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        System.out.println("===== END : testMTFOKAggregationRatesFromMQLPs2 =====");
    }

    @Test
    public void testMTFOKAggregationInvertedRatesFromMQLPs() throws Exception {
        System.out.println("===== START : testMTFOKAggregationInvertedRatesFromMQLPs =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //      QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 5, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);

        //      quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 5, 1.17, 1.20);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();

        System.out.println("=====aggregated quote==="+ aggregate.toString());

        //assert For bid price
        assertEquals(1.17, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.16, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.15, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.20, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.21, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.22, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        System.out.println("===== END : testMTFOKAggregationInvertedRatesFromMQLPs =====");
    }

    @Test
    public void testMTFOKAggregationInvertedRatesFromMQLPs1() throws Exception {
        System.out.println("===== START : testMTFOKAggregationInvertedRatesFromMQLPs1 =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //      QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 5, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);

        //      quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 5, 1.17, 1.20);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //      quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP2 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 5, 1.16, 1.18);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);

        System.out.println("=====LP3 quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();

        System.out.println("=====aggregated quote==="+ aggregate.toString());

        //assert For bid price
        assertEquals(1.17, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.16, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.15, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.18, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.19, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        System.out.println("===== END : testMTFOKAggregationInvertedRatesFromMQLPs1 =====");
    }

    @Test
    public void testMTFOKAggregationInvertedRatesFromMQLPs2() throws Exception {
        System.out.println("===== START : testMTFOKAggregationInvertedRatesFromMQLPs2 =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{2000,4000,5000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //      QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);

        //      quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 4, 1.17, 1.20);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //      quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP2 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 5, 1.16, 1.18);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);

        System.out.println("=====LP3 quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();

        System.out.println("=====aggregated quote==="+ aggregate.toString());

        //assert For bid price
        assertEquals(1.16, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.14, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.12, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.19, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.21, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.22, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        System.out.println("===== END : testMTFOKAggregationInvertedRatesFromMQLPs2 =====");
    }

    @Test
    public void testMTFOKAggregationInvertedRatesFromMQLPs3() throws Exception {
        System.out.println("===== START : testMTFOKAggregationInvertedRatesFromMQLPs3 =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1500,2500,3500});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //      QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 5, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);

        //      quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 5, 1.17, 1.20);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //      quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP2 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP32, fiProvision, 5, 1.16, 1.18);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);

        System.out.println("=====LP3 quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();

        System.out.println("=====aggregated quote==="+ aggregate.toString());

        //assert For bid price
        assertEquals(1.16, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.15, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.14, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3500.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.19, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.20, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.21, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3500.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        System.out.println("===== END : testMTFOKAggregationInvertedRatesFromMQLPs3 =====");
    }

    @Test
    public void testMTFOKProviderFilter() throws Exception {
        System.out.println("===== START : testMTFOKRequestedSize =====");

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1500,2500,3500});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);


        //RateDistributionManager manager = getNoHopManager(serverProvision);
        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        //manager.createRateBook(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.19, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.18, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        sleep(1);
        System.out.println("=====LP2 quote==="+quote.toString());


        PriceBook aggregate = book1.aggregate();

        System.out.println("=====aggregated quote==="+ aggregate.toString());

        //assert For bid price
        assertEquals(1.16, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.15, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.14, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3500.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.19, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.20, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.21, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3500.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
        System.out.println("===== END : testMTFOKAggregationInvertedRatesFromMQLPs3 =====");
    }

    @Test
    public void TC1_customAgg_MTproviders_requestedSize () throws Exception {
        // ideally requestedSize is ignored in MT provider for non FBA
// not working as expected for tier1 1500
        System.out.println("===== START : TC1_customAgg_MTproviders_requestedSize =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        //double [] tiers =  [1000,2000,5000];
        RateBook rateBook = manager.createRateBook("10001", fiProvision, cpIndex, new double[]{1500,2500,3500}, null, 2000, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.23, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        // actual output
        // bidPrices=[1.21, 1.2028, 0.0, ], bidQtys=[2500.0, 3500.0, 0.0],
        // offerPrices=[1.25, 1.2572, 0.0, ], offerQtys=[2500.0, 3500.0, 0.0]

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(2500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());


    }

    @Test
    public void TC2_customAgg_MTMQproviders_requestedSize () throws Exception {
// not working as expected
        System.out.println("===== START : TC2_customAgg_MTMQproviders_requestedSize =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1002", fiProvision, cpIndex,  new double[]{1500,2500,3500}, null, 2000, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

    }

    @Test
    public void TC3_customAgg_MQproviders_requestedSize () throws Exception {
        // requestSize is ignored incase of MQ providers
        System.out.println("===== START : TC3_customAgg_MQproviders_requestedSize =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500,2500,3500}, null, 2000, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());
    }

    @Test
    public void TC6_customAgg_multipleProviders () throws Exception {

        System.out.println("===== START : TC6_customAgg_multipleProviders =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500,2500,3500}, providers, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

    }

    @Test
    public void TC7_customAgg_selectedProviders () throws Exception {
// not working, provider tag is not getting considered
        System.out.println("===== START : TC7_customAgg_selectedProviders =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500,2500,3500}, providers, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.20, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

    }

    @Test
    public void TC8_customAgg_termCcy () throws Exception {

        System.out.println("===== START : TC8_customAgg_termCcy =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex,  new double[]{1500,2500,3500,4500}, providers, 0, true);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(3500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(2500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(3500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

    }

    @Test
    public void TC9_customAgg_termCcy_more_tiers () throws Exception {

        System.out.println("===== START : TC9_customAgg_termCcy_more_tiers =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex,  new double[]{2500,5000,7500,10000,15000,18000}, providers, 0, true);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(2500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(2500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
    }

    @Test
    public void TC10_customAgg_oneSidedBidRates () throws Exception {

        System.out.println("===== START : TC10_customAgg_oneSidedRates =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500,2500,3500}, providers, 0, false);

        //QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 0);
        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        //quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 0);
        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.00, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(0.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(0.00, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(0.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(0.00, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(0.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(0, aggregate.getNumOffers());

    }

    @Test
    public void TC11_customAgg_oneSidedOfferRates () throws Exception {
//failing, different from 2 sided op for the same input quotes
        System.out.println("===== START : TC11_customAgg_oneSidedOfferRates =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500,2500,3500}, providers, 0, false);

        //QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 0, 1.24);
        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.24, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.25, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0.00, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(0.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(0.00, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(0.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(0.00, aggregate.getBidPrices()[2], 0.0001);
        assertEquals(0.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(0, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.26, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

    }

    @Test
    public void TC12_customAgg_MQproviders_tiersHigherthanLiq () throws Exception {
        // requestSize is ignored incase of MQ providers
        System.out.println("===== START : TC12_customAgg_MQproviders_doubleAmts =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        //RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500.5,2500.5,3500.5,4500.5,5500.5, 6500.5}, null, 2000, false);
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500,2500,3500,4500,5500, 6500,7500,9500,13000}, null, 2000, false);
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());
    }

    @Test
    public void TC13_customAgg_MQproviders_doubleAmounts () throws Exception {
        // requestSize is ignored incase of MQ providers
        System.out.println("===== START : TC13_customAgg_MQproviders_doubleAmounts =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        //RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500.5,2500.5,3500.5,4500.5,5500.5, 6500.5}, null, 2000, false);
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500.5,2500.99,3000.99}, null, 2000, false);
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1500.5, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(2500.99, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(1500.5, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(2500.99, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());
    }

    @Test
    public void TC14_customAgg_MQproviders_termCcy_doubleAmounts () throws Exception {
        // requestSize is ignored incase of MQ providers
        System.out.println("===== START : TC14_customAgg_MQproviders_termCcy_doubleAmounts =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        //RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500.5,2500.5,3500.5,4500.5,5500.5, 6500.5}, null, 2000, false);
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500.5,2500.99,3500.99}, null, 2000, true);
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1500.5, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(3500.99, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(1500.5, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(3500.99, aggregate.getOfferQtys()[1], 0.01);
//        assertEquals(1.2566, aggregate.getOfferPrices()[2], 0.0001);
//        assertEquals(13000.99, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumOffers());
    }

}
