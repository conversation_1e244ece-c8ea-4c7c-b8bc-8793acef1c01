package com.integral.mdf.rate;

import static org.junit.Assert.assertEquals;

import org.junit.Test;

import com.integral.mdf.rate.sortedbook.EMSIterator;
import com.integral.mdf.rate.sortedbook.RangeSortedArray;
import com.integral.mdf.rate.treeset.AddTupleValueMergeHandler;
import com.integral.mdf.rate.treeset.AddValueMergeHandler;
import com.integral.mdf.rate.treeset.BidPriceComparator;
import com.integral.mdf.rate.treeset.MergeOnConflictTreeMap;
import com.integral.mdf.rate.treeset.OfferPriceComparator;
import com.integral.util.Tuple;

public class TestRangeSortedArrayBasedAggregation {
	
	
	@Test
	public void testRangeSortedArrayBasic(){
		RangeSortedArray<Tuple<Double,Double>> bids = new RangeSortedArray<Tuple<Double,Double>>(100);
		
		//long d = (long) (1.2121d * 100000000);
		
		bids.add(getKey(1.2120d), new Tuple<Double,Double>(1.2120d,2000d));
		System.out.println(bids);
		bids.add(getKey(1.2122d), new Tuple<Double,Double>(1.2122d,2000d));
		System.out.println(bids);
		bids.add(getKey(1.2121d), new Tuple<Double,Double>(1.2121d,3000d));
		System.out.println(bids);
		bids.add(getKey(1.2121d), new Tuple<Double,Double>(1.2121d,5000d));
		System.out.println(bids);
		
		EMSIterator<Tuple<Double,Double>> values = bids.createNewBestPriceIterator(0);
		Tuple<Double,Double> d = values.getNext();
		while(d!=null){
			System.out.println(d.first + "   " + d.second);
			d = values.getNext();
		}
		
		values = bids.createNewWorstPriceIterator(Integer.MAX_VALUE);
		d = values.getNext();
		while(d!=null){
			System.out.println(d.first + "   " + d.second);
			d = values.getNext();
		}
		
	}
	
	
	@Test
	public void testRangeSortedArrayBounds(){
		RangeSortedArray<Tuple<Double,Double>> bids = new RangeSortedArray<Tuple<Double,Double>>(100);
		
		//long d = (long) (1.2121d * 100000000);
		
		bids.add(getKey(2147.483647d), new Tuple<Double,Double>(2147.483647d,2000d));
		System.out.println(bids);
		bids.add(getKey(1.2122d), new Tuple<Double,Double>(1.2122d,2000d));
		System.out.println(bids);
		bids.add(getKey(1.2121d), new Tuple<Double,Double>(1.2121d,3000d));
		System.out.println(bids);
		bids.add(getKey(1.2121d), new Tuple<Double,Double>(1.2121d,5000d));
		System.out.println(bids);
		
		EMSIterator<Tuple<Double,Double>> values = bids.createNewBestPriceIterator(0);
		Tuple<Double,Double> d = values.getNext();
		while(d!=null){
			System.out.println(d.first + "   " + d.second);
			d = values.getNext();
		}
		
		values = bids.createNewWorstPriceIterator(Integer.MAX_VALUE);
		d = values.getNext();
		while(d!=null){
			System.out.println(d.first + "   " + d.second);
			d = values.getNext();
		}
		
		assertEquals(1, bids.getCount());
		
	}
	
	
	@Test
	public void testMergeOnConflict(){
		
		//Natural order prices
		RangeSortedArray<Tuple<Double,Double>> prices = new RangeSortedArray<Tuple<Double,Double>>(100,new AddTupleValueMergeHandler());
		
		prices.add(getKey(1.2121d), new Tuple<Double,Double>(1.2121d,3000d));
		prices.add(getKey(1.2120d), new Tuple<Double,Double>(1.2120d,2000d));
		prices.add(getKey(1.2122d), new Tuple<Double,Double>(1.2122d,3000d));
		prices.add(getKey(1.2121d), new Tuple<Double,Double>(1.2121d,5000d));
		
		assertEquals(3, prices.getCount());
	}
	
	
	@Test
	public void testSortAndMergeBidPrices(){
		
		//Natural order prices
		MergeOnConflictTreeMap<Double, Double> prices = new MergeOnConflictTreeMap<>(new AddValueMergeHandler());
		prices.put(1.2121d, 1000d);
		prices.put(1.2120d, 1000d);
		prices.put(1.2122d, 1000d);
		prices.put(1.2121d, 1000d);
		
		assertEquals(1000d,prices.get(1.2122d) ,1.0d);
		assertEquals(2000d,prices.get(1.2121d) ,1.0d);
		assertEquals(1000d,prices.get(1.2120d) ,1.0d);
		
		
		//highest bid to lowest
		prices = new MergeOnConflictTreeMap<>(new BidPriceComparator(),new AddValueMergeHandler());
		prices.put(1.2121d, 1000d);
		prices.put(1.2120d, 1000d);
		prices.put(1.2122d, 1000d);
		prices.put(1.2121d, 1000d);
		
		//test merge
		assertEquals(1000d,prices.get(1.2122d) ,1.0d);
		assertEquals(2000d,prices.get(1.2121d) ,1.0d);
		assertEquals(1000d,prices.get(1.2120d) ,1.0d);
		
		//test sorting 
		assertEquals(1.2122d,prices.pollFirstEntry().getKey(),0.0001d);
		assertEquals(1.2121d,prices.pollFirstEntry().getKey(),0.0001d);
		assertEquals(1.2120d,prices.pollFirstEntry().getKey(),0.0001d);
		

		//lowest offer to highest
		prices = new MergeOnConflictTreeMap<>(new OfferPriceComparator(),new AddValueMergeHandler());
		prices.put(1.2121d, 1000d);
		prices.put(1.2120d, 1000d);
		prices.put(1.2122d, 1000d);
		prices.put(1.2121d, 1000d);
		
		assertEquals(1000d,prices.get(1.2122d) ,1.0d);
		assertEquals(2000d,prices.get(1.2121d) ,1.0d);
		assertEquals(1000d,prices.get(1.2120d) ,1.0d);
	}
	
	
	@Test
	public void testSortAndMergeOfferPrices(){
		
		//Natural order prices
		MergeOnConflictTreeMap<Double, Double> prices = new MergeOnConflictTreeMap<>(new AddValueMergeHandler());
		prices.put(1.2121d, 1000d);
		prices.put(1.2120d, 1000d);
		prices.put(1.2122d, 1000d);
		prices.put(1.2121d, 1000d);
		
		assertEquals(1000d,prices.get(1.2122d) ,1.0d);
		assertEquals(2000d,prices.get(1.2121d) ,1.0d);
		assertEquals(1000d,prices.get(1.2120d) ,1.0d);

		//lowest offer to highest
		prices = new MergeOnConflictTreeMap<>(new OfferPriceComparator(),new AddValueMergeHandler());
		prices.put(1.2121d, 1000d);
		prices.put(1.2120d, 1000d);
		prices.put(1.2122d, 1000d);
		prices.put(1.2121d, 1000d);
		
		assertEquals(1000d,prices.get(1.2122d) ,1.0d);
		assertEquals(2000d,prices.get(1.2121d) ,1.0d);
		assertEquals(1000d,prices.get(1.2120d) ,1.0d);
		
		//test sorting 
		assertEquals(1.2120d,prices.pollFirstEntry().getKey(),0.0001d);
		assertEquals(1.2121d,prices.pollFirstEntry().getKey(),0.0001d);
		assertEquals(1.2122d,prices.pollFirstEntry().getKey(),0.0001d);

	}
	
	   /**
     * Returns the key to be used for the given rate.
     */
    protected int getKey(double rate)
    {
        if (rate < 0.0d) {
            return 0;
        }
        else if (Math.abs(rate - Double.MAX_VALUE) < 0.0001d) {
            return Integer.MAX_VALUE;
        }

        return (int) (Math.round(rate * multiplier) / 10);
    }

    int precision = 5;
    int multiplier = (int) Math.pow(10, precision + 1);
	
}
