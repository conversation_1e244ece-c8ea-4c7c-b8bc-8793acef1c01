package com.integral.mdf.rate;

import com.integral.log.LogFactory;
import com.integral.mdf.data.*;
import com.integral.notifications.StreamUpdate;
import com.integral.provision.LPProvision;
import com.integral.provision.MDFAggregationType;
import org.junit.Assert;
import org.junit.Test;
import com.integral.log.Log;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import com.integral.mdf.data.FIProvision;
import com.integral.mdf.data.FIProvisionImpl;
import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.mdf.data.ServerProvision;
import com.integral.provision.OrgProvision;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;

public class TestSwitchStream extends PriceAggregationTest{


    public static String EUR_USD = "EUR/USD";
    public static String LP12 = "lp1";
    public static String LP22 = "lp2";
    UnSafeBuffer buffer = new UnSafeBuffer();
    StreamUpdate streamUpdate = new StreamUpdate();
    private final Log log = LogFactory.getLog(this.getClass());
    //Long NondefaultLE = "NondefaultLE";
    private long defaultLEObjId = 9002;

    @Test
    public void TestSwitchStreamRBA() throws Exception {
        //Switch stream from One active stream to another active stream RBA
        System.out.println("=========START:TestSwitchStreamON=========================");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24 );
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();
        System.out.println("=================quote for aggregation======================"+aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[5], 0.01);
        //assertEquals(1.18, aggregate.getBidPrices()[6], 0.01);
        //assertEquals(1.18, aggregate.getBidPrices()[7], 0.01);
        //assertEquals(1.17, aggregate.getBidPrices()[8], 0.01);
        //assertEquals(1.17, aggregate.getBidPrices()[9], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(6, aggregate.getNumOffers());

        //1. switch stream
        manager.handleStreamSwitch(fiProvision, 1001, 2001, true, "TestStream2", LP12);
        System.out.println("==========stream switch done===============");

        //2. make quote from old stream is not aggregated
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);  //default that this func sends ----return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.21, 1.24);
        quote.setStreamIdx(1001);//send for old stream
        manager.handleRate(quote);

       aggregate = book1.aggregate();
        System.out.println("=====================Old stream quote after stream switch============================"+ aggregate.toString());
        //Only quote from LP22 is retained,,. LP12 quotes are removed from aggregation
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());
        assertEquals(3, aggregate.getNumOffers());

        //3. make sure quote from new stream is getting aggregated
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision); // default that this func sends ----return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.21, 1.24);
        quote.setStreamIdx(2001);//send for old stream ///shud be new stream
        manager.handleRate(quote);

        aggregate = book1.aggregate();
        System.out.println("================New stream quote after stream switch================================="+aggregate.toString());
        //aggregates the LP22 rate old quote(3) and the LP12 new quote stream(6 new quote sent above)
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(9, aggregate.getNumBids());
        assertEquals(9, aggregate.getNumOffers());

    }


    @Test
    public void TestSwitchStreamFBA() throws Exception {
        //Switch stream from One active stream to another active stream FBA
        System.out.println("=========START:TestSwitchStreamFBA=========================");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24 );
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();
        System.out.println("=================quote for aggregation======================"+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[3], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(1.27, aggregate.getOfferPrices()[3], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(5000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());

        //1. switch stream
        manager.handleStreamSwitch(fiProvision, 1001, 2001, true, "TestStream2", LP12);
        System.out.println("==========stream switch done===============");


        //2. make quote from old stream is not aggregated
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);  //default that this func sends ----return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.21, 1.24);
        quote.setStreamIdx(1001);//send for old stream
        manager.handleRate(quote);
        System.out.println("=====LP12 quote from old stream is not aggregated==="+quote.toString());


        aggregate = book1.aggregate();
        System.out.println("=====================Old stream quote after stream switch============================"+ aggregate.toString());
        //Only quote from LP22 is retained,,. LP12 quotes are removed from aggregation
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());
        assertEquals(3, aggregate.getNumOffers());

        //3. make sure quote from new stream is getting aggregated
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision); // default that this func sends ----return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.21, 1.24);
        quote.setStreamIdx(2001);//send for old stream ///shud be new stream
        manager.handleRate(quote);


        aggregate = book1.aggregate();
        System.out.println("================New stream quote after stream switch================================="+aggregate.toString());
        //aggregates the LP22 rate old quote(3) and the LP12 new quote stream(6 new quote sent above)
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(7, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());



    }


    @Test
    public void TestSwitchStreamActtoIna() throws Exception {
        //Switch stream from One active stream to another active stream FBA
        System.out.println("=========START:TestSwitchStreamActtoIna=========================");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24 );
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, 1.25);
        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();
        System.out.println("=================quote for aggregation======================"+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        //1. switch stream to inactive stream
        manager.handleStreamSwitch(fiProvision, 1001, 2001, false, "TestStream2", LP12);
        System.out.println("==========stream switch done===============");

        //2. make quote from old stream is not aggregated
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);  //default that this func sends ----return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.21, 1.24);
        quote.setStreamIdx(1001);//send for old stream
        manager.handleRate(quote);

        aggregate = book1.aggregate();
        System.out.println("=====================Old stream quote after stream switch============================"+ aggregate.toString());
        //Only quote from LP22 is retained,,. LP12 quotes are removed from aggregation
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());

        //3. make sure quote from new stream is getting aggregated
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.22, 1.26); // default that this func sends ----return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.21, 1.24);
        quote.setStreamIdx(2001);//send for old stream ///shud be new stream
        manager.handleRate(quote);

        aggregate = book1.aggregate();
        System.out.println("================New stream quote after stream switch should remain same as old one================================="+aggregate.toString());
        //aggregates the LP22 rate old quote(3) and the LP12 new quote stream with 1.26 is not considered, since new stream is inactive
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());



    }


    @Test
    public void TestSwitchStreamInatoAct() throws Exception {
        //Switch stream from One active stream to another active stream FBA
        System.out.println("=========START:TestSwitchStreamInatoAct=========================");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        LPProvision lp1Prov = null;
        for (LPProvision lpProv : fiProvision.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lp1Prov = lpProv;
                break;
            }
        }
        Assert.assertNotNull(lp1Prov);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        //1. disable stream
        manager.handleStreamStatusChange(1001, StreamUpdate.STREAM_INACTIVE, lp1Prov.getShortName());

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24 );
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, 1.25);
        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();
        System.out.println("=================quote for aggregation======================"+aggregate.toString());
        //only quote from LP22 is considered for aggregation
        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        //1. switch stream to active stream
        manager.handleStreamSwitch(fiProvision, 1001, 2001, true, "TestStream2", LP12);
        System.out.println("==========stream switch done===============");

        //2. make quote from old stream is not aggregated
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);  //default that this func sends ----return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.21, 1.24);
        quote.setStreamIdx(1001);//send for old stream
        manager.handleRate(quote);

        aggregate = book1.aggregate();
        System.out.println("=====================Old stream quote after stream switch============================"+ aggregate.toString());
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());

        //3. make sure quote from new stream is getting aggregated
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.22, 1.26); // default that this func sends ----return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.21, 1.24);
        quote.setStreamIdx(2001);//send for old stream ///shud be new stream
        manager.handleRate(quote);

        aggregate = book1.aggregate();
        System.out.println("================New stream quote after stream switch should remain same as old one================================="+aggregate.toString());
        //aggregates the LP22 rate old quote(3) and the LP12 new quote stream with 1.26 is not considered, since new stream is inactive
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.001);
        assertEquals(1, aggregate.getNumBids());
        assertEquals(2, aggregate.getNumOffers());



    }


    @Test
    public void TestSwitchStreamNondefaultLE() throws Exception {
        //Switch stream from One active stream to another active stream FBA
        System.out.println("=========START:TestSwitchStreamNondefaultLE=========================");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        LPProvision lp1Prov = null;
        for (LPProvision lpProv : fiProvision.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lp1Prov = lpProv;
                break;
            }
        }
        Assert.assertNotNull(lp1Prov);
        //System.out.println("======lp1Prov========" +lp1Prov);


        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        //System.out.println("======FIProvision========" +fiProvision);


        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.24 );
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, 1.25);
        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();
        System.out.println("=================quote for aggregation======================"+aggregate.toString());
        //only quote from LP22 is considered for aggregation
        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2, aggregate.getNumBids());
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);


        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        //setting a different defaultLE for fiProvision ob
        impl.setDefaultLEObjId(9002);  //second value from definedOrgLEIndexes in RDSBaseTest
        OrgProvision orgProv = ((FIProvisionImpl) fiProvision).getOrgProvision();
        orgProv.setDefaultLE("DefaultLE1");
        orgProv.setDefaultLEIndex(defaultLEObjId);
        ((FIProvisionImpl) fiProvision).setOrgProvision(orgProv);
        System.out.println("======OrgProvision========" +orgProv);

        //1. switch stream to active stream on non-defaultLE
        manager.handleStreamSwitch(fiProvision, 1001, 2001, true, "TestStream2", LP12);
        System.out.println("==========stream switch done===============");

        //2. make quote from old stream is not aggregated
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1,1.21,1.24,1000,1000 );  //default that this func sends ----return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.21, 1.24);
        quote.setStreamIdx(1001);//send for old stream
        manager.handleRate(quote);
        System.out.println("=====LP12 quote from old stream is not aggregated==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("=====================Old stream quote after stream switch same as LP2 quote============================"+ aggregate.toString());
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());

        //3. make sure quote from new stream is getting aggregated
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.22, 1.26); // default that this func sends ----return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.21, 1.24);
        quote.setStreamIdx(2001);//send for old stream ///shud be new stream
        manager.handleRate(quote);
        System.out.println("=====LP12 quote from new stream with new LE is getting aggregated==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("================New stream quote after stream switch ================================="+aggregate.toString());
        //aggregates the LP22 rate old quote(3) and the LP12 new quote stream with 1.26 is not considered, since new stream is inactive
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.001);
        assertEquals(1, aggregate.getNumBids());
        assertEquals(2, aggregate.getNumOffers());

    }


    @Test
    public void TestSwitchStreamChangeFBATwoFIs() throws Exception {

        System.out.println("=======START:TestSwitchStreamChangeFBATwoFIs TestCase start==============");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);
        service.provision(TESTFI2);  //// Provision another FI

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        FIProvision fiProvision2 = serverProvision.getFIProvision(1002);
        FIProvisionImpl impl2 = (FIProvisionImpl) fiProvision2;
        impl2.setAggregationType(MDFAggregationType.FULL_BOOK);

        LPProvision lp1Prov = null;
        for (LPProvision lpProv : fiProvision.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lp1Prov = lpProv;
                break;
            }
        }
        Assert.assertNotNull(lp1Prov);

        LPProvision lp1Prov2 = null;
        for (LPProvision lpProv : fiProvision2.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lp1Prov2 = lpProv;
                break;
            }
        }
        Assert.assertNotNull(lp1Prov2);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        RateDistributionManager manager2 = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        manager2.createRateBooks(fiProvision2);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        Assert.assertEquals(15, manager2.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet2 = manager2.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet2) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision2.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision2, manager2, 1001);

        Assert.assertNotNull(book1);
        Assert.assertNotNull(book2);
        // Assertion to verify same book across Lp's
        //Assert.assertSame("Books not same across Lp's", book1, book2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }


        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision,6,1.21,1.24,1000,1000);
        manager.handleRate(quote);

        //make sure LP1 is withdrawn
        PriceBook aggregate = book1.aggregate();
        System.out.println("=======book1 when stream active========" +aggregate.toString());

        //make sure only LP2 is aggregated
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(6, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());


        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision2,6,1.21,1.24,1000,1000);
        manager2.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision2,5,1.21,1.24,1000,1000);
        manager2.handleRate(quote);

        //make sure LP1 is withdrawn
        PriceBook aggregate2 = book2.aggregate();
        System.out.println("=======book2 when stream active========" +aggregate2.toString());

        //make sure only LP2 is aggregated
        assertEquals(1.21, aggregate2.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate2.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate2.getBidPrices()[1], 0.01);
        assertEquals(2000.00, aggregate2.getBidQtys()[0], 0.01);
        assertEquals(4000.00, aggregate2.getBidQtys()[1], 0.01);
        assertEquals(6000.00, aggregate2.getBidQtys()[5], 0.01); //Last tier from LP21 only
        assertEquals(6000.00, aggregate2.getOfferQtys()[5], 0.01);  //Last tier from LP21 only
        assertEquals(6, aggregate2.getNumBids());
        assertEquals(6, aggregate2.getNumOffers());


        //1. switch stream to inactive stream
        manager.handleStreamSwitch(fiProvision, 1001, 2001, false, "TestStream2", LP12);
        System.out.println("==========stream switch done for FI1===============");

        //2. make quote from old stream is not aggregated
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);  //default that this func sends ----return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.21, 1.24);
        quote.setStreamIdx(1001);//send for old stream
        manager.handleRate(quote);

        aggregate = book1.aggregate();
        System.out.println("=====================Old stream quote after stream switch============================"+ aggregate.toString());
        //Only quote from LP22 is retained,,. LP12 quotes are removed from aggregation
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(6000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[5], 0.01);  //Last tier
        assertEquals(6, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());

        //3. make sure quote from new stream is getting aggregated
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.22, 1.26); // default that this func sends ----return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.21, 1.24);
        quote.setStreamIdx(2001);//send for old stream ///shud be new stream
        manager.handleRate(quote);

        aggregate = book1.aggregate();
        System.out.println("================New stream quote after stream switch should remain same as old one================================="+aggregate.toString());
        //aggregates the LP22 rate old quote(3) and the LP12 new quote stream with 1.26 is not considered, since new stream is inactive
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.0001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(6, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());


        //make sure LP1 is withdrawn
        aggregate2 = book2.aggregate();
        System.out.println("=======book2 should remain unaffected========" +aggregate2.toString());

        //make sure only LP2 is aggregated
        assertEquals(1.21, aggregate2.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate2.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate2.getBidPrices()[1], 0.01);
        assertEquals(2000.00, aggregate2.getBidQtys()[0], 0.01);
        assertEquals(4000.00, aggregate2.getBidQtys()[1], 0.01);
        assertEquals(6000.00, aggregate2.getBidQtys()[5], 0.01); //Last tier from LP21 only
        assertEquals(6000.00, aggregate2.getOfferQtys()[5], 0.01);  //Last tier from LP21 only
        assertEquals(6, aggregate2.getNumBids());
        assertEquals(6, aggregate2.getNumOffers());

    }


    @Test
    public void TestSwitchStreamMTFOK() throws Exception {
        //Switch stream from One active stream to another active stream FBA
        System.out.println("=========START:TestSwitchStreamFBA=========================");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,4000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24 );
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();
        System.out.println("=================quote for aggregation======================"+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        //1. switch stream
        manager.handleStreamSwitch(fiProvision, 1001, 2001, true, "TestStream2", LP12);
        System.out.println("==========stream switch done===============");


        //2. make quote from old stream is not aggregated
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);  //default that this func sends ----return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.21, 1.24);
        quote.setStreamIdx(1001);//send for old stream
        manager.handleRate(quote);
        System.out.println("=====LP12 quote from old stream is not aggregated==="+quote.toString());


        aggregate = book1.aggregate();
        System.out.println("=====================Old stream quote after stream switch============================"+ aggregate.toString());
        //Only quote from LP22 is retained,,. LP12 quotes are removed from aggregation
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(0.0, aggregate.getBidPrices()[2], 0.01);
        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.01);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());
        assertEquals(2, aggregate.getNumOffers());

        //3. make sure quote from new stream is getting aggregated
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 4, 1.22, 1.25); // default that this func sends ----return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.21, 1.24);
        quote.setStreamIdx(2001);//send for old stream ///shud be new stream
        manager.handleRate(quote);


        aggregate = book1.aggregate();
        System.out.println("================New stream quote after stream switch================================="+aggregate.toString());
        //aggregates the LP22 rate old quote(3) and the LP12 new quote stream(6 new quote sent above)
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());
        assertEquals(3, aggregate.getNumOffers());


    }

    @Test
    public void TestSwitchStreamActtoInaMTFOK() throws Exception {
        //Switch stream from One active stream to another active stream FBA
        System.out.println("=========START:TestSwitchStreamActtoInaMTFOK=========================");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,4000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, 1.24);
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();
        System.out.println("=================quote for aggregation======================"+aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        //1. switch stream to inactive stream
        manager.handleStreamSwitch(fiProvision, 1001, 2001, false, "TestStream2", LP12);
        System.out.println("==========stream switch done===============");

        //2. make quote from old stream is not aggregated
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);  //default that this func sends ----return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.21, 1.24);
        quote.setStreamIdx(1001);//send for old stream
        manager.handleRate(quote);

        aggregate = book1.aggregate();
        System.out.println("=====================Old stream quote after stream switch============================"+ aggregate.toString());
        //Only quote from LP22 is retained,,. LP12 quotes are removed from aggregation
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.001);
        assertEquals(2, aggregate.getNumBids());
        assertEquals(2, aggregate.getNumOffers());

        //3. make sure quote from new stream is getting aggregated
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.22, 1.26); // default that this func sends ----return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.21, 1.24);
        quote.setStreamIdx(2001);//send for old stream ///shud be new stream
        manager.handleRate(quote);

        aggregate = book1.aggregate();
        System.out.println("================New stream quote after stream switch should remain same as old one================================="+aggregate.toString());
        //aggregates the LP22 rate old quote(3) and the LP12 new quote stream with 1.26 is not considered, since new stream is inactive
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1.26, aggregate.getOfferPrices()[1], 0.001);
        assertEquals(2, aggregate.getNumBids());
        assertEquals(2, aggregate.getNumOffers());


    }
    //@Test
    public void MDFrestart() throws Exception {
        System.out.println("=========START:MDFrestart=========================");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

    }


    /*@Test
    public void TestSwitchStream() throws Exception {

        System.out.println("===== START : TestSwitchStream =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        System.out.println("========================== FIProvision ======================");
        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);
        //impl.setLPSupportedCcyPairs(lpSupportedCcyPairs);

        System.out.println("====================RateDistribution Manager=================");
        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        System.out.println("====================handleStreamStatusChange=================");
        manager.handleStreamStatusChange(1, (byte) 0,LP12);  //-- switching off stream
        //manager.handleStreamStatusChange(1, (byte) 1,LP12);  // -- switching on stream
        // manager.handleStreamUpdate(buffer,streamUpdate);
        //create new rate channel .. new stream

        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        book1.processStreamStatusUpdate(1, true, LP12);
        //book1.switchStream(1, , , )
        System.out.println("====================Check if status is inactive for stream1 lp1=================");
        Assert.assertTrue(book1.isActive()); //-- does not work



        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        //service.provision(TESTFI2);
        for (RateBook rBook : manager.rateBooks.values()) {

            Assert.assertTrue(rBook.isActive());
            System.out.println("===rbook====" +rBook.isActive());
            System.out.println("===rbook2====" +manager.rateBooks.values().stream().toString());
        }

        for(RateBook rateBook : manager.rateBooks.values()) {
            System.out.println("========inactive for stream1 lp1=====" + manager.rateChannels.values().stream().toString());
                    }



//        System.out.println("===============GetQuoteCPrecision EUR_USD======================");
//        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.245,  1.445 );
//        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
//        manager.handleRate(quote);
//        System.out.println("=========================LP1 precisioned quote===============" + quote.toString());
//        Assert.assertEquals(1, quote.getStreamIdx(), 0.001);

        //System.out.println("=================Switching Stream===================");


        //System.out.println("=====LP stream name=========" + fiProvision.getLPProvisions());
        //lpProvision.getStreamName()
//        int result = manager.handleStreamSwitch(fiProvision,1,2,true,"lp1stream2",LP12);
//        Assert.assertNotNull(result);
//
//        QuoteC quote1 = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.245,  1.445 );
//        quote1.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
//        manager.handleRate(quote1);
//        System.out.println("=========================LP1 precisioned quote after stream switch===============" + quote1.toString());
//        Assert.assertEquals(2, quote1.getStreamIdx(), 0.001);

//
//        PriceBook aggregate = book1.aggregate();
//        System.out.println("========Aggregated Quote=============" + aggregate.toString());
//
//        Assert.assertEquals(1.2457, aggregate.getBidPrices()[0], 0.001);
//        Assert.assertEquals(1.4458, aggregate.getOfferPrices()[0], 0.001);
//        System.out.println("=================END: TestSwitchStream=========================");


    }
*/


}
