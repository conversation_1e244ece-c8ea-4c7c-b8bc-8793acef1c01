package com.integral.mdf.rate;

import com.google.gson.Gson;
import com.integral.mdf.DefaultProvisioningTest;
import com.integral.mdf.PriceBookSink;
import com.integral.mdf.RateSource;
import com.integral.mdf.Util;
import com.integral.mdf.data.*;
import com.integral.model.OracleEntity;
import com.integral.notifications.StreamUpdate;
import com.integral.provision.LPProvision;
import com.integral.provision.MDFAggregationType;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map.Entry;
import java.util.Set;

import static org.junit.Assert.*;

public class TestPriceAggregation extends DefaultProvisioningTest {

    private static final String LP22 = "lp2";
    private static final String LP12 = "lp1";
    private static final String EUR_USD = "EUR/USD";

    @Test
    public void testCreatingRateBooksForProvisionedFI() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.20, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(6, aggregate.getNumOffers());
    }


    @Test
    public void testRawBookAggregationBase() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[5], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[6], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[7], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[8], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[9], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[8], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[9], 0.01);
        assertEquals(10, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(10, aggregate.getNumOffers());
    }


    @Test
    public void testRawBookAggregationInactiveQuoteSizeOne() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[5], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[6], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[7], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[8], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[9], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[8], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[9], 0.01);
        assertEquals(10, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(10, aggregate.getNumOffers());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 0.0d, 0.0d, 0.0d, 0.0d);

        System.out.println("Inactive quote -> " + quote.toString());

        manager.handleRate(quote);

        aggregate = book1.aggregate();

        System.out.println("Pricebook -> " + aggregate);
    }


    @Test
    public void testRawBookAggregationLargerAmount() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        manager.handleRate(quote);

        //  quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 6,
                1.21d, 1.24d, 2000d, 2000d);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[5], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[6], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[7], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[8], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[9], 0.01);

        //testing the aggregation
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(6000.00, aggregate.getBidQtys()[8], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[9], 0.01);
        assertEquals(10, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(10, aggregate.getNumOffers());
    }

    @Test
    public void testBestPriceAggregationBase() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
    }

    @Test
    public void testBestPriceAggregationLargeAmount() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        manager.handleRate(quote);

        // quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 6,
                1.21d, 1.24d, 2000d, 2000d);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
    }


    @Test
    public void testBPASingleLPInactiveQuote() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.BEST_PRICE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        manager.handleRate(quote);

//        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);
//
//        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        manager.handleRate(quote);

        aggregate = book1.aggregate();

        assertNull(aggregate);

        //send
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 0, 0.0d, 0.0d);

        manager.handleRate(quote);
        aggregate = book1.aggregate();
        assertNotNull(aggregate);
    }


    @Test
    public void testStreamSwitch() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);

        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[5], 0.01);
        //assertEquals(1.18, aggregate.getBidPrices()[6], 0.01);
        //assertEquals(1.18, aggregate.getBidPrices()[7], 0.01);
        //assertEquals(1.17, aggregate.getBidPrices()[8], 0.01);
        //assertEquals(1.17, aggregate.getBidPrices()[9], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(6, aggregate.getNumOffers());

        //1. switch stream
        manager.handleStreamSwitch(fiProvision, 1001, 2001, true, "TestStream2", LP12);

        //2. make quote from old stream is not aggregated
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setStreamIdx(1001);//send for old stream
        manager.handleRate(quote);

        aggregate = book1.aggregate();

        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());
        assertEquals(3, aggregate.getNumOffers());

        //3. make sure quote from new stream is getting aggregated
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setStreamIdx(2001);//send for old stream
        manager.handleRate(quote);

        aggregate = book1.aggregate();

        assertEquals(1.21, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(9, aggregate.getNumBids());
        assertEquals(9, aggregate.getNumOffers());

    }


    @Test
    public void testStreamStatusChange() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        LPProvision lp1Prov = null;
        for (LPProvision lpProv : fiProvision.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lp1Prov = lpProv;
                break;
            }
        }
        Assert.assertNotNull(lp1Prov);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[5], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[6], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[7], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[8], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[9], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[8], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[9], 0.01);
        assertEquals(10, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(10, aggregate.getNumOffers());

        //1. disable stream
        manager.handleStreamStatusChange(1001, StreamUpdate.STREAM_INACTIVE, lp1Prov.getShortName());

        //make sure LP1 is withdrawn
        aggregate = book1.aggregate();

        //make sure only LP2 is aggregated
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(6, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());


        //2. make quote from stream which is inactive now
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setStreamIdx(1001);//send for old stream
        manager.handleRate(quote);

        aggregate = book1.aggregate();

        //make sure only LP2 is aggregate
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(6, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());

//		//3. make sure quote from new stream is getting aggregated
//		quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
//		quote.setStreamIdx(2001);//send for old stream
//		manager.handleRate(quote);
//		
//		aggregate = book1.aggregate();
//		
//		assertEquals(1.21, aggregate.getBidPrices() [0], 0.001);
//		assertNotEquals(1.21, aggregate.getBidPrices() [1], 0.01);
//		assertEquals(1.2, aggregate.getBidPrices() [1], 0.01);
//		assertEquals(6, aggregate.getNumBids());
//		assertEquals(6, aggregate.getNumOffers());

    }


    @Test
    public void testLRLPUpdateRealLP() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[5], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[6], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[7], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[8], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[9], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[8], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[9], 0.01);
        assertEquals(10, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(10, aggregate.getNumOffers());

        //1. disable stream
        manager.handleLiquidityRulesLPUpdate(fiProvision, 1001, StreamUpdate.STREAM_INACTIVE);

        //make sure LP1 is withdrawn
        aggregate = book1.aggregate();

        //make sure only LP2 is aggregated
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(6, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());


        //2. make quote from stream which is inactive now
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setStreamIdx(1001);//send for old stream
        manager.handleRate(quote);

        aggregate = book1.aggregate();

        //make sure only LP2 is aggregate
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(6, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());

//		//3. make sure quote from new stream is getting aggregated
//		quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
//		quote.setStreamIdx(2001);//send for old stream
//		manager.handleRate(quote);
//		
//		aggregate = book1.aggregate();
//		
//		assertEquals(1.21, aggregate.getBidPrices() [0], 0.001);
//		assertNotEquals(1.21, aggregate.getBidPrices() [1], 0.01);
//		assertEquals(1.2, aggregate.getBidPrices() [1], 0.01);
//		assertEquals(6, aggregate.getNumBids());
//		assertEquals(6, aggregate.getNumOffers());

    }

    /**
     * Test to validate that there is no aggregation when LP is disabled
     * in Liquidity rules and stream is active
     *
     * @throws Exception
     */
    @Test
    public void testRealLP_LRDisabledOnStartup() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        LPProvision lp1Prov = null;
        // disable LP12 in LPProvision
        for (LPProvision lpProv : fiProvision.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lpProv.setLrEnabled(false);
                lp1Prov = lpProv;
                break;
            }
        }
        Assert.assertNotNull(lp1Prov);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);

        manager.handleRate(quote);

        //make sure LP1 is withdrawn
        PriceBook aggregate = book1.aggregate();

        //make sure only LP2 is aggregated
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(6, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());


        //2. make quote from stream which is inactive now
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setStreamIdx(1001);//send for old stream
        manager.handleRate(quote);

        aggregate = book1.aggregate();

        //make sure only LP2 is aggregate
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(6, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());
    }

    /**
     * Test to validate that there is no aggregation when stream is disabled
     * and LP is enabled in liquidity rules
     *
     * @throws Exception
     */
    @Test
    public void testRealLP_StreamDisabledOnStartup() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        LPProvision lp1Prov = null;
        // disable stream status in LPProvision
        for (LPProvision lpProv : fiProvision.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lpProv.setStreamStatus(false);
                lp1Prov = lpProv;
                break;
            }
        }
        Assert.assertNotNull(lp1Prov);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);

        manager.handleRate(quote);

        //make sure LP1 is withdrawn
        PriceBook aggregate = book1.aggregate();

        //make sure only LP2 is aggregated
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(6, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());

        //2. make quote from stream which is inactive now
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setStreamIdx(1001);//send for old stream
        manager.handleRate(quote);

        aggregate = book1.aggregate();

        //make sure only LP2 is aggregate
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(6, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());
    }

    /**
     * Test to validate that there is no aggregation when stream is disabled
     * and LP is disabled in liquidity rules
     *
     * @throws Exception
     */
    @Test
    public void testRealLP_LRStreamDisabledOnStartup() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        LPProvision lp1Prov = null;
        // disable both LR & stream status in LPProvision
        for (LPProvision lpProv : fiProvision.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lpProv.setStreamStatus(false);
                lpProv.setLrEnabled(false);
                lp1Prov = lpProv;
                break;
            }
        }
        Assert.assertNotNull(lp1Prov);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);

        manager.handleRate(quote);

        //make sure LP1 is withdrawn
        PriceBook aggregate = book1.aggregate();

        //make sure only LP2 is aggregated
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(6, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());

        //2. make quote from stream which is inactive now
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setStreamIdx(1001);//send for old stream
        manager.handleRate(quote);

        aggregate = book1.aggregate();

        //make sure only LP2 is aggregate
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(6, aggregate.getNumBids());
        assertEquals(6, aggregate.getNumOffers());
    }

    @Test
    public void testRawAggregationOneSideBidsMultiQuote() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.27962d, true); //
        // getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);

        //quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.279635d, 1.281155d); //getQuoteC
        // (serverProvision, EUR_USD, LP22, fiProvision);

        //manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        /*
         * 17:01:06.174 #[-]
         * lp1|stream1|0|0|0|3|1.2796|1000.0|1000.0|1.2696|2000.0|2000.0|1.2596|3000.0|3000.0|0
         * RBA testfi1|EUR|USD|0|3|1.2796|1000.0|1.2696|2000.0|1.2596|3000.0|0

         */

        //assert For bid price
        assertEquals(3, aggregate.getNumBids());

        assertEquals(1.27962, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.26962, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.25962, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);

        //assert For offer price
        assertEquals(0, aggregate.getNumOffers());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
    }


    @Test
    public void testRawAggregationOneSideRatesOffersMultiQuote() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.27962d, false); //
        // getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);

        //quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.279635d, 1.281155d); //getQuoteC
        // (serverProvision, EUR_USD, LP22, fiProvision);

        //manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        /*
         * 17:01:06.174 #[-]
         * lp1|stream1|0|0|0|3|1.2796|1000.0|1000.0|1.2696|2000.0|2000.0|1.2596|3000.0|3000.0|0
         * RBA testfi1|EUR|USD|0|3|1.2796|1000.0|1.2696|2000.0|1.2596|3000.0|0

         */

        //assert For offer price
        assertEquals(3, aggregate.getNumOffers());

        assertEquals(1.27962, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1.2897, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.2997, aggregate.getOfferPrices()[2], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);


        //assert For bid price
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getBidPrices()[0], 0.0);
        assertEquals(0.0, aggregate.getBidQtys()[0], 0.0);

    }


    @Test
    public void testReproduceRawAggregationOneSideBidsMQToMTChangeBug() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.27962d, true); //
        // getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);

        //quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.279635d, 1.281155d); //getQuoteC
        // (serverProvision, EUR_USD, LP22, fiProvision);

        //manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        /*
         * 17:01:06.174 #[-]
         * lp1|stream1|0|0|0|3|1.2796|1000.0|1000.0|1.2696|2000.0|2000.0|1.2596|3000.0|3000.0|0
         * RBA testfi1|EUR|USD|0|3|1.2796|1000.0|1.2696|2000.0|1.2596|3000.0|0

         */

        //assert For bid price
        assertEquals(1, aggregate.getNumBids());

        assertEquals(1.27962, aggregate.getBidPrices()[0], 0.001);
        // assertEquals(1.26962, aggregate.getBidPrices()[1], 0.01);
        // assertEquals(1.25962, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        // assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        // assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);

        //assert For offer price
        assertEquals(0, aggregate.getNumOffers());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);

        //send MQ quote now.
        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.2899d, true); //
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);

        manager.handleRate(quote);
        aggregate = book1.aggregate();

        assertEquals(1, aggregate.getNumBids());
        assertEquals(1.2899, aggregate.getBidPrices()[0], 0.001);
        //assert For offer price
        assertEquals(0, aggregate.getNumOffers());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);
    }


    @Test
    public void testRawAggregationOneSideBidsMultiTier() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.27962d, true); //
        // getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);

        manager.handleRate(quote);

        //quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.279635d, 1.281155d); //getQuoteC
        // (serverProvision, EUR_USD, LP22, fiProvision);

        //manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        /*
         * 17:01:06.174 #[-]
         * lp1|stream1|0|0|0|3|1.2796|1000.0|1000.0|1.2696|2000.0|2000.0|1.2596|3000.0|3000.0|0
         * RBA testfi1|EUR|USD|0|3|1.2796|1000.0|1.2696|2000.0|1.2596|3000.0|0

         */

        //assert For bid price
        assertEquals(1, aggregate.getNumBids());

        assertEquals(1.27962, aggregate.getBidPrices()[0], 0.001);
        //assertEquals(1.26962, aggregate.getBidPrices()[1], 0.01);
        //assertEquals(1.25962, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        //assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        //assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);

        //assert For offer price
        assertEquals(0, aggregate.getNumOffers());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
    }


    @Test
    public void testRawAggregationOneSideRatesOffersMultiTier() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.27962d, false); //
        // getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);

        manager.handleRate(quote);

        //quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.279635d, 1.281155d); //getQuoteC
        // (serverProvision, EUR_USD, LP22, fiProvision);

        //manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        /*
         * 17:01:06.174 #[-]
         * lp1|stream1|0|0|0|3|1.2796|1000.0|1000.0|1.2696|2000.0|2000.0|1.2596|3000.0|3000.0|0
         * RBA testfi1|EUR|USD|0|3|1.2796|1000.0|1.2696|2000.0|1.2596|3000.0|0

         */

        //assert For offer price
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1.27962, aggregate.getOfferPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);

        //assert For bid price
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getBidPrices()[0], 0.0);
        assertEquals(0.0, aggregate.getBidQtys()[0], 0.0);

    }


    @Test
    public void testFullBookAggregationOneSideBidsMultiQuote() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.27962d, true); //
        // getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);

        //quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.279635d, 1.281155d); //getQuoteC
        // (serverProvision, EUR_USD, LP22, fiProvision);

        //manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        /*
         * 17:01:06.174 #[-]
         * lp1|stream1|0|0|0|3|1.2796|1000.0|1000.0|1.2696|2000.0|2000.0|1.2596|3000.0|3000.0|0
         * RBA testfi1|EUR|USD|0|3|1.2796|1000.0|1.2696|2000.0|1.2596|3000.0|0

         */

        //assert For bid price
        assertEquals(3, aggregate.getNumBids());

        assertEquals(1.27962, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.26962, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.25962, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);

        //assert For offer price
        assertEquals(0, aggregate.getNumOffers());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
    }


    @Test
    public void testFullBookAggregationOneSideRatesOffersMultiQuote() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.27962d, false); //
        // getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);

        //quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.279635d, 1.281155d); //getQuoteC
        // (serverProvision, EUR_USD, LP22, fiProvision);

        //manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        /*
         * 17:01:06.174 #[-]
         * lp1|stream1|0|0|0|3|1.2796|1000.0|1000.0|1.2696|2000.0|2000.0|1.2596|3000.0|3000.0|0
         * RBA testfi1|EUR|USD|0|3|1.2796|1000.0|1.2696|2000.0|1.2596|3000.0|0

         */

        //assert For offer price
        assertEquals(3, aggregate.getNumOffers());

        assertEquals(1.27962, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(1.2897, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.2997, aggregate.getOfferPrices()[2], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);


        //assert For bid price
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getBidPrices()[0], 0.0);
        assertEquals(0.0, aggregate.getBidQtys()[0], 0.0);

    }


    @Test
    public void testFullBookAggregationOneSideBidsMultiTier() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.27962d, true); //
        // getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);

        manager.handleRate(quote);

        //quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.279635d, 1.281155d); //getQuoteC
        // (serverProvision, EUR_USD, LP22, fiProvision);

        //manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        /*
         * 17:01:06.174 #[-]
         * lp1|stream1|0|0|0|3|1.2796|1000.0|1000.0|1.2696|2000.0|2000.0|1.2596|3000.0|3000.0|0
         * RBA testfi1|EUR|USD|0|3|1.2796|1000.0|1.2696|2000.0|1.2596|3000.0|0

         */

        //assert For bid price
        assertEquals(1, aggregate.getNumBids());

        assertEquals(1.27962, aggregate.getBidPrices()[0], 0.001);
        //assertEquals(1.26962, aggregate.getBidPrices()[1], 0.01);
        //assertEquals(1.25962, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        //assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        //assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);

        //assert For offer price
        assertEquals(0, aggregate.getNumOffers());
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
    }


    @Test
    public void testFullBookAggregationOneSideRatesOffersMultiTier() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.FULL_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.27962d, false); //
        // getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);

        manager.handleRate(quote);

        //quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.279635d, 1.281155d); //getQuoteC
        // (serverProvision, EUR_USD, LP22, fiProvision);

        //manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        /*
         * 17:01:06.174 #[-]
         * lp1|stream1|0|0|0|3|1.2796|1000.0|1000.0|1.2696|2000.0|2000.0|1.2596|3000.0|3000.0|0
         * RBA testfi1|EUR|USD|0|3|1.2796|1000.0|1.2696|2000.0|1.2596|3000.0|0

         */

        //assert For offer price
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1.27962, aggregate.getOfferPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);

        //assert For bid price
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0.0, aggregate.getBidPrices()[0], 0.0);
        assertEquals(0.0, aggregate.getBidQtys()[0], 0.0);

    }


    @Test
    public void testRawAggregationWithInvertedRateFilter() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        assertEquals("Expecting 11 LP's to be provisioned for the fi", 11, fiProvision.getLPProvisions().size());

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        QuoteC quote = getInvertedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3);
        manager.handleRate(quote);

        Thread.sleep(10);

        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3);
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, "lp3", fiProvision, 3);
        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        assertNotNull(aggregate);

		/*
		 *  lp2 is dropped
		 *  
			lp1|stream1|0|0|0|3|1.21|1000.0|1000.0|1.2|2000.0|2000.0|1.19|3000.0|3000.0|3|1.24|1000.0|1000.0|1.25
			|2000.0|2000.0|1.26|3000.0|3000.0
			lp3|stream3|0|0|0|3|1.21|1000.0|1000.0|1.2|2000.0|2000.0|1.19|3000.0|3000.0|3|1.24|1000.0|1000.0|1.25
			|2000.0|2000.0|1.26|3000.0|3000.0
			RBA testfi1|EUR|USD|0|6|1.21|1000.0|1.21|1000.0|1.2|2000.0|1.2|2000.0|1.19|3000.0|1.19|3000.0|
			                      6|1.24|1000.0|1.24|1000.0|1.25|2000.0|1.25|2000.0|1.26|3000.0|1.26|3000.0

		 */

        assertEquals(1000.00d, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00d, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2000.00d, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2000.00d, aggregate.getBidQtys()[3], 0.01);
        assertEquals(3000.00d, aggregate.getBidQtys()[4], 0.01);
        assertEquals(3000.00d, aggregate.getBidQtys()[5], 0.01);

        assertEquals(1.21d, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1.21d, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(1.20d, aggregate.getBidPrices()[2], 0.0001);
        assertEquals(1.20d, aggregate.getBidPrices()[3], 0.0001);
        assertEquals(1.19d, aggregate.getBidPrices()[4], 0.0001);
        assertEquals(1.19d, aggregate.getBidPrices()[5], 0.0001);

        //The book is sorted at the offer side as well
        assertEquals(1000.00d, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00d, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2000.00d, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2000.00d, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(3000.00d, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(3000.00d, aggregate.getOfferQtys()[5], 0.01);

        assertEquals(1.24d, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(1.24d, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(1.25d, aggregate.getOfferPrices()[2], 0.0001);
        assertEquals(1.25d, aggregate.getOfferPrices()[3], 0.0001);
        assertEquals(1.26d, aggregate.getOfferPrices()[4], 0.0001);
        assertEquals(1.26d, aggregate.getOfferPrices()[5], 0.0001);

    }


    @Test
    public void testRawBookAggregationTiersLessThenMaxDepth() throws Exception {

        //max depth is 10 , let add 2 3-tiered quotes.

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3);

        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[5], 0.01);


        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(6, aggregate.getNumOffers());
    }


    @Test
    public void testRawBookAggregationInactiveQuote() throws Exception {

        //max depth is 10 , let add 2 3-tiered quotes.

        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 0.0d, 0.0d);

        manager.handleRate(quote);

        //quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision,3);

        //manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();


        assertNotNull(aggregate);
        assertEquals(1, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());
    }

    @Test
    public void testCreatingRateBooksForProvisionedFIQA() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.27962d, 1.28116d); //  getQuoteC
        // (serverProvision, EUR_USD, LP12, fiProvision);

        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.279635d, 1.281155d); //getQuoteC
        // (serverProvision, EUR_USD, LP22, fiProvision);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        //assert For bid price
        assertEquals(1.2796, aggregate.getBidPrices()[0], 0.001);
        //assertEquals(1.2796, aggregate.getBidPrices() [1], 0.01);
        //assertEquals(1.19, aggregate.getBidPrices() [2], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        //assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2811, aggregate.getOfferPrices()[0], 0.01);
        //assertEquals(1.2811, aggregate.getOfferPrices() [1], 0.01);
        //assertEquals(1.26, aggregate.getOfferPrices() [2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        //assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumOffers());
    }


    @Test
    public void testAggregationOneSideRates() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.27962d, true); //
        // getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        manager.handleRate(quote);

        //quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.279635d, 1.281155d); //getQuoteC
        // (serverProvision, EUR_USD, LP22, fiProvision);

        //manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        //assert For bid price
        assertEquals(1.2796, aggregate.getBidPrices()[0], 0.001);
        //assertEquals(1.2796, aggregate.getBidPrices() [1], 0.01);
        //assertEquals(1.19, aggregate.getBidPrices() [2], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        //assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        //assertEquals(1.2811, aggregate.getOfferPrices() [0], 0.01);
        //assertEquals(1.2811, aggregate.getOfferPrices() [1], 0.01);
        //assertEquals(1.26, aggregate.getOfferPrices() [2], 0.01);

        //The book is sorted at the offer side as well
        //assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        //assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        //assertEquals(2, aggregate.getNumOffers());
    }

    @Test
    public void testAggregationWithInvertedRateFilter() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        assertEquals("Expecting 11 LP's to be provisioned for the fi", 11, fiProvision.getLPProvisions().size());

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        QuoteC quote = getInvertedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3);
        manager.handleRate(quote);

        Thread.sleep(10);

        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3);
        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, "lp3", fiProvision, 3);
        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        //testing the aggregation for merging the quotes form provider 1 & 3 . Discarding quote 2 due to inverted
        // rate filter
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(6000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(0.00, aggregate.getBidQtys()[3], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(6000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(0.00, aggregate.getOfferQtys()[3], 0.01);
    }


    @Test
    public void testAggregationSingleLPInactiveQuote() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        assertEquals("Expecting 11 LP's to be provisioned for the fi", 11, fiProvision.getLPProvisions().size());

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3);
        manager.handleRate(quote);

        Thread.sleep(10);

        //quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision,3);
        //manager.handleRate(quote);

        //quote = getQuoteC(serverProvision, EUR_USD, "lp3", fiProvision,3);
        //manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        /*
         * lp2|stream2|17500|0|0|3|1.21|1000.0|1000.0|1.2|2000.0|2000.0|1.19|3000.0|3000.0|3|1.24|1000.0|1000.0|1.25
         * |2000.0|2000.0|1.26|3000.0|3000.0
         * FBA testfi1|EUR|USD|0|17500|3|1.21|1000.0|1.2|2000.0|1.19|3000.0|3|1.24|1000.0|1.25|2000.0|1.26|3000.0

         */

        //testing the aggregation for merging the quotes form provider 1.
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(0.00, aggregate.getBidQtys()[3], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(0.00, aggregate.getOfferQtys()[3], 0.01);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 0.0d, 0.0d);
        manager.handleRate(quote);

        aggregate = book1.aggregate();

        assertNotNull(aggregate);
        assertEquals(1, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());
    }

    @Test
    public void testAggregationWithMultipleInvertedRates() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        assertEquals("Expecting 11 LP's to be provisioned for the fi", 11, fiProvision.getLPProvisions().size());

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        QuoteC quote = getInvertedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3);
        manager.handleRate(quote);

        quote = getInvertedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3);
        manager.handleRate(quote);

        Thread.sleep(10);

        quote = getQuoteC(serverProvision, EUR_USD, "lp3", fiProvision, 3);
        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        //testing the aggregation for merging the quotes form provider 1 & 3 . Discarding quote 2 due to inverted
        // rate filter
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(0.00, aggregate.getBidQtys()[3], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(0.00, aggregate.getOfferQtys()[3], 0.01);
    }

    @Test
    public void testAggregationWithSingleInvertedRates() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        QuoteC quote = getSingleInvertedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3);
        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        assertNotNull(aggregate);
        assertEquals(1, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());
    }

    @Test
    public void testAggregationforRateAndLimitRoundingWithHighPrecision() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);
        FIProvision fiProvision = serverProvision.getFIProvision(1001);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        QuoteC quote = getHighPrecisionQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        manager.handleRate(quote);

        quote = getHighPrecisionQuoteC(serverProvision, EUR_USD, LP22, fiProvision);

        manager.handleRate(quote);

        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        PriceBook aggregate = book1.aggregate();

        assertEquals(1.2115, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.2015, aggregate.getBidPrices()[1], 0.01);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.66, aggregate.getBidQtys()[0], 0.01);
        assertEquals(4000.66, aggregate.getBidQtys()[1], 0.01);

        assertEquals(1.2449, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.2549, aggregate.getOfferPrices()[1], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(2000.66, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000.66, aggregate.getOfferQtys()[1], 0.01);
    }


    @Test
    public void testHeartbeatTimeOut() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.RAW_BOOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[2], 0.01);
        assertEquals(1.20, aggregate.getBidPrices()[3], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[4], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[5], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[6], 0.01);
        assertEquals(1.18, aggregate.getBidPrices()[7], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[8], 0.01);
        assertEquals(1.17, aggregate.getBidPrices()[9], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[8], 0.01);
        assertEquals(5000.00, aggregate.getBidQtys()[9], 0.01);
        assertEquals(10, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(10, aggregate.getNumOffers());

        manager.onHeartbeatTimeout();

        aggregate = book1.aggregate();
        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());

    }


    @Test
    public void testMTFOKAggregationBase() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000, 2000, 3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        manager.handleRate(quote);

        System.out.println("Quote1 = " + quote);


        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);

        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3, aggregate.getNumOffers());
    }


    @Test
    public void testMTFOKAggregationHigherTierLimit() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{10000, 20000, 30000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);

        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        //assert For bid price
        assertEquals(0, aggregate.getNumBids());
        assertEquals(0, aggregate.getNumOffers());
    }


    @Test
    public void testMTFOKAggregationHigherQuoteLimit() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{100, 200, 300});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision);

        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision);

        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        //assert For bid price
        assertEquals(1, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(300.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
    }

    @Test
    public void testMTFOKAggregationOnSidedBidMultiQuote() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{100, 200, 300});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 6, 1.21d, 1000d, true);

        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 6, 1.21d, 1000d, true);

        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("book=" + aggregate.toString());

        //assert For bid price
        assertEquals(1, aggregate.getNumBids());
        assertEquals(0, aggregate.getNumOffers());
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(300.00, aggregate.getBidQtys()[0], 0.01);
        //   assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        //   assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
    }

    @Test
    public void testMTFOKAggregationOnSidedBidMultiTier() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{100, 200, 300});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 6, 1.21d, 1000d, true);
        quote.setQuoteType(QuoteC.MULTI_TIER);

        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 6, 1.21d, 1000d, true);
        quote.setQuoteType(QuoteC.MULTI_TIER);


        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("book=" + aggregate.toString());

        //assert For bid price
        assertEquals(1, aggregate.getNumBids());
        assertEquals(0, aggregate.getNumOffers());
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.001);
        assertEquals(300.00, aggregate.getBidQtys()[0], 0.01);
        //   assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        //   assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
    }


    @Test
    public void testMTFOKAggregationOnSidedOfferMultiQuote() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{100, 200, 300});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 6, 1.24d, 1000d, false);

        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 6, 1.24d, 1000d, false);

        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("book=" + aggregate.toString());

        //assert For bid price
        assertEquals(0, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
        //   assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        //   assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
    }

    @Test
    public void testMTFOKAggregationOnSidedOfferMultiTier() throws Exception {
        ServerProvision serverProvision = doDefaultProvision();

        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{100, 200, 300});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);

        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 6, 1.24d, 1000d, false);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        System.out.println("Quote1 = " + quote);

        manager.handleRate(quote);

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 6, 1.24d, 1000d, false);
        quote.setQuoteType(QuoteC.MULTI_TIER);
        System.out.println("Quote2 = " + quote);

        manager.handleRate(quote);

        PriceBook aggregate = book1.aggregate();

        System.out.println("book=" + aggregate.toString());

        //assert For bid price
        assertEquals(0, aggregate.getNumBids());
        assertEquals(1, aggregate.getNumOffers());
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.001);
        assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
        //   assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        //   assertEquals(300.00, aggregate.getOfferQtys()[0], 0.01);
    }


    @Test
    public void testMTFOKMEzeroliquidityFixed() throws Exception {
        //As of now,.. 0 liquidity sent from ME comes as 0 only from MDF as well
        //Change will be made to drop this quote with 0 liquidity,. then the test case will fail
        System.out.println("==============START:MEzeroliquidityBase==================== ");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000, 2000, 3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.MULTI_TIER_FOK);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Entry<Long, RateChannel>> entrySet = manager.rateChannels
                .entrySet();

        for (Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }
        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);

        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.22, 1.24, 1000, 1000);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote===" + quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21d, 1.24d, 1000d, 1000d);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote===" + quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote ===" + aggregate.toString());

        //assert For bid price
        assertEquals(1.22, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);

        assertEquals(3, aggregate.getNumOffers());

        //LP1 send an updated quote with 0 liquidity, LP2 quote remains as it is
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 3, 1.22, 1.24, 0, 1000);
        manager.handleRate(quote);
        System.out.println("=====LP1 second quote===" + quote.toString());

        aggregate = book1.aggregate();
        System.out.println("===== aggregate second quote in MEzeroliquidityBase ===" + aggregate.toString());

        //assert For bid price
        assertEquals(1.21, aggregate.getBidPrices()[0], 0.01);
        assertEquals(1.2, aggregate.getBidPrices()[1], 0.01);
        assertEquals(1.19, aggregate.getBidPrices()[2], 0.01);

        //testing the aggregation
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.24, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.25, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.26, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
    }


    protected RateBook getBook(ServerProvision serverProvision,
                               FIProvision fiProvision, RateDistributionManager manager) {
        return getBook(serverProvision, fiProvision, manager, 1);
    }

    protected RateBook getBook(ServerProvision serverProvision,
                               FIProvision fiProvision, RateDistributionManager manager, int streamIdx) {
        Assert.assertTrue("Common Currency missing in the provision",
                serverProvision.getCcyPairIndex(EUR_USD).isPresent());
        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD)
                .get();
        int bCcyIndex = Util.getBaseCurrencyIndex(commonCcyPairIdx);
        int vCcyIndex = Util.getVarCurrencyIndex(commonCcyPairIdx);

        RateChannel rateChannel1 = manager.rateChannels.get(Util
                .getRateChannelKey(streamIdx, bCcyIndex, vCcyIndex));
        RateBook book1 = rateChannel1.getRatebook(fiProvision.getIndex(),
                commonCcyPairIdx, fiProvision.getAggregationType().getIndex());
        return book1;
    }

    protected RateDistributionManager getNoHopManager(ServerProvision serverProvision) throws IOException {
        RateDistributionManager manager = new RateDistributionManager(serverProvision) {

            public void handleRate(QuoteC rate) {
                super.handleRate(rate);
                int sIdx = rate.getStreamIdx();
                int cIdx = rate.getCcyPairIdx();
                int bIdx = Util.getBaseCurrencyIndex(cIdx);
                int vIdx = Util.getVarCurrencyIndex(cIdx);
                long key = Util.getRateChannelKey(sIdx, bIdx, vIdx);
                RateChannel channel = rateChannels.get(key);

                if (channel != null) {
                    RateProcessor rp = getRateProcessor(cIdx);
                    channel.updateRateBooks(rp.sink, rp.rpm);
                }
            }

        };
        manager.setRateSource(Mockito.mock(RateSource.class));

        TestPriceBookSink sink = new TestPriceBookSink();
        RateProcessor[] processors = manager.rateProcessors;
        for (RateProcessor rateProcessor : processors) {
            rateProcessor.sink = sink;
            //Shutting down the rate processor , so that aggregation could be tested directly.
            rateProcessor.shutdown();
        }
        return manager;
    }

    protected List<OracleEntity> getLPProvisions() {
        List<OracleEntity> lpProvisions = super.getLPProvisions();
        LPProvision fromJson = new Gson().fromJson(lp3, LPProvision.class);
        fromJson.setLrEnabled(true);
        fromJson.setStreamStatus(true);
        fromJson.setCcyPairs(getCcyPairProv(lp3_definedCcyPairs));
        lpProvisions.add(fromJson);
        return lpProvisions;
    }

    public QuoteC getInvertedQuoteC(ServerProvision serverProvision, String ccyp,
                                    String lpName, FIProvision fi, int noOfTiers) {
        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(ccyp).get();
        LPProvision lpSelected = null;

        for (LPProvision lp : fi.getLPProvisions()) {
            if (lp.getShortName().equals(lpName)) {
                lpSelected = lp;
                break;
            }
        }
        assertNotNull(lpSelected);
        QuoteC quote = new QuoteC();
        quote.setCcyPairIdx(commonCcyPairIdx);
        quote.setStreamIdx(Util.getStreamIndex(serverProvision, lpSelected));
        quote.setValueDate((short) 17500);

        //bids
        int bidTiers = noOfTiers;
        for (int j = 0; j < bidTiers; j++) {
            quote.setBidTiersNum(quote.getBidTiersNum() + 1);
            int bid = quote.tierOffset(QuoteC.BUY, j);
            quote.setPrice(bid, 1.25 - ((double) j / 100));
            quote.setTotalQty(bid, 1000.0d + (j * 1000.0d));
            quote.setShowQty(bid, 1000.0d + (j * 1000.0d));
        }
        //offers
        int offerTiers = noOfTiers;
        for (int j = 0; j < offerTiers; j++) {
            quote.setOfferTiersNum(quote.getOfferTiersNum() + 1);
            int offer = quote.tierOffset(QuoteC.SELL, j);
            quote.setPrice(offer, 1.29 + ((double) j / 100));
            quote.setTotalQty(offer, 1000.0d + (j * 1000.0d));
            quote.setShowQty(offer, 1000.0d + (j * 1000.0d));
        }
        return quote;
    }

    public QuoteC getSingleInvertedQuoteC(ServerProvision serverProvision, String ccyp, String lpName, FIProvision
            fi, int noOfTiers) {
        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(ccyp).get();
        LPProvision lpSelected = null;

        for (LPProvision lp : fi.getLPProvisions()) {
            if (lp.getShortName().equals(lpName)) {
                lpSelected = lp;
                break;
            }
        }
        assertNotNull(lpSelected);
        QuoteC quote = new QuoteC();
        quote.setCcyPairIdx(commonCcyPairIdx);
        quote.setStreamIdx(Util.getStreamIndex(serverProvision, lpSelected));
        quote.setValueDate((short) 17500);

        //bids
        int bidTiers = noOfTiers;
        for (int j = 0; j < bidTiers; j++) {
            quote.setBidTiersNum(quote.getBidTiersNum() + 1);
            int bid = quote.tierOffset(QuoteC.BUY, j);
            quote.setPrice(bid, 1.24 - ((double) j / 100));
            quote.setTotalQty(bid, 1000.0d + (j * 1000.0d));
            quote.setShowQty(bid, 1000.0d + (j * 1000.0d));
        }
        //offers
        int offerTiers = noOfTiers;
        for (int j = 0; j < offerTiers; j++) {
            quote.setOfferTiersNum(quote.getOfferTiersNum() + 1);
            int offer = quote.tierOffset(QuoteC.SELL, j);
            quote.setPrice(offer, 1.22 + ((double) j / 100));
            quote.setTotalQty(offer, 1000.0d + (j * 1000.0d));
            quote.setShowQty(offer, 1000.0d + (j * 1000.0d));
        }
        return quote;
    }

    public QuoteC getHighPrecisionQuoteC(ServerProvision serverProvision, String ccyp,
                                         String lpName, FIProvision fi) {
        return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.2114242, 1.2448576, 1000.3333d, 1000.3333d);
    }

    public QuoteC getQuoteC(ServerProvision serverProvision, String ccyp,
                            String lpName, FIProvision fi, int numOfTiers) {
        return getQuoteC(serverProvision, ccyp, lpName, fi, numOfTiers, 1.21, 1.24);
    }

    public QuoteC getQuoteC(ServerProvision serverProvision, String ccyp,
                            String lpName, FIProvision fi) {
        return getQuoteC(serverProvision, ccyp, lpName, fi, 6, 1.21, 1.24);
    }

    public QuoteC getQuoteC(ServerProvision serverProvision, String ccyp,
                            String lpName, FIProvision fi, int noOfTiers, double startBid, double startOffer) {
        return getQuoteC(serverProvision, ccyp, lpName, fi, noOfTiers, startBid, startOffer, 1000.0d, 1000.0d);
    }

    public QuoteC getOneSidedQuoteC(ServerProvision serverProvision, String ccyp,
                                    String lpName, FIProvision fi, int noOfTiers, double startPrice, boolean isBid) {
        return getOneSidedQuoteC(serverProvision, ccyp, lpName, fi, noOfTiers, startPrice, 1000.0d, isBid);
    }

    public QuoteC getQuoteC(ServerProvision serverProvision, String ccyp,
                            String lpName, FIProvision fi, int noOfTiers, double startBid, double startOffer,
                            double startBLimit, double startOLimit) {
        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(ccyp).get();
        LPProvision lpSelected = null;

        for (LPProvision lp : fi.getLPProvisions()) {
            if (lp.getShortName().equals(lpName)) {
                lpSelected = lp;
                break;
            }
        }
        assertNotNull(lpSelected);

        QuoteC quote = new QuoteC();
        quote.setCcyPairIdx(commonCcyPairIdx);
        quote.setStreamIdx(Util.getStreamIndex(serverProvision, lpSelected));
        quote.setValueDate((short) 17500);
        quote.setQuoteCreatedTime(System.currentTimeMillis());

        //bids
        int bidTiers = noOfTiers;
        for (int j = 0; j < bidTiers; j++) {
            quote.setBidTiersNum(quote.getBidTiersNum() + 1);
            int bid = quote.tierOffset(QuoteC.BUY, j);
            quote.setPrice(bid, startBid - ((double) j / 100));
            quote.setTotalQty(bid, startBLimit + (j * 1000.0d));
            quote.setShowQty(bid, startBLimit + (j * 1000.0d));
        }
        //offers
        int offerTiers = noOfTiers;
        for (int j = 0; j < offerTiers; j++) {
            quote.setOfferTiersNum(quote.getOfferTiersNum() + 1);
            int offer = quote.tierOffset(QuoteC.SELL, j);
            quote.setPrice(offer, startOffer + ((double) j / 100));
            quote.setTotalQty(offer, startOLimit + (j * 1000.0d));
            quote.setShowQty(offer, startOLimit + (j * 1000.0d));
        }
        return quote;
    }


    public QuoteC getOneSidedQuoteC(ServerProvision serverProvision, String ccyp,
                                    String lpName, FIProvision fi, int noOfTiers, double startPrice, double startLimit, boolean isBid) {
        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(ccyp).get();
        LPProvision lpSelected = null;

        for (LPProvision lp : fi.getLPProvisions()) {
            if (lp.getShortName().equals(lpName)) {
                lpSelected = lp;
                break;
            }
        }
        assertNotNull(lpSelected);

        QuoteC quote = new QuoteC();
        quote.setCcyPairIdx(commonCcyPairIdx);
        quote.setStreamIdx(Util.getStreamIndex(serverProvision, lpSelected));
        quote.setValueDate((short) 17500);

        if (isBid) {
            //bids
            double startBid = startPrice;
            double startBLimit = startLimit;

            int bidTiers = noOfTiers;
            for (int j = 0; j < bidTiers; j++) {
                quote.setBidTiersNum(quote.getBidTiersNum() + 1);
                int bid = quote.tierOffset(QuoteC.BUY, j);
                quote.setPrice(bid, startPrice - ((double) j / 100));
                quote.setTotalQty(bid, startBLimit + (j * 1000.0d));
                quote.setShowQty(bid, startBLimit + (j * 1000.0d));
            }
        } else {
            //offers
            double startOffer = startPrice;
            double startOLimit = startLimit;

            int offerTiers = noOfTiers;
            for (int j = 0; j < offerTiers; j++) {
                quote.setOfferTiersNum(quote.getOfferTiersNum() + 1);
                int offer = quote.tierOffset(QuoteC.SELL, j);
                quote.setPrice(offer, startOffer + ((double) j / 100));
                quote.setTotalQty(offer, startOLimit + (j * 1000.0d));
                quote.setShowQty(offer, startOLimit + (j * 1000.0d));
            }
        }
        return quote;
    }

    public static class TestPriceBookSink implements PriceBookSink {
        private PriceBook last;

        @Override
        public void begin() {
        }

        @Override
        public void accept(PriceBook book) {
            this.setLast(book);
        }

        @Override
        public void end() {
        }

        @Override
        public boolean isActive() {
            return false;
        }

        public PriceBook getLast() {
            return last;
        }

        public void setLast(PriceBook last) {
            this.last = last;
        }
    }


    public QuoteC getQuoteC(ServerProvision serverProvision, String ccyp, String lpName, FIProvision fi,
                            int noOfBidTiers, int noOfOfferTiers, double startBid, double startOffer, double startBLimit,
                            double startOLimit) {
        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(ccyp).get();
        LPProvision lpSelected = null;

        for (LPProvision lp : fi.getLPProvisions()) {
            if (lp.getShortName().equals(lpName)) {
                lpSelected = lp;
                break;
            }
        }
        assertNotNull(lpSelected);

        QuoteC quote = new QuoteC();
        quote.setCcyPairIdx(commonCcyPairIdx);
        quote.setStreamIdx(Util.getStreamIndex(serverProvision, lpSelected));
        quote.setValueDate((short) 17500);
        quote.setQuoteCreatedTime(System.currentTimeMillis());

        // bids
        int bidTiers = noOfBidTiers;
        for (int j = 0; j < bidTiers; j++) {
            quote.setBidTiersNum(quote.getBidTiersNum() + 1);
            int bid = quote.tierOffset(QuoteC.BUY, j);
            quote.setPrice(bid, startBid - ((double) j / 100));
            quote.setTotalQty(bid, startBLimit + (j * 1000.0d));
            quote.setShowQty(bid, startBLimit + (j * 1000.0d));
        }
        // offers
        int offerTiers = noOfOfferTiers;
        for (int j = 0; j < offerTiers; j++) {
            quote.setOfferTiersNum(quote.getOfferTiersNum() + 1);
            int offer = quote.tierOffset(QuoteC.SELL, j);
            quote.setPrice(offer, startOffer + ((double) j / 100));
            quote.setTotalQty(offer, startOLimit + (j * 1000.0d));
            quote.setShowQty(offer, startOLimit + (j * 1000.0d));
        }
        return quote;
    }

}
