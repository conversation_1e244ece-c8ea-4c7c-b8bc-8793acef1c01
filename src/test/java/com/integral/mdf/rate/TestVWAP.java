package com.integral.mdf.rate;

import com.integral.mdf.data.*;
import com.integral.notifications.StreamUpdate;
import com.integral.provision.CurrencyPairProvision;
import com.integral.provision.LPProvision;
import com.integral.provision.MDFAggregationType;
import org.junit.Assert;
import org.junit.Test;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;

public class TestVWAP extends PriceAggregationTest {

    public static final String LP22 = "lp2";
    public static final String LP12 = "lp1";
    public static final String EUR_USD = "EUR/USD";

    protected void set3TierRates(QuoteC quote,byte buyOrSell,double p1, double p2, double p3){
        //tier 1
        int tieridx = quote.tierOffset(buyOrSell, 0);
        quote.setPrice(tieridx, p1);
        //tier 2
        tieridx = quote.tierOffset(buyOrSell, 1);
        quote.setPrice(tieridx, p2);
        //tier 3
        tieridx = quote.tierOffset(buyOrSell, 2);
        quote.setPrice(tieridx, p3);
    }

    /**
     * MultiQuote-MultiQuote VWAP aggregation.
     * @throws Exception
     PLT-3520*/
    @Test
    public void testVWAPFromMultiQuoteLPs () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testVWAPFromMultiQuoteLPs =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiQuoteLPs ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1.21206, aggregate.getBidPrices()[1], 0.00001);
      //  assertEquals(1.21207, aggregate.getBidPrices()[2], 0.001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
      //  assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
   //     assertEquals(1.2122, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(1.21224, aggregate.getOfferPrices()[1], 0.0001);


        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        //assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromMultiQuoteLPs =====");
    }


    /**
     * MultiTier-MultiQuote VWAP aggregation.
     * @throws Exception
     */
    @Test
    public void testVWAPMQMTLP () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testVWAPMQMTLP =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPMQMTLP ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
  //      assertEquals(1.2121, aggregate.getBidPrices()[1], 0.001);
        assertEquals(1.21203, aggregate.getBidPrices()[1], 0.00001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
 //       assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
 //       assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(1.21227, aggregate.getOfferPrices()[1], 0.0001);


        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
 //       assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.01);

        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPMQMTLP =====");
    }


    @Test
    public void testVWAPFromMultiQuoteLPs_smalltiers () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testVWAPFromMultiQuoteLPs_smalltiers =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{500.00,1500.00,2000.00});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiQuoteLPs_smalltiers ==="+aggregate.toString());
        System.out.println("===== aggregated book ==="+book1.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
      //  assertEquals(1.2121, aggregate.getBidPrices()[1], 0.001);
        //delta value has to be more to validate all the precisions, otherwise it will round off and the assertion will pass even if value is wrong
       // assertEquals(1.2121, aggregate.getBidPrices()[2], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
    //    assertEquals(500, aggregate.getBidQtys()[0], 0.01);
    //    assertEquals(1500, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.01);
     //   assertEquals(1.2122, aggregate.getOfferPrices()[1], 0.01);
     //   assertEquals(1.2122, aggregate.getOfferPrices()[2], 0.00001);


        //The book is sorted at the offer side as well
    //    assertEquals(500, aggregate.getOfferQtys()[0], 0.01);
    //    assertEquals(1500, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2000, aggregate.getOfferQtys()[0], 0.01);

        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromMultiQuoteLPs_smalltiers =====");
    }


    @Test
    public void testVWAPFromMultiQuoteLPs_bigtiers () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testVWAPFromMultiQuoteLPs_bigtiers =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{3000,4000,5000,8000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiQuoteLPs_bigtiers ==="+aggregate.toString());
        System.out.println("===== aggregated book ==="+book1.toString());

        //assert For bid price
        assertEquals(1.21206, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.21205, aggregate.getBidPrices()[1], 0.00001);
        //delta value has to be more to validate all the precisions, otherwise it will round off and the assertion will pass even if value is wrong
        assertEquals(1.21202, aggregate.getBidPrices()[2], 0.00001);
        //1.211975 is the full value, gets rounded down to 5 precision
        assertEquals(1.21197, aggregate.getBidPrices()[3], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(3000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(4000, aggregate.getBidQtys()[1], 0.01);
        assertEquals(5000, aggregate.getBidQtys()[2], 0.01);
        assertEquals(8000, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21224, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.21225, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.21228, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1.21234, aggregate.getOfferPrices()[3], 0.00001);

        //The book is sorted at the offer side as well
        assertEquals(3000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(4000, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(5000, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(8000, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromMultiQuoteLPs_bigtiers =====");
    }

    @Test       //scenario - Tier values are too high and LP quotes do not have that much liquidity, then quote gets dropped in mdf PLT-3520
    public void testVWAPFromMultiQuoteLPs_noliquidity () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testVWAPFromMultiQuoteLPs_noliquidity =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{12000,13000,15000,18000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiQuoteLPs_noliquidity ==="+aggregate.toString());
        System.out.println("===== aggregated book ==="+book1.toString());

        //assert For bid price
        assertEquals(1.21192, aggregate.getBidPrices()[0], 0.00001);
        assertNotEquals(1.21205, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(0.0, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(12000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(0.0, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21245, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(12000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(0.0, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromMultiQuoteLPs_noliquidity =====");
    }


    @Test
    public void testVWAPFromMTMQTierLimitEQAvblQuoteLimit() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers
        System.out.println("===== START : testVWAPFromMTMQTierLimitEQAvblQuoteLimit =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000,4000,5000,7000,9000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiQuoteLPs_bigtiers ==="+aggregate.toString());
        System.out.println("===== aggregated book ==="+book1.toString());

        //assert For bid price
        assertEquals(1.21210, aggregate.getBidPrices()[0], 0.00001);
//        assertEquals(1.21210, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.21203, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.21195, aggregate.getBidPrices()[2], 0.00001);
        assertEquals(1.21194, aggregate.getBidPrices()[3], 0.00001);
        assertEquals(1.21191, aggregate.getBidPrices()[4], 0.00001);
        assertEquals(1.21188, aggregate.getBidPrices()[5], 0.00001);


        //testing the aggregation for merging the quotes form provider 1 & 2
//        assertEquals(1000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000, aggregate.getBidQtys()[1], 0.01);
        assertEquals(4000, aggregate.getBidQtys()[2], 0.01);
        assertEquals(5000, aggregate.getBidQtys()[3], 0.01);
        assertEquals(7000, aggregate.getBidQtys()[4], 0.01);
        assertEquals(9000, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
    //    assertEquals(1.212200, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.212200, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.212266, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.212350, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1.212380, aggregate.getOfferPrices()[3], 0.00001);
        assertEquals(1.212442, aggregate.getOfferPrices()[4], 0.00001);
        assertEquals(1.212500, aggregate.getOfferPrices()[5], 0.00001);

        //The book is sorted at the offer side as well
//        assertEquals(1000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(5000, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(7000, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(9000, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromMTMQTierLimitEQAvblQuoteLimit =====");
    }

    @Test   //available is 12k,, so last tier shud get 12k price  - lasttierGRlmt PLT-3520
    public void testVWAPFromMultiQuoteLPs_alltiers() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testVWAPFromMultiQuoteLPs_alltiers  =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1500.00,2500.00,3000.00,4000.00,5000.00,8000.00,10000.00,13000.00});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiQuoteLPs_alltiers ==="+aggregate.toString());
        System.out.println("===== aggregated book ==="+book1.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.21208, aggregate.getBidPrices()[1], 0.00001);
        //delta value has to be more to validate all the precisions, otherwise it will round off and the assertion will pass even if value is wrong
        assertEquals(1.212066, aggregate.getBidPrices()[2], 0.00001);
        assertEquals(1.21205, aggregate.getBidPrices()[3], 0.00001);
        assertEquals(1.21202, aggregate.getBidPrices()[4], 0.00001);
        assertEquals(1.211975, aggregate.getBidPrices()[5], 0.00001);
        assertEquals(1.21195, aggregate.getBidPrices()[6], 0.00001);
        assertEquals(1.211925, aggregate.getBidPrices()[7], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1500, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2500, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000, aggregate.getBidQtys()[3], 0.01);
        assertEquals(5000, aggregate.getBidQtys()[4], 0.01);
        assertEquals(8000, aggregate.getBidQtys()[5], 0.01);
        assertEquals(10000, aggregate.getBidQtys()[6], 0.01);
        assertEquals(12000, aggregate.getBidQtys()[7], 0.01);
        assertEquals(8, aggregate.getNumBids());

        //1.2122, 1.21222, 1.212234, 1.21225, 1.21228, 1.212338, 1.21239
        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.21222, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.212234, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1.21225, aggregate.getOfferPrices()[3], 0.00001);
        assertEquals(1.21228, aggregate.getOfferPrices()[4], 0.00001);
        assertEquals(1.212338, aggregate.getOfferPrices()[5], 0.00001);
        assertEquals(1.21239, aggregate.getOfferPrices()[6], 0.00001);
        assertEquals(1.212442, aggregate.getOfferPrices()[7], 0.00001);

        //The book is sorted at the offer side as well
        assertEquals(1500, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2500, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(4000, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(5000, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(8000, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(10000, aggregate.getOfferQtys()[6], 0.01);
        assertEquals(12000, aggregate.getOfferQtys()[7], 0.01);
        assertEquals(8, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromMultiQuoteLPs_alltiers =====");


    }


    @Test
    public void testVWAPFromMultiQuoteLPs_SQ () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testVWAPFromMultiQuoteLPs_SQ =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,0,0);
        set3TierRates(quote,QuoteC.SELL,1.2122,0,0);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,0,0);
        set3TierRates(quote,QuoteC.SELL,1.2122,0,0);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiQuoteLPs_SQ ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.00001);
        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.00001);
        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromMultiQuoteLPs_SQ =====");
    }

    @Test
    public void testVWAPFromMultiQuoteLPs_SQsingleTier () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testVWAPFromMultiQuoteLPs_SQLPsingleTier =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1500});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,0,0);
        set3TierRates(quote,QuoteC.SELL,1.2122,0,0);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,0,0);
        set3TierRates(quote,QuoteC.SELL,1.2122,0,0);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiQuoteLPs_SQLPsingleTier ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.00001);
        assertNotEquals(1.2121, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(0, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertNotEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2122, aggregate.getOfferPrices()[0], 0.00001);
        //The book is sorted at the offer side as well
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromMultiQuoteLPs_SQLPsingleTier =====");
    }

    @Test //MTMQ
    public void testVWAPFromMultiTierLPs_SQsingleTier () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testVWAPFromMultiTierLPs_SQsingleTier =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{2500});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,0);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,0);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,0);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,0);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiTierLPs_SQsingleTier ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.21208, aggregate.getBidPrices()[0], 0.00001);
        assertNotEquals(1.2121, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(0, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21222, aggregate.getOfferPrices()[0], 0.00001);
        //The book is sorted at the offer side as well
        assertEquals(2500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromMultiTierLPs_SQsingleTier =====");
    }


    @Test
    public void testVWAPFromLPCausingInvertedTiers () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote
        System.out.println("===== START : testVWAPFromLPCausingInvertedTiers =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, 1.24, 2000,2000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.19, 1.20,2000,2000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromLPCausingInvertedTiers ==="+aggregate.toString());

        //LP1 quote is dropped, and LP2 quote is aggregated in Vwap
        assertEquals(1.19, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.1866, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.00001);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.00001);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2, aggregate.getOfferPrices()[0], 0.00001);
        //Offer is doing round down instead of round up, hence this assert fails
        assertEquals(1.2034, aggregate.getOfferPrices()[1], 0.00001);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromLPCausingInvertedTiers =====");

    }


    @Test //lower precision hence passes
    public void testVWAPFromMTLPCausingInvertedTiers () throws Exception {
        // Quote causing inverted quote will be dropped, which is generally the oldest quote

        System.out.println("===== START : testVWAPFromMTLPCausingInvertedTiers =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());

        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, 1.24, 2000,2000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        sleep(1);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.19, 1.20,2000,2000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMTLPCausingInvertedTiers ==="+aggregate.toString());

        //LP1 quote is dropped, and LP2 quote is aggregated in Vwap
        assertEquals(1.19, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.18, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.00001);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.00001);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.21, aggregate.getOfferPrices()[1], 0.00001);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromMTLPCausingInvertedTiers =====");

    }


    @Test
    public void testVWAPFromLPGivingInvertedTiers () throws Exception {
        // Quote causing inverted quote should be dropped,
        //noOfTiers is 1 since MDF has to send quote with 0 rate and 1 tier size to represent inactive quote to MDG,
        //otherwise MDG will not be able to recognize inactive quote

        System.out.println("===== START : testVWAPFromLpGivingInvertedTiers =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);
        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.24, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromSQLPGivingInvertedTiers ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0.0, aggregate.getBidPrices()[0], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(0.0, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.0, aggregate.getOfferPrices()[0], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(0.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());
        System.out.println("===== END : testVWAPFromLPGivingInvertedTiers =====");

    }


    @Test  //aggregation not happening for one-sided rates  PLT-3525 fixed
    public void testVWAPFromMultiQuoteLPs_BidSideOnly () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testVWAPFromMultiQuoteLPs_BidSideOnly =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        //set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, true);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        //set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiQuoteLPs_BidSideOnly ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.212066, aggregate.getBidPrices()[1], 0.000001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumBids());

        System.out.println("===== END : testVWAPFromMultiQuoteLPs_BidSideOnly =====");
    }


    @Test  //fails should give quote for 6k, since 6k is available after stream1 inactive PLT-3520
    public void testVWAPFromMTMQ_StreamIna() throws Exception {
        System.out.println("===== START : testVWAPFromMTMQ_StreamIna =====");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000,4000,5000,7000,9000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        LPProvision lp1Prov = null;
        for (LPProvision lpProv : fiProvision.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lp1Prov = lpProv;
                break;
            }
        }
        Assert.assertNotNull(lp1Prov);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMTMQ_StreamIna ==="+aggregate.toString());
        System.out.println("===== aggregated book ==="+book1.toString());

        //assert For bid price
        assertEquals(1.21210, aggregate.getBidPrices()[0], 0.00001);
       // assertEquals(1.21210, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.21203, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.21195, aggregate.getBidPrices()[2], 0.00001);
        assertEquals(1.21194, aggregate.getBidPrices()[3], 0.00001);
        assertEquals(1.21191, aggregate.getBidPrices()[4], 0.00001);
        assertEquals(1.21188, aggregate.getBidPrices()[5], 0.00001);


        //testing the aggregation for merging the quotes form provider 1 & 2
        //assertEquals(1000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000, aggregate.getBidQtys()[1], 0.01);
        assertEquals(4000, aggregate.getBidQtys()[2], 0.01);
        assertEquals(5000, aggregate.getBidQtys()[3], 0.01);
        assertEquals(7000, aggregate.getBidQtys()[4], 0.01);
        assertEquals(9000, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.212200, aggregate.getOfferPrices()[0], 0.00001);
//        assertEquals(1.212200, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.212266, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.212350, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1.212380, aggregate.getOfferPrices()[3], 0.00001);
        assertEquals(1.212442, aggregate.getOfferPrices()[4], 0.00001);
        assertEquals(1.212500, aggregate.getOfferPrices()[5], 0.00001);

        //The book is sorted at the offer side as well
//        assertEquals(1000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(5000, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(7000, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(9000, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumOffers());


        //1. disable stream
        manager.handleStreamStatusChange(1001, StreamUpdate.STREAM_INACTIVE, lp1Prov.getShortName());

        //make sure LP1 is withdrawn
        aggregate = book1.aggregate();
        System.out.println("=======book1 when stream1 inactive========" +aggregate.toString());

        //make sure only LP2 is aggregated
        //assert For bid price
        assertEquals(1.21210, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.212, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.211966, aggregate.getBidPrices()[2], 0.00001);
        assertEquals(1.211925, aggregate.getBidPrices()[3], 0.00001);
        assertEquals(1.2119, aggregate.getBidPrices()[4], 0.00001);
        assertEquals(1.211883, aggregate.getBidPrices()[5], 0.00001); //1.211833

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000, aggregate.getBidQtys()[3], 0.01);
        assertEquals(5000, aggregate.getBidQtys()[4], 0.01);
        assertEquals(6000, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.212200, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.21235, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.2124, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1.212475, aggregate.getOfferPrices()[3], 0.00001);
        assertEquals(1.21252, aggregate.getOfferPrices()[4], 0.00001);
        assertEquals(1.21255, aggregate.getOfferPrices()[5], 0.00001);

        //The book is sorted at the offer side as well
        assertEquals(1000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(3000, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(4000, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(5000, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(6000, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumOffers());
        System.out.println("===== END : testVWAPFromMTMQ_StreamIna =====");
    }

    @Test
    public void testVWAPFromMultiQuoteLPs_SQsamebidoffer() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testVWAPFromMultiQuoteLPs_SQsamebidoffer =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1500});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.21, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,0,0);
        set3TierRates(quote,QuoteC.SELL,1.2121,0,0);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.21, 1.21);
        set3TierRates(quote,QuoteC.BUY,1.2121,0,0);
        set3TierRates(quote,QuoteC.SELL,1.2121,0,0);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiQuoteLPs_SQsamebidoffer ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.00001);
        assertNotEquals(1.2121, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(0, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertNotEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2121, aggregate.getOfferPrices()[0], 0.00001);
        //The book is sorted at the offer side as well
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        //Test agg after second quote
        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.21, 1.23);
        set3TierRates(quote,QuoteC.BUY,1.2121,0,0);
        set3TierRates(quote,QuoteC.SELL,1.2123,0,0);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 second quote==="+quote.toString());

        aggregate = book1.aggregate();
        System.out.println("=====second aggregate quote in testVWAPFromMultiQuoteLPs_SQsamebidoffer ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.00001);
        assertNotEquals(1.2121, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(0, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertNotEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.212167, aggregate.getOfferPrices()[0], 0.00001);
        //The book is sorted at the offer side as well
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());



        System.out.println("===== END : testVWAPFromMultiQuoteLPs_SQsamebidoffer =====");
    }

    @Test  //aggregated book has single tier
    public void testVWAPFromMTMQLPs_samebidoffer1T2Lps () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers
        System.out.println("===== START : testVWAPFromMTMQLPs_samebidoffer1T2Lps =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 1, 1.22, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2123,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2123,1.2120,1.2119);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.22, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2121,1.2119,1.2118);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMTMQLPs_samebidoffer1T2Lps ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.001);
        assertNotEquals(1.2121, aggregate.getBidPrices()[1], 0.001);
        assertEquals(0.0, aggregate.getBidPrices()[1], 0.001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.212, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(0.0, aggregate.getOfferPrices()[1], 0.01);
        assertNotEquals(1.21224, aggregate.getOfferPrices()[2], 0.01);

        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.01);
        assertNotEquals(2000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromMultiQuoteLPs =====");
    }

    @Test //aggregation gives 0 - LP1 giving same rates for T1 and T2
    public void testVWAPFromMultiQuoteLPs_SQsamebidoffer2T1Lp() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testVWAPFromMultiQuoteLPs_SQsamebidoffer2T1Lp =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1500,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2121,1.2121);
        set3TierRates(quote,QuoteC.SELL,1.2121,1.2121,1.2121);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiQuoteLPs_SQsamebidoffer2T1Lp ==="+aggregate.toString());

        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(0, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(3000.0, aggregate.getBidQtys()[0], 0.01);
        assertNotEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumBids());
        assertEquals(1.2121, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(3000.0, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromMultiQuoteLPs_SQsamebidoffer2T1Lp =====");
    }

    @Test //aggregated book gets merged
    public void testVWAPFromMultiQuoteLPs_SQsamebidoffer2T1LpMerge() throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testVWAPFromMultiQuoteLPs_SQsamebidoffer2T1LpMerge =====");
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1500,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.21, 1.21);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2121,0);
        set3TierRates(quote,QuoteC.SELL,1.2121,1.2121,0);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

//        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 1, 1.21, 1.21);
//        set3TierRates(quote,QuoteC.BUY,1.2121,0,0);
//        set3TierRates(quote,QuoteC.SELL,1.2121,0,0);
//        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
//
//        manager.handleRate(quote);
//        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiQuoteLPs_SQsamebidoffer2T1LpMerge ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2121, aggregate.getBidPrices()[0], 0.00001);
        assertNotEquals(1.2121, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(0, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(3000.00, aggregate.getBidQtys()[0], 0.01);
        assertNotEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2121, aggregate.getOfferPrices()[0], 0.00001);
        //The book is sorted at the offer side as well
        assertEquals(3000.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromMultiQuoteLPs_SQsamebidoffer2T1LpMerge =====");
    }

    @Test
    public void testVWAPFromMultiQuoteLPs_samebidoffer2T2Lps () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers
        System.out.println("===== START : testVWAPFromMultiQuoteLPs_samebidoffer2T2Lps =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.25, 1.26);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2125,1.2124,1.2123);
        set3TierRates(quote,QuoteC.SELL,1.2126,1.2127,1.2128);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        set3TierRates(quote,QuoteC.BUY,1.2122,1.2121,1.2120);
        set3TierRates(quote,QuoteC.SELL,1.2125,1.2126,1.2127);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiQuoteLPs_samebidoffer2T2Lps ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2125, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.21245, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.212433, aggregate.getBidPrices()[2], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2125, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.21255, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.212567, aggregate.getOfferPrices()[2], 0.00001);


        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(2000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(3000.00, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(3, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromMultiQuoteLPs_samebidoffer2T2Lps =====");
    }

    @Test
    public void testVWAPFromMT_zeroliquidity() throws Exception {
        System.out.println("===== START : testVWAPFromMT_zeroliquidity =====");
        ServerProvision serverProvision = doDefaultProvision();
        ((ServerProvisionImpl) serverProvision).setUseSuperLPStreamIndex(true);
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000,4000,5000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        LPProvision lp1Prov = null;
        for (LPProvision lpProv : fiProvision.getLPProvisions()) {
            if (lpProv.getShortName().equals("lp1")) {
                lp1Prov = lpProv;
                break;
            }
        }
        Assert.assertNotNull(lp1Prov);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager, 1001);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 1002);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMT_zeroliquidity ==="+aggregate.toString());
        System.out.println("===== aggregated book ==="+book1.toString());

        //assert For bid price
        assertEquals(1.21210, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.212033, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.21195, aggregate.getBidPrices()[2], 0.00001);
        assertEquals(1.2119, aggregate.getBidPrices()[3], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertNotEquals(1000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(3000, aggregate.getBidQtys()[1], 0.01);
        assertEquals(4000, aggregate.getBidQtys()[2], 0.01);
        assertEquals(5000, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.212200, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.212267, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.21235, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1.21244, aggregate.getOfferPrices()[3], 0.00001);

        //The book is sorted at the offer side as well
        assertNotEquals(1000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(5000, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());

        //LP1 gives 0 liquidity and bid quote gets dropped
        quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22, 0, 1000);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        //make sure LP1 is withdrawn
        aggregate = book1.aggregate();
        System.out.println("=======book1 when stream1 inactive========" +aggregate.toString());

        //make sure only LP2 is aggregated
        //assert For bid price
        assertEquals(1.21210, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.21205, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.211966, aggregate.getBidPrices()[2], 0.00001);
        assertEquals(1.2119, aggregate.getBidPrices()[3], 0.00001);
        assertEquals(1.21184, aggregate.getBidPrices()[4], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000, aggregate.getBidQtys()[2], 0.01);
        assertEquals(4000, aggregate.getBidQtys()[3], 0.01);
        assertEquals(5000, aggregate.getBidQtys()[4], 0.01);
        assertEquals(5, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.212200, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.212267, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.21235, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(1.21244, aggregate.getOfferPrices()[3], 0.00001);

        //The book is sorted at the offer side as well
        assertNotEquals(1000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(2000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(3000, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(4000, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(5000, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromMT_zeroliquidity =====");
    }

    @Test
    public void testVWAPFromMultiTierLPs_merge2T () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers
        System.out.println("===== START : testVWAPFromMultiTierLPs_merge2T =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 2, 1.25, 1.26);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2125,1.2124,0.0);
        set3TierRates(quote,QuoteC.SELL,1.2126,1.2127,0.0);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 2, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        set3TierRates(quote,QuoteC.BUY,1.2122,1.2121,0.0);
        set3TierRates(quote,QuoteC.SELL,1.2125,1.2126,0.0);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiTierLPs_merge2T ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2125, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.2124, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.212333, aggregate.getBidPrices()[2], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2125, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.2126, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(0.0, aggregate.getOfferPrices()[2], 0.00001);


        //The book is sorted at the offer side as well
        assertEquals(1000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(3000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(0.0, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromMultiTierLPs_merge2T =====");
    }

    @Test //PLT-3520, check after fix
    public void testVWAPFromMultiTierLPs_maxliquidity () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers
        System.out.println("===== START : testVWAPFromMultiTierLPs_maxliquidity =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{3000,6000,7000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.25, 1.26);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2125,1.2124,1.2123);
        set3TierRates(quote,QuoteC.SELL,1.2126,1.2127,1.2128);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        set3TierRates(quote,QuoteC.BUY,1.2122,1.2121,1.2120);
        set3TierRates(quote,QuoteC.SELL,1.2125,1.2126,1.2127);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiTierLPs_maxliquidity ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2123, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.21215, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(0.0, aggregate.getBidPrices()[2], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(3000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(6000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(0.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2126, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.21275, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(0.0, aggregate.getOfferPrices()[2], 0.00001);


        //The book is sorted at the offer side as well
        assertEquals(3000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(6000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(0.0, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromMultiTierLPs_maxliquidity =====");
    }

    /**
     * Four tiers in Vwap - check scenario
     * Rates are same since rounding happening after second tier(when precision is 4), but not getting merged
     * Rates are different with precision 6
     * MergingwithroundingQT.txt. Same scenario works fine manually, mdf merges the tiers and gives the vwap agg rates
     * PLT-3586
     * @throws Exception
     */
    @Test
    public void testVWAPFromMultiQuoteLPs_merge2T () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers
        System.out.println("===== START : testVWAPFromMultiQuoteLPs_merge2T =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1000,2000,3000,4000,5000,6000,7000,8000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(4);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 6, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tier 1
        int tieridx = quote.tierOffset(QuoteC.BUY, 0);
        quote.setPrice(tieridx,1.2122);
        //tier 2
        tieridx = quote.tierOffset(QuoteC.BUY, 1);
        quote.setPrice(tieridx, 1.2121);
        //tier 3
        tieridx = quote.tierOffset(QuoteC.BUY, 2);
        quote.setPrice(tieridx, 1.2121);
        tieridx = quote.tierOffset(QuoteC.BUY, 3);
        quote.setPrice(tieridx, 1.2121);
        tieridx = quote.tierOffset(QuoteC.BUY, 4);
        quote.setPrice(tieridx, 1.2121);
        tieridx = quote.tierOffset(QuoteC.BUY, 5);
        quote.setPrice(tieridx, 1.2121);

        //tier 1
        tieridx = quote.tierOffset(QuoteC.SELL, 0);
        quote.setPrice(tieridx,1.2125);
        //tier 2
        tieridx = quote.tierOffset(QuoteC.SELL, 1);
        quote.setPrice(tieridx, 1.2126);
        //tier 3
        tieridx = quote.tierOffset(QuoteC.SELL, 2);
        quote.setPrice(tieridx, 1.2126);
        tieridx = quote.tierOffset(QuoteC.SELL, 3);
        quote.setPrice(tieridx, 1.2126);
        tieridx = quote.tierOffset(QuoteC.SELL, 4);
        quote.setPrice(tieridx, 1.2126);
        tieridx = quote.tierOffset(QuoteC.SELL, 5);
        quote.setPrice(tieridx, 1.2126);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 6, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tier 1
        tieridx = quote.tierOffset(QuoteC.BUY, 0);
        quote.setPrice(tieridx,1.2122);
        //tier 2
        tieridx = quote.tierOffset(QuoteC.BUY, 1);
        quote.setPrice(tieridx, 1.2121);
        //tier 3
        tieridx = quote.tierOffset(QuoteC.BUY, 2);
        quote.setPrice(tieridx, 1.2121);
        tieridx = quote.tierOffset(QuoteC.BUY, 3);
        quote.setPrice(tieridx, 1.2121);
        tieridx = quote.tierOffset(QuoteC.BUY, 4);
        quote.setPrice(tieridx, 1.2121);
        tieridx = quote.tierOffset(QuoteC.BUY, 5);
        quote.setPrice(tieridx, 1.2121);
        //tier 1
        tieridx = quote.tierOffset(QuoteC.SELL, 0);
        quote.setPrice(tieridx,1.2125);
        //tier 2
        tieridx = quote.tierOffset(QuoteC.SELL, 1);
        quote.setPrice(tieridx, 1.2126);
        //tier 3
        tieridx = quote.tierOffset(QuoteC.SELL, 2);
        quote.setPrice(tieridx, 1.2126);
        tieridx = quote.tierOffset(QuoteC.SELL, 3);
        quote.setPrice(tieridx, 1.2126);
        tieridx = quote.tierOffset(QuoteC.SELL, 4);
        quote.setPrice(tieridx, 1.2126);
        tieridx = quote.tierOffset(QuoteC.SELL, 5);
        quote.setPrice(tieridx, 1.2126);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiQuoteLPs_merge2T ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2122, aggregate.getBidPrices()[0], 0.00001);
        assertEquals(1.2121, aggregate.getBidPrices()[1], 0.00001);
        //should merge all remaining tiers since values are same after rounding up
        assertEquals(0.0, aggregate.getBidPrices()[2], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(2000.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(8000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(0.0, aggregate.getBidQtys()[2], 0.01);
        assertEquals(2, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2125, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.2126, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(0.0, aggregate.getOfferPrices()[2], 0.00001);

        //The book is sorted at the offer side as well
        assertEquals(2000.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(8000.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(0.0, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(2, aggregate.getNumOffers());


        /**  Agg quote with precision 6
         * ===== aggregate quote in testVWAPFromMultiQuoteLPs_merge2T ===PriceBook [version=4, fiIndex=1001, ccyPairIndex=6029387, bookId=1,
         * bidPrices=[1.2122, 1.212166, 1.21215, 1.21214, 1.212133, 1.212128, 1.212125, 0.0, 0.0, 0.0],
         * bidQtys=[2000.0, 3000.0, 4000.0, 5000.0, 6000.0, 7000.0, 8000.0, 0.0, 0.0, 0.0],
         * bidNoLPs[0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
         * offerPrices=[1.2125, 1.212534, 1.21255, 1.21256, 1.212567, 1.212572, 1.212575, 0.0, 0.0, 0.0],
         * offerQtys=[2000.0, 3000.0, 4000.0, 5000.0, 6000.0, 7000.0, 8000.0, 0.0, 0.0, 0.0],
         * offerNoLPs[0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
         * timeEffective=1553063427709, numBids=7, numOffers=7, maxDepth=10, valueDate=17500, qid=0, qfime=1553063427692, flags=32]
         */

        System.out.println("===== END : testVWAPFromMultiQuoteLPs_merge2T =====");
    }


    /**
     * PLT-3585 Only positive tier limits are allowed
     * 0M or negative values cannot be entered as vwap limit
     * Validate the exception raised in this case
      */
    @Test
    public void testVWAPPositiveTierLimit () throws Exception {
        System.out.println("===== START : testVWAPPositiveTierLimit =====");
        //IllegalArgumentException exception = new IllegalArgumentException();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;

        try {
            impl.setAggregationTiers(new double[]{1000,-1,0});
            System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
            impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
            RateDistributionManager manager = getNoHopManager(serverProvision);
            manager.createRateBooks(fiProvision);
        }catch (IllegalArgumentException exception) {
            System.out.println("Inside catch block 1");
            String exceptionmessage = exception.getMessage();
            assertEquals("Message is", "tier amount can't be zero or negetive. tiers=[1000.0, -1.0, 0.0]", exceptionmessage);
        }

        try {
            impl.setAggregationTiers(new double[]{0,0,0});
            System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
            impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
            RateDistributionManager manager = getNoHopManager(serverProvision);
            manager.createRateBooks(fiProvision);
        }catch (IllegalArgumentException exception) {
            System.out.println("Inside catch block 2");
            String exceptionmessage = exception.getMessage();
            assertEquals("Message is", "tier amount can't be zero or negetive. tiers=[0.0, 0.0, 0.0]", exceptionmessage);
        }
    }


    /**
     * PLT-3585 Scenario when no tiers are specified, but VWap is selected in admin
     * Change TC after fix, as of now TC passes but it should throw exception - check
     */
    @Test
    public void testVWAPNoTiers () throws Exception {
        System.out.println("===== START : testVWAPNoTiers =====");
        //IllegalArgumentException exception = new IllegalArgumentException();
        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;

        try {
            impl.setAggregationTiers(new double[]{});
            System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
            impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);
            RateDistributionManager manager = getNoHopManager(serverProvision);
            manager.createRateBooks(fiProvision);
        }catch (IllegalArgumentException exception) {
            System.out.println("Inside catch block 1");
            String exceptionmessage = exception.getMessage();
            assertEquals("Message is", "tier amount can't be zero or negetive. tiers=[1000.0, -1.0, 0.0]", exceptionmessage);
        }
    }

    @Test
    public void testVWAPFromMultiTierLPs_new () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers
        // not working as expected
        System.out.println("===== START : testVWAPFromMultiTierLPs_new =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{1500,2500,3500});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(6);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //tiers
        //set3TierRates(quote,QuoteC.BUY,1.2125,1.2124,0.0);
        //set3TierRates(quote,QuoteC.SELL,1.2126,1.2127,0.0);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        //set3TierRates(quote,QuoteC.BUY,1.2122,1.2121,0.0);
        //set3TierRates(quote,QuoteC.SELL,1.2125,1.2126,0.0);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiTierLPs_merge2T ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2166, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(1.2057, aggregate.getBidPrices()[2], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(3500.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2167, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(1.21, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(1.2586, aggregate.getOfferPrices()[2], 0.00001);


        //The book is sorted at the offer side as well
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.00001);
        assertEquals(2500.00, aggregate.getOfferQtys()[1], 0.00001);
        assertEquals(3500.00, aggregate.getOfferQtys()[2], 0.00001);
        assertEquals(2, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromMultiTierLPs_new =====");
    }

    @Test       //scenario - Tier values are too high and LP quotes do not have that much liquidity, then quote gets dropped in mdf PLT-3520
    public void testVWAPFromMultiQuoteLPs_lowerLiquidity () throws Exception {
        // Ignores the quote if LP quote limits doesnt match with the configured tiers

        System.out.println("===== START : testVWAPFromMultiQuoteLPs_lowerLiquidity =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        Integer commonCcyPairIdx = serverProvision.getCcyPairIndex(EUR_USD).get();
        CurrencyPairProvision cpp = serverProvision.getCcyPairProvision(EUR_USD).get();
        cpp.setSpotPrecision(5);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationTiers(new double[]{13000,15000,18000});
        System.out.println("tiers =" + Arrays.toString(impl.getAggregationTiers()));
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        RateDistributionManager manager = getNoHopManager(serverProvision);
        manager.createRateBooks(fiProvision);

        Assert.assertEquals(15, manager.rateChannels.size());
        Set<Map.Entry<Long, RateChannel>> entrySet = manager.rateChannels.entrySet();

        for (Map.Entry<Long, RateChannel> entry : entrySet) {
            List<RateBook> rateBooks = entry.getValue().getRateBooks();
            Assert.assertEquals(1, rateBooks.size());
            for (RateBook rateBook : rateBooks) {
                Assert.assertTrue(rateBook.isActive());
                Assert.assertEquals(fiProvision.getIndex(),
                        rateBook.getFIIndex());
            }
        }

        RateBook book1 = getBook(serverProvision, fiProvision, manager);
        RateBook book2 = getBook(serverProvision, fiProvision, manager, 2);
        Assert.assertNotNull(book1);
        // Assertion to verify same book across Lp's
        Assert.assertSame("Books not same across Lp's", book1, book2);

        // Provision another FI
        service.provision(TESTFI2);

        for (RateBook rBook : manager.rateBooks.values()) {
            Assert.assertTrue(rBook.isActive());
        }

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.22);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        //tiers
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2120,1.2119);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2123,1.2124);

        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.21, 1.24);
        set3TierRates(quote,QuoteC.BUY,1.2121,1.2119,1.2118);
        set3TierRates(quote,QuoteC.SELL,1.2122,1.2125,1.2127);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);

        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = book1.aggregate();
        System.out.println("===== aggregate quote in testVWAPFromMultiQuoteLPs_noliquidity ==="+aggregate.toString());
        System.out.println("===== aggregated book ==="+book1.toString());

        //assert For bid price
        assertEquals(1.21192, aggregate.getBidPrices()[0], 0.00001);
        assertNotEquals(1.21205, aggregate.getBidPrices()[1], 0.00001);
        assertEquals(0.0, aggregate.getBidPrices()[1], 0.00001);

        //testing the aggregation for merging the quotes form provider 1 & 2
        assertEquals(12000, aggregate.getBidQtys()[0], 0.01);
        assertEquals(0.0, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.21245, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(12000, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(0.0, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1, aggregate.getNumOffers());

        System.out.println("===== END : testVWAPFromMultiQuoteLPs_noliquidity =====");
    }

    @Test
    public void TC1_customAgg_MTproviders_requestedSize () throws Exception {
        // ideally requestedSize is ignored in MT provider for non FBA
// always ensure sleep is given between two quote publisings.
// This will ensure the logic of considering older quote incase of tie
        System.out.println("===== START : TC1_customAgg_MTproviders_requestedSize =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        //double [] tiers =  [1000,2000,5000];
        RateBook rateBook = manager.createRateBook("10001", fiProvision, cpIndex, new double[]{1500,2500,3500}, null, 2000, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        sleep(1);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());
//1.21 2000 1.26
// 1.20 2000, 1.25

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        // actual output
        // bidPrices=[1.21, 1.2028, 0.0, ], bidQtys=[2500.0, 3500.0, 0.0],
        // offerPrices=[1.25, 1.2572, 0.0, ], offerQtys=[2500.0, 3500.0, 0.0]

        //assert For bid price
        assertEquals(1.2166, aggregate.getBidPrices()[0], 0.001);
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.21, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.2057, aggregate.getBidPrices()[2], 0.001);
        assertEquals(3500.00, aggregate.getBidQtys()[2], 0.01);

        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.01);
        assertEquals(2500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.2572, aggregate.getOfferPrices()[1], 0.01);
        assertEquals(3500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(2, aggregate.getNumOffers());


    }

    @Test
    public void TC2_customAgg_MTMQproviders_requestedSize () throws Exception {
// when there is a tie in rates, oldest quote will be considered.
// For example, if there is a tie with T1 of MT lp and MT lp is the oldest quote, T1 of MT will be considered.
// Again if there is a tie with 2nd tier of MT provider, it will drop the T1 and T2 will be considered.
        System.out.println("===== START : TC2_customAgg_MTMQproviders_requestedSize =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1002", fiProvision, cpIndex,  new double[]{1500,2500,3500,5500,7500,9500}, null, 2000, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_TIER);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());
        sleep(1);

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2166, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.214, aggregate.getBidPrices()[1], 0.001);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.2128, aggregate.getBidPrices()[2], 0.001);
        assertEquals(3500.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(1.2072, aggregate.getBidPrices()[3], 0.0001);
        assertEquals(5500.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(1.2053, aggregate.getBidPrices()[4], 0.001);
        assertEquals(7500.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(1.2011, aggregate.getBidPrices()[5], 0.001);
        assertEquals(9000.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.25, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(2500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.2572, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(3500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.2582, aggregate.getOfferPrices()[2], 0.0001);
        assertEquals(5500.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(1.2607, aggregate.getOfferPrices()[3], 0.0001);
        assertEquals(7500.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(1.2623, aggregate.getOfferPrices()[4], 0.0001);
        assertEquals(9000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(5, aggregate.getNumOffers());

    }

    @Test
    public void TC3_customAgg_MQproviders_requestedSize () throws Exception {
        // requestSize is ignored incase of MQ providers
        System.out.println("===== START : TC3_customAgg_MQproviders_requestedSize =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500,2500,3500}, null, 2000, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2166, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.214, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.2128, aggregate.getBidPrices()[2], 0.0001);
        assertEquals(3500.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2434, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.246, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.2472, aggregate.getOfferPrices()[2], 0.0001);
        assertEquals(3500.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
    }

    @Test
    public void TC6_customAgg_multipleProviders () throws Exception {

        System.out.println("===== START : TC6_customAgg_multipleProviders =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500,2500,3500}, providers, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2166, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.214, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.2128, aggregate.getBidPrices()[2], 0.0001);
        assertEquals(3500.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2434, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.246, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.2472, aggregate.getOfferPrices()[2], 0.0001);
        assertEquals(3500.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());

    }

    @Test
    public void TC7_customAgg_selectedProviders () throws Exception {
// not working, provider tag is not getting considered
        System.out.println("===== START : TC7_customAgg_selectedProviders =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        //providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500,2500,3500}, providers, 0, false);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2066, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.204, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.2015, aggregate.getBidPrices()[2], 0.001);
        assertEquals(3500.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2434, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.246, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.2486, aggregate.getOfferPrices()[2], 0.01);
        assertEquals(3500.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());

    }

    @Test
    public void TC8_customAgg_termCcy () throws Exception {

        System.out.println("===== START : TC8_customAgg_termCcy =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex,  new double[]{1500,2500,3500,4500}, providers, 0, true);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2181, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.2148, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.2134, aggregate.getBidPrices()[2], 0.0001);
        assertEquals(3500.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(1.2127, aggregate.getBidPrices()[3], 0.0001);
        assertEquals(4500.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2418, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.2451, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.2465, aggregate.getOfferPrices()[2], 0.0001);
        assertEquals(3500.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(1.2473, aggregate.getOfferPrices()[3], 0.0001);
        assertEquals(4500.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(4, aggregate.getNumOffers());
    }

    @Test
    public void TC9_customAgg_termCcy_more_tiers () throws Exception {

        System.out.println("===== START : TC9_customAgg_termCcy_more_tiers =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex,  new double[]{2500,5000,7500,10000,15000,18000}, providers, 0, true);

        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2148, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(2500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.2121, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(5000.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.208, aggregate.getBidPrices()[2], 0.0001);
        assertEquals(7500.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(1.206, aggregate.getBidPrices()[3], 0.0001);
        assertEquals(10000.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(1.2017, aggregate.getBidPrices()[4], 0.0001);
        assertEquals(14420.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(5, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2451, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(2500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.2476, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(5000.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.2517, aggregate.getOfferPrices()[2], 0.0001);
        assertEquals(7500.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(1.2538, aggregate.getOfferPrices()[3], 0.0001);
        assertEquals(10000.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(1.2584, aggregate.getOfferPrices()[4], 0.0001);
        assertEquals(15000.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(1.2584, aggregate.getOfferPrices()[5], 0.0001);
        assertEquals(15100.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(6, aggregate.getNumOffers());
    }

    @Test
    public void TC10_customAgg_oneSidedBidRates () throws Exception {

        System.out.println("===== START : TC10_customAgg_oneSidedRates =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500,2500,3500}, providers, 0, false);

        //QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 0);
        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        //quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 0);
        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, true);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2166, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.214, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.2128, aggregate.getBidPrices()[2], 0.0001);
        assertEquals(3500.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(0.00, aggregate.getOfferPrices()[0], 0.00001);
        assertEquals(0.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(0.00, aggregate.getOfferPrices()[1], 0.00001);
        assertEquals(0.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(0.00, aggregate.getOfferPrices()[2], 0.00001);
        assertEquals(0.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(0, aggregate.getNumOffers());

    }

    @Test
    public void TC11_customAgg_oneSidedOfferRates () throws Exception {
//failing, different from 2 sided op for the same input quotes
        System.out.println("===== START : TC11_customAgg_oneSidedOfferRates =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        List<String> providers = new ArrayList<String>();
        providers.add(LP12);
        providers.add(LP22);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500,2500,3500}, providers, 0, false);

        //QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 0, 1.24);
        QuoteC quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.24, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getOneSidedQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.25, false);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(0.00, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(0.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(0.00, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(0.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(0.00, aggregate.getBidPrices()[2], 0.0001);
        assertEquals(0.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(0, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2434, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.246, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.2472, aggregate.getOfferPrices()[2], 0.0001);
        assertEquals(3500.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());

    }

    @Test
    public void TC12_customAgg_MQproviders_tiersHigherthanLiq () throws Exception {
        // requestSize is ignored incase of MQ providers
        System.out.println("===== START : TC12_customAgg_MQproviders_doubleAmts =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        //RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500.5,2500.5,3500.5,4500.5,5500.5, 6500.5}, null, 2000, false);
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500,2500,3500,4500,5500, 6500,7500,9500,13000}, null, 2000, false);
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2166, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.214, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.2128, aggregate.getBidPrices()[2], 0.0001);
        assertEquals(3500.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(1.2111, aggregate.getBidPrices()[3], 0.0001);
        assertEquals(4500.00, aggregate.getBidQtys()[3], 0.01);
        assertEquals(1.209, aggregate.getBidPrices()[4], 0.0001);
        assertEquals(5500.00, aggregate.getBidQtys()[4], 0.01);
        assertEquals(1.2076, aggregate.getBidPrices()[5], 0.0001);
        assertEquals(6500.00, aggregate.getBidQtys()[5], 0.01);
        assertEquals(1.2066, aggregate.getBidPrices()[6], 0.0001);
        assertEquals(7500.00, aggregate.getBidQtys()[6], 0.01);
        assertEquals(1.2047, aggregate.getBidPrices()[7], 0.0001);
        assertEquals(9500.00, aggregate.getBidQtys()[7], 0.01);
        assertEquals(1.2016, aggregate.getBidPrices()[8], 0.0001);
        assertEquals(12000.00, aggregate.getBidQtys()[8], 0.01);
        assertEquals(9, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2434, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(1500.00, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.246, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(2500.00, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.2472, aggregate.getOfferPrices()[2], 0.0001);
        assertEquals(3500.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(1.2489, aggregate.getOfferPrices()[3], 0.0001);
        assertEquals(4500.00, aggregate.getOfferQtys()[3], 0.01);
        assertEquals(1.251, aggregate.getOfferPrices()[4], 0.0001);
        assertEquals(5500.00, aggregate.getOfferQtys()[4], 0.01);
        assertEquals(1.2524, aggregate.getOfferPrices()[5], 0.0001);
        assertEquals(6500.00, aggregate.getOfferQtys()[5], 0.01);
        assertEquals(1.2534, aggregate.getOfferPrices()[6], 0.0001);
        assertEquals(7500.00, aggregate.getOfferQtys()[6], 0.01);
        assertEquals(1.2553, aggregate.getOfferPrices()[7], 0.0001);
        assertEquals(9500.00, aggregate.getOfferQtys()[7], 0.01);
        assertEquals(1.2584, aggregate.getOfferPrices()[8], 0.0001);
        assertEquals(12000.00, aggregate.getOfferQtys()[8], 0.01);
        assertEquals(9, aggregate.getNumOffers());
    }

    @Test
    public void TC13_customAgg_MQproviders_doubleAmounts () throws Exception {
        // requestSize is ignored incase of MQ providers
        System.out.println("===== START : TC13_customAgg_MQproviders_doubleAmounts =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        //RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500.5,2500.5,3500.5,4500.5,5500.5, 6500.5}, null, 2000, false);
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500.5,5500.99,13000.99}, null, 2000, false);
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2166, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1500.5, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.209, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(5500.99, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.2016, aggregate.getBidPrices()[2], 0.0001);
        assertEquals(12000.00, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2434, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(1500.5, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.251, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(5500.99, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.2584, aggregate.getOfferPrices()[2], 0.0001);
        assertEquals(12000.00, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
    }

    @Test
    public void TC14_customAgg_MQproviders_termCcy_doubleAmounts () throws Exception {
        // requestSize is ignored incase of MQ providers
        System.out.println("===== START : TC14_customAgg_MQproviders_termCcy_doubleAmounts =====");

        ServerProvision serverProvision = doDefaultProvision();
        service.provision(TESTFI1);

        FIProvision fiProvision = serverProvision.getFIProvision(1001);
        FIProvisionImpl impl = (FIProvisionImpl) fiProvision;
        impl.setAggregationType(MDFAggregationType.WEIGHTED_AVERAGE);

        CustomRateDistributionManager manager = getCustomManager(serverProvision);
        int cpIndex = serverProvision.getCcyPairIndex(EUR_USD).get();
        //CurrencyPairProvision cpProvision = ((FIProvisionImpl) fiProvision).getCcyProvision(EUR_USD).get();
        //RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500.5,2500.5,3500.5,4500.5,5500.5, 6500.5}, null, 2000, false);
        RateBook rateBook = manager.createRateBook("1001", fiProvision, cpIndex, new double[]{1500.5,5500.99,13000.99}, null, 2000, true);
        QuoteC quote = getQuoteC(serverProvision, EUR_USD, LP12, fiProvision, 3, 1.21, 1.24);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP1 quote==="+quote.toString());

        quote = getQuoteC(serverProvision, EUR_USD, LP22, fiProvision, 3, 1.22, 1.25);
        quote.setQuoteType(ProvisionedQuoteC.MULTI_QUOTE);
        manager.handleRate(quote);
        System.out.println("=====LP2 quote==="+quote.toString());

        PriceBook aggregate = rateBook.aggregate();
        System.out.println("=====first aggregated quote ==="+aggregate.toString());

        //assert For bid price
        assertEquals(1.2181, aggregate.getBidPrices()[0], 0.0001);
        assertEquals(1500.5, aggregate.getBidQtys()[0], 0.01);
        assertEquals(1.211, aggregate.getBidPrices()[1], 0.0001);
        assertEquals(5500.99, aggregate.getBidQtys()[1], 0.01);
        assertEquals(1.203, aggregate.getBidPrices()[2], 0.0001);
        assertEquals(13000.99, aggregate.getBidQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumBids());

        //assert For offer price
        assertEquals(1.2418, aggregate.getOfferPrices()[0], 0.0001);
        assertEquals(1500.5, aggregate.getOfferQtys()[0], 0.01);
        assertEquals(1.2487, aggregate.getOfferPrices()[1], 0.0001);
        assertEquals(5500.99, aggregate.getOfferQtys()[1], 0.01);
        assertEquals(1.2566, aggregate.getOfferPrices()[2], 0.0001);
        assertEquals(13000.99, aggregate.getOfferQtys()[2], 0.01);
        assertEquals(3, aggregate.getNumOffers());
    }


}
