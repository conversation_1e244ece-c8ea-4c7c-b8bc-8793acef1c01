import java.math.BigDecimal;

public class TestBigDecimal {
    public static void main(String[] args) {
        BigDecimal price  = BigDecimal.valueOf(300.50);
        BigDecimal price1  = BigDecimal.valueOf(300.50);
        BigDecimal price2  = BigDecimal.valueOf(300.05);
        BigDecimal fractionalPart  = price.remainder(BigDecimal.ONE);
        System.out.println(fractionalPart.movePointRight(price1.scale()).abs().intValue());  // Output is 5
        System.out.println(fractionalPart.movePointRight(price2.scale()).abs().intValue());  // Output is 5
    }
}
