package com.integral.mdf.subscriptions;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.MessageGateway;
import com.integral.mdf.Util;
import com.integral.mdf.cache.subscription.SubscriptionCache;
import com.integral.mdf.cache.subscription.SubscriptionCacheManager;
import com.integral.mdf.cache.subscription.SubscriptionCacheManagerC;
import com.integral.mdf.data.FIProvision;
import com.integral.mdf.data.FIProvisionImpl;
import com.integral.mdf.data.ServerProvision;
import com.integral.mdf.data.SubscriptionErrorCodes;
import com.integral.mdf.proivision.MDFProvisionDataService;
import com.integral.mdf.rate.CustomRateDistributionManager;
import com.integral.mdf.rate.RateBook;
import com.integral.mdf.rate.RateDistributionManager;
import com.integral.notifications.Error;
import com.integral.notifications.Status;
import com.integral.notifications.mdf.SubscriptionRequest;
import com.integral.notifications.mdf.SubscriptionResponse;
import com.integral.notifications.mdf.SubscriptionType;
import com.integral.provision.CurrencyPairProvision;
import com.integral.provision.MDFAggregationType;
import org.jctools.maps.NonBlockingHashMapLong;

import java.util.*;

import static com.google.common.base.Strings.isNullOrEmpty;
import static com.integral.mdf.data.SubscriptionErrorCodes.*;
import static java.util.Objects.isNull;
import static com.integral.mdf.Util.*;

public class SubscriptionManager {

    final ServerProvision serverProvision;
    MessageGateway gateway;
    final RateDistributionManager rdm;
    final MDFProvisionDataService dataService;

    Log log = LogFactory.getLog(this.getClass());

    //Key   = ccyp + org + arrgtype -> Tier1 = vs1,vs2,vs3
    //                              -> Tier2 = vs2,vs3
    //                              -> Tier3 = vs3,vs1
    //                              -> Tier4 = vs1
    //Value = list of subscribersRDS
    NonBlockingHashMapLong<NonBlockingHashMapLong<Set<String>>> subscriptions = new NonBlockingHashMapLong();

    public SubscriptionManager(ServerProvision serverProvision,
                               RateDistributionManager rdm, MDFProvisionDataService dataService){
        this.serverProvision = serverProvision;
        this.rdm = rdm;
        this.dataService = dataService;
    }

    public void setGateway(MessageGateway gateway){
        this.gateway = gateway;
    }

    public void handleSubscriptionRequest(SubscriptionRequest request, String sentBy, String cachingKey){
        switch (request.getType()){
            case SUBSCRIBE:
                subscribe(request,sentBy, cachingKey);
                break;
            case UNSUBSCRIBE:
                unsubscribe(request,sentBy, cachingKey, true);
                break;
        }
    }

    protected void subscribe(SubscriptionRequest request, String sentBy, String cachingKey){

        if(log.isInfoEnabled()){
            log.info("subscribe revc msg="+stringify(request) + ", sentBy="+sentBy);
        }

        if(!isValidRequest(request)) return;

        FIProvision fi=null;
        try{
            fi = getFIProvision(request);
        }catch(Exception e){
            log.warn("failed to retrieve FI's configuration.");
        }

        if(fi==null){
            sendFailureResponse(request,SubscriptionErrorCodes.UnsupportedOrganization);
            return;
        }

        //cache only valid requests.  All validation should be done before this call.
        saveRequestInCache(cachingKey, request, sentBy);

        try {
            //already validated
            CurrencyPairProvision cp = getCurrencyPairProvision(request);
            String bookKey = getStringKey(fi.getName(),cp.getShortName(),request.getAggregationType());
            int ccypIdx = serverProvision.getCcyPairIndex(cp.getShortName()).get();
            int vIdx = getVarCurrencyIndex(ccypIdx);
            int bIdx = getBaseCurrencyIndex(ccypIdx);

            NonBlockingHashMapLong<Set<String>> subscribedTiers  = getOrCreateSubscribedTiers(request,fi,bIdx,vIdx,bookKey);
            boolean isTermCcyAgg = request.getDealtCurrencyIndex() == vIdx;
            //create ratebook
            boolean custom = request.isCustomAggregation();
            RateBook ratebook;
            if(custom){
                if(!(rdm instanceof CustomRateDistributionManager)){
                    throw new IllegalArgumentException("custom aggregation not supported for " + rdm.getClass().getName());
                }
                double[] tiers = request.getTierValues();
                ratebook = ((CustomRateDistributionManager)rdm).createRateBook(request.getId(), fi, ccypIdx, tiers, request.getProviders(), request.getRequestedSize(), isTermCcyAgg);
            }else {
                ratebook = rdm.createRateBook(fi, ccypIdx);
            }

            if(!custom){
                switch (request.getAggregationType()){
                    case BEST_PRICE:
                    case FULL_BOOK:
                    case RAW_BOOK:
                    case RAW_DIRECT:
                        addTier(subscribedTiers,0,sentBy);
                        break;
                    case MULTI_TIER_FOK:
                    case WEIGHTED_AVERAGE:
                    case FA_MULTI_TIER:
                        for (long tier:request.getTiers()) {
                            addTier(subscribedTiers,tier,sentBy);
                            //update pricebook with tier, this operation is harmless since pricebook ignores duplicates.
                            //removals should be guarded.
                            ratebook.getPricebook().addTier(tier);
                        }
                        break;
                }
            }

            sendSuccessResponse(request,"Subscribe successfully at " + serverProvision.getVirtralServer());
        }catch (Exception e){
            log.error("failed to subscrobe r=" + stringify(request),e);
        }
    }

    protected void addTier(NonBlockingHashMapLong<Set<String>> subscribers,long tier,String sentBy){
        Set<String> servers = subscribers.get(tier); //there are no tiers for these subscriptions
        if(servers==null){
            Set<String> newServers = new HashSet<>(5);
            servers = subscribers.putIfAbsent(tier,newServers);
            if(servers==null)
                servers=newServers;
        }
        servers.add(sentBy);
    }

    protected void unsubscribe(SubscriptionRequest request, String sentBy, String cachingKey, boolean removeCache){
        if(!isValidRequest(request)) return;

        if (removeCache) {
            removeRequestFromCache(cachingKey, request, sentBy);
        }

        FIProvision fi=null;
        try{
            fi = getFIProvision(request);
        }catch(Exception e){
            log.warn("failed to retrieve FI's configuration.");
        }

        if(fi==null){
            sendFailureResponse(request,SubscriptionErrorCodes.UnsupportedOrganization);
            return;
        }

        try {
            CurrencyPairProvision cp = getCurrencyPairProvision(request);

            String bookKey = getStringKey(fi.getName(),cp.getShortName(),request.getAggregationType());

            int ccypIdx = serverProvision.getCcyPairIndex(cp.getShortName()).get();
            int vIdx = getVarCurrencyIndex(ccypIdx);
            int bIdx = getBaseCurrencyIndex(ccypIdx);

            NonBlockingHashMapLong<Set<String>> subscribedTiers  = getOrCreateSubscribedTiers(request,fi,bIdx,vIdx,bookKey);
            boolean custom = request.isCustomAggregation();
            Optional<RateBook> rateBook;
            if(custom){
                if(!(rdm instanceof CustomRateDistributionManager)){
                    throw new IllegalArgumentException("custom aggregation not supported for " + rdm.getClass().getName());
                }
                rateBook = ((CustomRateDistributionManager)rdm).getRateBook(Long.parseLong(request.getId()));
            }else {
                rateBook = rdm.getRateBook(fi.getIndex(),bIdx,vIdx,request.getAggregationType().getIndex());
            }

            if(rateBook.isPresent()) {
                if(!custom){
                    switch (request.getAggregationType()){
                        case BEST_PRICE:
                        case FULL_BOOK:
                        case RAW_BOOK:
                        case RAW_DIRECT:
                            removeTier(subscribedTiers,0, sentBy, bookKey,rateBook.get());
                            break;
                        case MULTI_TIER_FOK:
                        case WEIGHTED_AVERAGE:
                        case FA_MULTI_TIER:
                            for (long tier:request.getTiers()) {
                                removeTier(subscribedTiers,tier, sentBy, bookKey,rateBook.get());
                            }
                            break;
                    }
                }

                if(subscribedTiers.isEmpty()){
                    boolean result;
                    if(custom){
                        result = ((CustomRateDistributionManager)rdm).deactivateRateBook(Long.parseLong(request.getId()));
                    }else {
                        result = rdm.deactivateRateBook(fi, ccypIdx, request.getAggregationType());
                    }
                    if(!result){
                        log.info("No more subscribers. failed to deactivate book="+bookKey);
                    }
                }
                sendSuccessResponse(request, "Unsubscribe successfully at " + serverProvision.getVirtralServer());

            }else{
                log.info("no-op unsubscription request. rate book not found." + getStringKey(fi.getName(),
                        cp.getShortName(),request.getAggregationType()));
            }
        }catch (Exception e){
            log.error("failed to unsubscrobe r=" + request,e);
        }
    }

    protected boolean removeTier(NonBlockingHashMapLong<Set<String>> subscribers,
                                 long tier, String sentBy,
                                 String bookKey, RateBook rateBook) {
        Set<String> servers = subscribers.get(tier);
        if (servers != null) {
            servers.remove(sentBy);
            log.info("removed subscriber. book=" + bookKey + ", t=" + tier + ", sub=" + sentBy);
            if (servers.isEmpty()) {
                subscribers.remove(tier);
                rateBook.getPricebook().removeTier(tier);
                log.info("removed tier. no more subscribers.book=" + bookKey + ", t=" + tier);
            }
        }
        return true;
    }

    /**
     *
     * @param request
     * @return is valid or not
     */
    protected boolean isValidRequest(SubscriptionRequest request){
        if(isNullOrEmpty(request.getId())){
            sendFailureResponse(request,InvalidRequestId);
            return false;
        }
        if(request.getOrgIndex()<=0){
            sendFailureResponse(request,OrganizationRequired);
            return false;
        }
        if(request.getCurrencyPairIndex()<=0){
            sendFailureResponse(request,CurrencyPairRequired);
            return false;
        }else{
            Optional<String> ccyp = serverProvision.getCcyPairName(request.getCurrencyPairIndex());
            if(!(ccyp.isPresent() && serverProvision.getCcyPairProvision(ccyp.get()).isPresent())){
                sendFailureResponse(request,InvalidCurrencyPair);
                return false;
            }
        }
        if(isNull(request.getAggregationType())){
            sendFailureResponse(request,AggregationTypeRequired);
            return false;
        }
        if(request.isCustomAggregation()){
            try{
                Long.parseLong(request.getId());
            }catch (NumberFormatException e){
                sendFailureResponse(request,InvalidRequestId);
                return false;
            }
            MDFAggregationType aggType = request.getAggregationType();
            if(request.getType() == SubscriptionType.SUBSCRIBE){
                if(aggType == MDFAggregationType.WEIGHTED_AVERAGE ||
                        aggType == MDFAggregationType.MULTI_TIER_FOK ||
                        aggType == MDFAggregationType.FA_MULTI_TIER){
                    if(request.getTierValues() == null || request.getTierValues().length == 0){
                        sendFailureResponse(request,TierValuesRequired);
                        return false;
                    }
                }
            }
        }
        return true;
    }

    protected void sendSuccessResponse(SubscriptionRequest request, String message){
        Optional<String> uigName = Optional.ofNullable(request.getSentBy());
        sendSuccessResponse(request.getId(),message,uigName);
    }

    protected void sendFailureResponse(SubscriptionRequest request, SubscriptionErrorCodes error){
        Optional<String> uigName = Optional.ofNullable(request.getSentBy());
        sendFailureResponse(request.getId(),error.getCode(),error.getDescription(),uigName);
    }

    protected void sendFailureResponse(String id, int errorCode, String message, Optional<String> uigName){
        Error error = new Error();
        error.setCode(errorCode);
        error.setMessage(message);
        SubscriptionResponse response = new SubscriptionResponse();
        response.setRequestId(id);
        response.setStatus(Status.FAILURE);
        response.setError(error);
        log.info("subscription failed. res="+stringify(response)+", uig="+uigName);
        this.gateway.sendResponse(response,uigName);
    }

    protected void sendSuccessResponse(String id, String msg, Optional<String> uigName){
        SubscriptionResponse response = new SubscriptionResponse();
        response.setRequestId(id);
        response.setStatus(Status.SUCCESS);
        response.setMessage(msg);
        log.info("request processed successfully. res="+stringify(response)+", uig="+uigName);
        this.gateway.sendResponse(response,uigName);
    }

    //assumes pre-validated request
    protected String getStringKey(String orgName, String ccyp, MDFAggregationType atype){
        return new StringJoiner("-").add(orgName)
                .add(ccyp)
                .add(String.valueOf(Util.getStringKey(atype))).toString();
    }

    protected FIProvision getFIProvision(SubscriptionRequest request) throws Exception {
        FIProvision fi = serverProvision.getFIProvision(request.getOrgIndex());
        if (fi==null) {
            //try to load
            dataService.provision(request.getOrgIndex());
            fi = serverProvision.getFIProvision(request.getOrgIndex());
        }
        ((FIProvisionImpl)fi).setAggregationType(request.getAggregationType());
        return fi;
    }

    protected CurrencyPairProvision getCurrencyPairProvision(SubscriptionRequest request){
        //already validated
        Optional<String> ccyp = serverProvision.getCcyPairName(request.getCurrencyPairIndex());
        return serverProvision.getCcyPairProvision(ccyp.get()).get();
    }

    protected NonBlockingHashMapLong<Set<String>> getOrCreateSubscribedTiers(SubscriptionRequest request, FIProvision fi,
                                                                             int bIdx , int vIdx, String logKey ){

        long key = getRateBookKey(request.getOrgIndex(),bIdx,vIdx,request.getAggregationType().getIndex());

        //map of servers subscribed to this key
        NonBlockingHashMapLong<Set<String>> subscribers = subscriptions.get(key);
        if(subscribers==null){
            NonBlockingHashMapLong<Set<String>> newSubscribers = new NonBlockingHashMapLong<>();
            subscribers = subscriptions.putIfAbsent(key,newSubscribers);
            if(subscribers==null) subscribers = newSubscribers;
            log.info("initialized subscribers map. key="+logKey);
        }
        return subscribers;
    }

    //Assumes valid request...mainly for testcases
    protected Map<Long,Set<String>> getSubscribedTiers(int orgIdx, int bIdx , int vIdx, MDFAggregationType at ) {
        long key = getRateBookKey(orgIdx, bIdx, vIdx, at.getIndex());
        //map of servers subscribed to this key
        NonBlockingHashMapLong<Set<String>> map = subscriptions.get(key);
        return map!=null?Collections.unmodifiableMap(map):null;
    }

    public void subscribeCachedRequest(String cachingKey) {
        SubscriptionCacheManager scm = SubscriptionCacheManagerC.getInstance();
        if (scm != null) {
            List<SubscriptionCache> subscriptionCaches = scm.getSubscriptions(cachingKey);
            if (subscriptionCaches != null) {
                log.infoAsFormat("Subscribing cached request : %s", subscriptionCaches);
                subscriptionCaches.forEach(sc -> sc.getAllSentBy().forEach(sb -> subscribe(sc, sb, cachingKey)));
            }
        }
    }

    public void unsubscribeCachedRequest(String cachingKey) {
        SubscriptionCacheManager scm = SubscriptionCacheManagerC.getInstance();
        if (scm != null) {
            List<SubscriptionCache> subscriptionCaches = scm.getSubscriptions(cachingKey);
            if (subscriptionCaches != null) {
                log.infoAsFormat("Unsubscribing cached request: %s", subscriptionCaches);
                subscriptionCaches.forEach(sc -> sc.getAllSentBy().forEach(sb -> unsubscribe(sc, sb, cachingKey, false)));
            }
        }
    }

    private void saveRequestInCache(String cachingKey, SubscriptionRequest request, String sentBy) {
        SubscriptionCacheManager scm = SubscriptionCacheManagerC.getInstance();
        if (scm != null) {
            scm.addSubscription(cachingKey, request, sentBy);
        }
    }

    private void removeRequestFromCache(String cachingKey, SubscriptionRequest request, String sentBy) {
        SubscriptionCacheManager scm = SubscriptionCacheManagerC.getInstance();
        if (scm != null) {
            scm.removeSubscription(cachingKey, request, sentBy);
        }
    }

}
