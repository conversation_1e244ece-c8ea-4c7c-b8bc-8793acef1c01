package com.integral.mdf.pool;

import java.util.concurrent.atomic.AtomicInteger;

import org.jctools.queues.SpscArrayQueue;

import com.integral.commons.pool.ObjectPool;
import com.integral.commons.pool.PoolFactory;
import com.integral.pool.ArrayListObjectPool;

/**
 * Low overhead objectpool for single produce - single consumer cases. This pool avoid monitors unlike {@link ArrayListObjectPool} 
 * <AUTHOR>
 *
 * @param <T>
 */
public class SPSCObjectPool<T> implements ObjectPool<T> {

	private static final String EMPTY = "";

	private final SpscArrayQueue<T> pool;

	private final PoolFactory<T> factory;

	private AtomicInteger borrowedFromPool;

	private AtomicInteger returnedToPool;

	private AtomicInteger createdInPool;

	private boolean statsEnabled;

	public SPSCObjectPool(PoolFactory<T> factory, final int size, final int maxSize, final boolean statsEnabled) {
		this.factory = factory;
		this.pool = new SpscArrayQueue<T>(maxSize);
		for (int i = 0; i < size; i++) {
			T t = factory.newPooledInstance(this);
			this.pool.offer(t);
		}
		this.statsEnabled = statsEnabled;
		if (statsEnabled) {
			borrowedFromPool = new AtomicInteger(0);
			returnedToPool = new AtomicInteger(0);
			createdInPool = new AtomicInteger(0);
		}
	}

	public SPSCObjectPool(PoolFactory<T> factory, final int size, final int maxSize) {
		this(factory, size, maxSize, false);
	}

	@Override
	public T borrowObject() {
		T obj = this.pool.poll();
		if (obj == null) {
			obj = this.factory.newUnPooledInstance();
			if (statsEnabled) {
				createdInPool.incrementAndGet();
			}
		} else if (statsEnabled) {
			borrowedFromPool.incrementAndGet();
		}
		return obj;
	}

	@Override
	public void returnObject(T object) {
		this.factory.recycle(object);
		// ignore if full
		if (this.pool.offer(object) && statsEnabled) {
			returnedToPool.incrementAndGet();
		}
	}

	public int size() {
		return this.pool.size();
	}

	public int getSize() {
		return this.pool.size();

	}

	public String report() {
		if (statsEnabled) {
			StringBuilder stats = new StringBuilder();
			stats.append("bfp=");
			stats.append(this.borrowedFromPool.getAndSet(0));
			stats.append(",rtp=");
			stats.append(this.returnedToPool.getAndSet(0));
			stats.append(",cip=");
			stats.append(this.createdInPool.getAndSet(0));
			return stats.toString();
		} else {
			return EMPTY;
		}
	}

}
