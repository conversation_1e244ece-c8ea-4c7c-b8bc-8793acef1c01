package com.integral.mdf;

import com.integral.mdf.cache.CacheClientC;
import com.integral.mdf.cache.subscription.SubscriptionCacheManagerC;
import com.integral.mdf.configuration.ConsulConfiguration;
import com.integral.mdf.configuration.PrometheusConfiguration;
import java.util.Arrays;
import java.util.Optional;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

import com.integral.alert.AlertLoggerFactory;
import com.integral.alert.impl.AlertMBeanC;
import com.integral.alert.impl.DefaultAlertLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.log.LogRingBufferMgr;
import com.integral.mdf.cluster.ClusteringService;
import com.integral.mdf.cluster.OnDemandNodeStateTransitionHandler;
import com.integral.mdf.cluster.PartitionTransitionHandlerFactory;
import com.integral.mdf.credit.CreditListener;
import com.integral.mdf.data.ConfigType;
import com.integral.mdf.data.FIProvision;
import com.integral.mdf.data.ServerProvision;
import com.integral.mdf.hb.HeartBeatListener;
import com.integral.mdf.multicast.MultiAddressMulticastRateSource;
import com.integral.mdf.multicast.MulticastRateSource;
import com.integral.mdf.proivision.MDFProvisionDataService;
import com.integral.mdf.proivision.builder.MDFProvisionBuilder;
import com.integral.mdf.proivision.builder.RemoteProvisionBuilder;
import com.integral.mdf.rate.CustomRateDistributionManager;
import com.integral.mdf.rate.RateDistributionManager;
import com.integral.mdf.rate.live.multicast.MulticastLiveRateReceiver;
import com.integral.mdf.rmq.RMQMessageGateway;
import com.integral.mdf.subscriptions.SubscriptionManager;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.services.cluster.mapped.notification.OnlineOffLineMappedTransitionHandler;
import org.apache.commons.lang3.StringUtils;

/**
 */
public class Main {

    private static final String MDF_CLUSTER_NAME_PREFIX = "clusterNamePrefix";
    private static final String DEFAULT_MDF_CLUSTER_NAME_PREFIX = "MDFCluster-";
    private static final String OnDemand_CLUSTER_PREFIX_key = "ondemandClusterNamePrefix";
    private static final String DEFAULT_OnDemand_PREFIX = "MDF-OnDemand-";

    Log log;

    public boolean init(ConfigType type, String vsName, String serverURL, String userPrefix)
            throws Exception {

        AlertLoggerFactory.setLogger(new DefaultAlertLogger(new AlertMBeanC("mdf.properties"), vsName));
        // start housekeeper worker - it is meant to keep doing low priority
        // tasks.
        Util.housekeeper.getClass();

        // start log
        LogRingBufferMgr bufferMgr = new LogRingBufferMgr(Util.housekeeper);
        LogRingBufferMgr.setInstance(bufferMgr);
        ShutdownHandle.getInstance().registerTask( () -> this.flushLogs());

        log = LogFactory.getLog(Main.class);

        MetricsManager instance = MetricsManager.instance();
        instance.setScheduledExecutorService(Util.scheduler);
        instance.start();

        log.info("Starting MDF Server in virtualserver:" + vsName + ",rds server url:" + serverURL);

        MDFProvisionBuilder builder;
        Properties runTimeProperties;
        switch (type) {
            case REMOTE:
                ClientInitializer clientInitializer = new ClientInitializer();
                runTimeProperties = clientInitializer.init(serverURL, vsName, userPrefix);
                builder = new RemoteProvisionBuilder(runTimeProperties);
                break;
            default:
                throw new UnsupportedOperationException("Config type is not supported:" + type);
        }

        MDFProvisionDataService dataService = MDFProvisionDataService.getInstance();
        dataService.init(builder);
        ServerProvision serverProvision = dataService.getServerProvision();
        serverProvision.logDetails("Main.init ");

        //init RateDistributionManager
        boolean isODA = serverProvision.isOnDemandAggregation();
        RateDistributionManager rdm = isODA ? new CustomRateDistributionManager(serverProvision) : new RateDistributionManager(serverProvision);

        //start rate listener
        MulticastRateSource mrs = serverProvision.isUseCcypBasedMCastAddresses() ? new MultiAddressMulticastRateSource(serverProvision,rdm)
                : new MulticastRateSource(serverProvision, rdm);
        mrs.start(null);//common multicast address and port for all ccyps.

        //init SubscriptionManager
        SubscriptionManager sm = new SubscriptionManager(serverProvision,rdm,MDFProvisionDataService.getInstance());

        //initializing Credit listener
        new CreditListener(serverProvision, rdm);

        new MulticastLiveRateReceiver(serverProvision);
        //init aggregators

        if(serverProvision.isClusteredNode()) {
            log.info("Server is running in clustered mode.");
            joinCluster(runTimeProperties, serverProvision, rdm, sm);
        }else{
            log.info("Server is running in standalone mode. loading tenants from mdf.properties file. " +
                    "Tenants=" + Arrays.toString(serverProvision.getTenants()));
            for(String fi: serverProvision.getTenants()){
                log.info("Start provisioning " +fi);
                try {
                    MDFProvisionDataService.getInstance().provision(fi);
                    Optional<Integer> orgIndex = serverProvision.getOrgIndex(fi);
                    if (orgIndex.isPresent()) {
                        FIProvision fiProvision = serverProvision.getFIProvision(orgIndex.get());
                        rdm.createRateBooks(fiProvision);
                    } else {
                        log.warn("FI organization not present in the venue:" + fi);
                    }
                    log.info("Completed rate books creation and activation for FI=" + fi);
                }catch (Exception e){
                    log.warn("Failed to provision fi=" + fi);
                }
            }
        }

        //start heartbeat listener
        if (serverProvision.getMEHBInterval() > 0 && serverProvision.isRatesFromMatchingEngine()) {
            HeartBeatListener hbl = new HeartBeatListener(serverProvision, rdm);
            Util.scheduler.scheduleAtFixedRate(hbl, 0, serverProvision.getMEHBInterval(), TimeUnit.MILLISECONDS);
            log.info("Main.init started heartbeat checker, interval(millis)=" + serverProvision.getMEHBInterval());
            hbl.start();
        } else {
            log.info("Venue HeartBeatListener not started. interval=" + serverProvision.getMEHBInterval()
                    + " ratesFromME="+serverProvision.isRatesFromMatchingEngine());
        }

        PrometheusConfiguration.getInstance().startServer(vsName);
        if (StringUtils.isNotEmpty(runTimeProperties.getProperty(ClientInitializer.SD_ADDRESS))) {
            ConsulConfiguration cc = new ConsulConfiguration(runTimeProperties.getProperty(ClientInitializer.SD_ADDRESS));
            cc.registerService(vsName, "mdf", PrometheusConfiguration.getInstance().getPort());
            Runtime.getRuntime().addShutdownHook(new Thread(cc::unregisterServices));
        }

        return true;
    }

    /*
     * Initialize distributed cache
     */
    private void initializeDistributedCache(ServerProvision serverProvision) {
        if (serverProvision.getDistributedCacheHeartbeatAddress() != null) {
            log.infoAsFormat("Initializing distributed cache, Heartbeat address %s", serverProvision.getDistributedCacheHeartbeatAddress());
            CacheClientC.init(serverProvision.getDistributedCacheHeartbeatAddress());
            SubscriptionCacheManagerC.init(CacheClientC.getInstance().getCacheInstance());
            ShutdownHandle.getInstance().registerTask(CacheClientC.getInstance());
        }
    }

    private void joinCluster(Properties properties, ServerProvision serverProvision, RateDistributionManager rdm, SubscriptionManager sm) throws Exception {

        String venueName = serverProvision.getVenueName();
        String clusterPrefix = getClusterNamePrefix(serverProvision,properties);
        String clusterName = clusterPrefix + venueName;

        ClusteringService chs = ClusteringService.getInstance();

        boolean joinCluster;
        if(serverProvision.isOnDemandAggregation()){
            initializeDistributedCache(serverProvision);
            RMQMessageGateway rmqg = new RMQMessageGateway(serverProvision,sm).init();
            ShutdownHandle.getInstance().registerTask(rmqg);
            joinCluster = chs.joinCluster(properties,clusterName,new OnDemandNodeStateTransitionHandler(rmqg));
        }else {
            OnlineOffLineMappedTransitionHandler handler = PartitionTransitionHandlerFactory.createHandler(rdm, serverProvision.getPartitionType());
            joinCluster = chs.joinCluster(properties, clusterName, handler);
        }

        if (joinCluster) {
            log.info("Joined the cluster:" + clusterName);
            ShutdownHandle.getInstance().registerTask(new ClusterInstanceShutdown(clusterName));
        } else {
            throw new Exception("Failed to join the cluster:" + clusterName);
        }
    }

    private String getClusterNamePrefix(ServerProvision sp, Properties properties){
        if(sp.isOnDemandAggregation()){
            return (String) properties.getOrDefault(OnDemand_CLUSTER_PREFIX_key,DEFAULT_OnDemand_PREFIX);
        }else{
            return (String) properties.getOrDefault(MDF_CLUSTER_NAME_PREFIX,DEFAULT_MDF_CLUSTER_NAME_PREFIX);
        }
    }

    public static void main(String[] args) throws Exception {

        String serverURL = null;
        String vsName = null;
        String userPrefix = null;

        // All the parameters are optional. Will use defaults if not provided
        if (args != null) {
            if (args.length > 0) {
                // The first parameter is virtual server name.
                vsName = args[0];
            }
            if (args.length > 1) {
                // The Second parameter is RDS server URL.
                serverURL = args[1];
            }
            if (args.length > 2) {
                // The user prefix.
                userPrefix = args[2];
            }
        }

        long st = System.currentTimeMillis();
        Main main = new Main();

        try {
            main.init(ConfigType.REMOTE, vsName, serverURL, userPrefix);
            main.log.info("MDF Server started in (ms) - " + (System.currentTimeMillis() - st));
        } catch (Exception e) {
            main.log.error("Fatal error. Failed to init MDF server", e);
            AlertLoggerFactory.getMessageLogger().log("MDF_STARTUP_FAILED", Main.class.getName(), "Fatal error. " +
                    "Failed to init MDF server:Cause:" + e.getMessage(), null);
            exit();
        }
        Runtime r = Runtime.getRuntime();
        r.addShutdownHook(ShutdownHandle.getInstance());

        Thread.currentThread().join();
    }

    protected static boolean exit() {
        //The log buffers to be flushed immediately.
        flushLogs();
        System.exit(-1);
        return true;
    }

    protected static boolean flushLogs(){
        LogRingBufferMgr mgr = LogRingBufferMgr.getInstance();
        if (mgr != null)
            mgr.stop();
        else
            return false;
        return true;
    }

}
