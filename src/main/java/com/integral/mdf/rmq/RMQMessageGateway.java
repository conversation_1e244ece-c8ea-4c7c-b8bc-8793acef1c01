package com.integral.mdf.rmq;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.MessageGateway;
import com.integral.mdf.ShutdownTask;
import com.integral.mdf.data.ServerProvision;
import com.integral.mdf.subscriptions.SubscriptionManager;
import com.integral.messaging.*;
import com.integral.notifications.mdf.SubscriptionRequest;
import com.integral.notifications.mdf.SubscriptionResponse;

import java.util.Optional;

public class RMQMessageGateway implements ShutdownTask, MessageGateway {

    final String exchange = "MDFX";
    final String queuePrefix = "mdfOnDemandQ";
    final String queue;

    Log log = LogFactory.getLog(this.getClass());
    boolean initialized = false;
    ServerProvision serverProvision;
    MessageReceiver mr;
    MessageSender ms;
    final SubscriptionManager sm;

    public RMQMessageGateway(ServerProvision serverProvision, SubscriptionManager sm){
        this.serverProvision=serverProvision;
        this.queue = queuePrefix + serverProvision.getVirtralServer();
        this.sm = sm;
        this.sm.setGateway(this);
    }

    public RMQMessageGateway init(){
        if(initialized){
            return this;
        }
        try {
            mr = MessageReceiverFactory.newMessageReceiver(exchange,queue,false,
                    true,true,false, new RMQMessageHandler());
        } catch (MessagingException e) {
            throw new RuntimeException("failed to initialized rmq message receiver",e);
        }

        try {
            ms = MessageSenderFactory.newMessageSender(exchange);
        } catch (MessagingException e) {
            throw new RuntimeException("failed to initialized rmq message receiver",e);
        }

        initialized = true;
        return this;
    }

    public boolean shutdown(){
        try{
            mr.close();
            MessageManager.getInstance().shutdown();
            log.info("shutdown rmq gateway");
        }catch (Exception e){
        }
        return true;
    }

    public void startListener(int partitionIndex){
        if(!initialized){
            throw new RuntimeException("rmq gateway is not initialized");
        }
        String bindingKey = getBindingKey(partitionIndex);
        try {
            sm.subscribeCachedRequest(bindingKey);
            mr.addBinding(bindingKey);
        } catch (MessagingException e) {
            throw new RuntimeException("failed to add binding to partition=" + partitionIndex,e);
        }
    }

    public void stopListener(int partitionIndex){
        if(!initialized){
            throw new RuntimeException("rmq gateway is not initialized");
        }
        String bindingKey = getBindingKey(partitionIndex);
        try {
            mr.removeBinding(bindingKey);
            sm.unsubscribeCachedRequest(bindingKey);
        } catch (MessagingException e) {
            throw new RuntimeException("failed to remove binding to partition=" + partitionIndex,e);
        }
    }

    private String getBindingKey(int partitionIndex){
        return serverProvision.getVenueName() + ".#." + partitionIndex;
    }

    public class RMQMessageHandler implements MessageListener{
        public void onMessage(RMQMessage message){
            log.info("rmq recv m="+message);
            Object payload = message.getPayload();
            if(payload!=null&&payload instanceof SubscriptionRequest){
                SubscriptionRequest sr = (SubscriptionRequest)payload;
                sm.handleSubscriptionRequest(sr,sr.getSentBy(), message.getRoutingKey());
            }
        }
    }

    @Override
    public void sendResponse(SubscriptionResponse response, Optional<String> senderId){
        try {
            String rkey = !senderId.isPresent() ? serverProvision.getVenueName() : senderId.get();
            ms.sendMessage(rkey, response);
        }catch (Exception e){
            StringBuilder sb= new StringBuilder("failed to send subscription response. id=");
            sb.append(response.getRequestId());
            sb.append(", msg=").append(response.getMessage());
            sb.append(", s=").append(response.getStatus());
            if(response.getError()!=null){
                //error has tostring
                sb.append(", err=").append(response.getError());
            }
            log.info(sb.toString());
            if(log.isDebugEnabled()){
                log.debug("exception e", e);
            }
        }
    }



}
