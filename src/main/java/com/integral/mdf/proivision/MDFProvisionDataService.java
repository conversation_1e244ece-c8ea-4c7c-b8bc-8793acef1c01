package com.integral.mdf.proivision;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.data.FIProvision;
import com.integral.mdf.data.ServerProvision;
import com.integral.mdf.data.ServerProvisionImpl;
import com.integral.mdf.proivision.builder.MDFProvisionBuilder;
import com.integral.virtualserver.MDFEntity;

import java.util.Optional;

public class MDFProvisionDataService {

	private static MDFProvisionDataService provisionDataService = new MDFProvisionDataService();

	private MDFEntity entity;
	
	private MDFProvisionBuilder builder;
	
	private ServerProvisionImpl serverProvision;

	private Log log = LogFactory.getLog(this.getClass());
	
	private MDFProvisionDataService(){
	}

	public static MDFProvisionDataService getInstance() {
		return provisionDataService;
	}

	public void init(MDFProvisionBuilder builder) throws Exception {
		serverProvision = new ServerProvisionImpl();
		
		this.builder = builder;
		this.entity =  builder.buildVenue(serverProvision);
		
		builder.buildRuntime(serverProvision,entity);		
		builder.buildCurrencyProvision(serverProvision);
		builder.buildConventions(serverProvision);
		builder.buildOrgIndexMapForLPs(serverProvision);
		builder.buildOrgIndexMapForMDFFIs(serverProvision);
		builder.setDistributedCacheAddress(serverProvision);
	}
	
	public ServerProvision getServerProvision() {
		return serverProvision;
	}
	
	public MDFEntity getEntity() {
		return entity;
	}

	public String getVenueName() {
		return serverProvision.getVenueName();
	}

    /**
     * fetches latest data from rds and caches it
     * @param fi
     * @throws Exception
     */
	public boolean provision(String fi) throws Exception {
		boolean result = builder.buildFIProvision(fi,serverProvision);
		//serverProvision.logFIUpdates("MDFProvisionDataService.provision for the FI:"+fi);
		log.info("Provisioned FI = " + fi);
		return result;
	}

    /**
     * fetches latest data from rds and caches it. fires OrgIndexQuery to fetch orgname.
     * @param orgIndex
     * @throws Exception
     */
	public void provision(int orgIndex) throws Exception{
        Optional<String> orgName = serverProvision.getOrgName(orgIndex);
        if(orgName.isPresent()){
            builder.buildFIProvision(orgName.get(),serverProvision);
        }else{
            throw new RuntimeException("org index not provisioned. index=" + orgIndex);
        }
    }

    public boolean provisionIfNot(String fi) throws Exception{
		Optional<Integer> orgIndex = serverProvision.getOrgIndex(fi);
		if(orgIndex.isPresent()){
			FIProvision fip = serverProvision.getFIProvision(orgIndex.get());
			if(fip!=null) {
				log.info("FI already provisioned. fi="+fi);
				return true;
			}else{
				return this.provision(fi);
			}
		}else{
			return this.provision(fi);
		}
	}

}
