package com.integral.mdf.proivision;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.data.ServerProvisionImpl;
import com.integral.mdf.proivision.builder.RemoteProvisionBuilder;

public class ProvisionValidator {
	
	static Log log = LogFactory.getLog(RemoteProvisionBuilder.class);
	
	private static final ProvisionValidator validator = new ProvisionValidator();
	
	public static ProvisionValidator getInstance(){
		return validator;
	}
	
	public void validateRuntime(ServerProvisionImpl serverProvision) {

		int port = serverProvision.getRateMulticastPort();
		String msg;
		if(port<=0){
			msg = "Invalid multicast port for receiving rates,value:"+port;
			log.info(msg);
			throw new IllegalArgumentException(msg);
		}
		
		port = serverProvision.getLiveRateMulticastPort();
		if(port<=0){
			msg = "Invalid multicast port for receiving live mds rates,value:"+port;
			log.info(msg);
			throw new IllegalArgumentException(msg);
		}
		
		port = serverProvision.getCreditMulticastPort();
		if(port<=0){
			msg = "Invalid multicast port for receiving credit updates ,value:"+port;
			log.info(msg);
			throw new IllegalArgumentException(msg);
		}
	}
	

}
