package com.integral.mdf.proivision.builder;

import com.integral.mdf.data.ServerProvisionImpl;
import com.integral.virtualserver.MDFEntity;

public interface MDFProvisionBuilder {

	public MDFEntity buildVenue(ServerProvisionImpl serverProvision) throws Exception;

	public boolean buildFIProvision(String fi, ServerProvisionImpl serverProvision)  throws Exception;
	
	public void buildVenueProvision(String name, ServerProvisionImpl serverProvision)	throws Exception;

	public void buildRuntime(ServerProvisionImpl serverProvision,MDFEntity mdfEntity) throws Exception;

	public void buildCurrencyProvision(ServerProvisionImpl serverProvision) throws Exception;

	public void buildConventions(ServerProvisionImpl serverProvision) throws Exception;
	
	public void buildOrgIndexMapForLPs(ServerProvisionImpl serverProvision) throws Exception;

	public void buildOrgIndexMapForMDFFIs(ServerProvisionImpl serverProvision);

	void setDistributedCacheAddress(ServerProvisionImpl serverProvision);
}
