package com.integral.mdf.proivision.builder;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.alert.AlertLoggerFactory;
import com.integral.mdf.Util;
import com.integral.mdf.cache.CacheClientC;
import com.integral.mdf.data.FIProvisionImpl;
import com.integral.mdf.data.ServerProvision;
import com.integral.mdf.data.ServerProvisionImpl;
import com.integral.mdf.proivision.ProvisionValidator;
import com.integral.model.OracleEntity;
import com.integral.model.ReferenceEntity;
import com.integral.organization.MulticastAddress;
import com.integral.provision.*;
import com.integral.rds.client.ClientFactory;
import com.integral.rds.client.ReferenceDataService;
import com.integral.rds.exception.ReferenceDataServiceException;
import com.integral.rds.message.QueryBuilder;
import com.integral.transport.multicast.MulticastAddressPoolService;
import com.integral.virtualserver.MDFEntity;

public class RemoteProvisionBuilder implements MDFProvisionBuilder {

    private static final String RATES_LISTENER_FROM_PROPERTIES = "ratesListenerFromProperties";
    private static final String RATES_FROM_MACHING_ENGINE = "ratesFromMachingEngine";
    private static final String USE_SUPERLP_STREAM_INDEX = "useSuperLPStreamIndex";
    private static final String LIVE_RATE_MESSAGE_SIZE = "liveRateMessageSize";
    private static final int DEFAULT_RATE_MESSAGE_SIZE = 2048;
    private static final String PROCESSOR_THREAD_IDLE_INTERVAL = "processorThreadIdleInterval";
    private static final String MAXIMUM_NO_OF_RATE_PROCESSORS = "maximumNoOfRateProcessors";

    private static final String NAMESPACE = "main";
    public static final String CURRENCY_SEPARATOR = "/";
    private static final long ORG_TICK_STREAM_ID = -1l;
    private static final long ORG_ONDEMAMD_STREAM_ID = -2l;

    public static final String DATA_CENTER_NAME = "dataCenterName";
    private static final String VIRTUAL_SERVER_NAME = "virtualServerName";
    private static final String SNAME = "sname";
    private static final String DC = "dc";

    private static final String RDS_CLIENT_TIMEOUT = "rds.client.timeout";
    private static final String CURRENCY_PROVISION_PAGE_SIZE = "provision.currency.page.size";
    private static final String RATE_CONVENTION_PROVISION_PAGE_SIZE = "provision.rate.convention.page.size";
    private static final long DEFAULT_RDS_CLIENT_TIMEOUT = 5000l;

    private static final String PROVISION_RATE_CONVENTIONS = "provision.rate.conventions";

    private static final String DEFAULT_PROVISION_RATE_CONVENTIONS = "STDQOTCNV";

    private static final String PRICEBOOK_MAX_DEPTH = "pricebookMaxDepth";

    private static final String ValidateValueDate = "ValidateValueDate";

    private static final String MulticastTTL = "MulticastTTL";

    private static final String Tenants = "Tenants";

    private static final String OpMode = "OperationMode";

    public static final int OPMODE_CLUSTERED = 0;
    public static final int OPMODE_STANDALONE = 1;

    public static final String OPMODE_CLUTER = "CLUSTER";
    public static final String OPMODE_STANDALNE = "STANDALONE";

    private Properties properties;

    static Log log = LogFactory.getLog(RemoteProvisionBuilder.class);

    private ProvisionValidator validator = ProvisionValidator.getInstance();

    public RemoteProvisionBuilder(Properties properties) throws Exception {

        ReferenceDataService rds = ClientFactory.getFactory()
                .getReferenceDataService();
        if (rds == null) {
            throw new Exception("RDS Client not initialized");
        }
        this.properties = properties;
    }

    @Override
    public MDFEntity buildVenue(ServerProvisionImpl serverProvision)
            throws Exception {
        String vsName = properties.getProperty(VIRTUAL_SERVER_NAME);
        String dcName = properties.getProperty(DATA_CENTER_NAME);

        serverProvision.setVirtralServer(vsName);

        MDFEntity mdfEntity = getMDFEntity(vsName, dcName);
        serverProvision.setVenueName(mdfEntity.getTradingVenue());

        buildVenueProvision(mdfEntity.getTradingVenue(), serverProvision);
        buildVenueConfigurations(serverProvision);
        return mdfEntity;
    }

    @Override
    public void buildRuntime(ServerProvisionImpl serverProvision,
                             MDFEntity mdfEntity) throws Exception {
        ServerRuntime serverRuntime = getServerRuntime(mdfEntity.getTradingVenue());
        serverProvision.setPriceBookMulticastPort(serverRuntime.getPriceBookMCPort());
        int depth;
        boolean isRatesFromMachingEngine;
        if (mdfEntity.isReadFromConfigPortal()) {
            serverProvision.setMaxRateProcessorCount(mdfEntity.getRateProcessors());
            depth = mdfEntity.getPriceBookDepth();
            isRatesFromMachingEngine = mdfEntity.isRatesFromMachingEngine();
            serverProvision.setRatesFromMatchingEngine(isRatesFromMachingEngine);
            serverProvision.setValidateValueDate(mdfEntity.isValidateValueDate());
            if(mdfEntity.getOperationMode()!=null){
                if(mdfEntity.getOperationMode().equals(OPMODE_STANDALNE)) {
                    serverProvision.setOperationMode(OPMODE_STANDALONE);
                }
            }
            serverProvision.setTenants(mdfEntity.getTenants().split(","));
        } else {
            serverProvision.setMaxRateProcessorCount(getIntProperty(MAXIMUM_NO_OF_RATE_PROCESSORS, 2));
            depth = getIntProperty(PRICEBOOK_MAX_DEPTH, 10);
            serverProvision.setValidateValueDate(getBooleanProperty(ValidateValueDate, true));
            isRatesFromMachingEngine = getBooleanProperty(RATES_FROM_MACHING_ENGINE, false);
            serverProvision.setRatesFromMatchingEngine(isRatesFromMachingEngine);
            serverProvision.setOperationMode(getIntProperty(OpMode,OPMODE_CLUSTERED));
            if(!properties.getProperty(Tenants,"").trim().equalsIgnoreCase(""))
                serverProvision.setTenants(properties.getProperty(Tenants,"").split(","));
        }

        serverProvision.setThreadIdleTimeInterval(getIntProperty(PROCESSOR_THREAD_IDLE_INTERVAL, 1000));

        String creditMCAddress = serverRuntime.getCreditMCAddress();

        serverProvision.setCreditMulticastGroup(getAddress(creditMCAddress));
        serverProvision.setCreditMulticastPort(serverRuntime.getCreditMCPort());

        serverProvision.setLiveRateMulticastgroup(getAddress(serverRuntime.getLiveRateMCAddress()));
        serverProvision.setLiveRateMulticastPort(serverRuntime.getLiveRateMCPort());

        serverProvision.setRateMessageSize(getIntProperty(LIVE_RATE_MESSAGE_SIZE, DEFAULT_RATE_MESSAGE_SIZE));
        serverProvision.setUseSuperLPStreamIndex(getBooleanProperty(USE_SUPERLP_STREAM_INDEX, false));

        serverProvision.setMEHBPort(serverRuntime.getHeartBeatMCPort());
        serverProvision.setMEHBMulticastGroup(getAddress(serverRuntime.getHeartBeatMCAddress()));
        serverProvision.setMEHBInterval(serverRuntime.getHeartBeatMCInterval());
        serverProvision.setCreditCutoffPercent((int) serverRuntime.getMvCreditCutOffPercent());

        if (depth > 50) {
            depth = 50;
            log.info("Invalid value for price book depth. Setting it to 50. given depth=" + depth + " , reset depth="
                    + depth);
        } else if (depth < 1) {
            depth = 1;
            log.info("Invalid value for price book depth. Setting it to 1. given depth=" + depth + " , reset depth="
					+ depth);
        }
        serverProvision.setMaxPriceBookDepth(depth);

        boolean ratesListenerFromProperties = getBooleanProperty(RATES_LISTENER_FROM_PROPERTIES, false);

        int port;
        if (isRatesFromMachingEngine) {
            if (ratesListenerFromProperties) {
                log.info("Receiving the rates from matching engine since property value is set to true for property:"
						+ RATES_LISTENER_FROM_PROPERTIES);
                serverProvision.setRateMulticastgroup(getAddress(serverRuntime.getRateListenerMCAddress()));
                serverProvision.setRateMulticastPort(serverRuntime.getRateListenerMCPort());
            } else {
                log.info("Receiving the rates from matching engine stream since property value is set to true for " +
						"property:" + RATES_FROM_MACHING_ENGINE);
                String venueName = serverProvision.getVenueName();
                OrgProvision orgProvision = getOrgProvision(venueName);

                List<MulticastAddressProvision> mCAddrs = orgProvision.getMultiCastAddresses();
                for (MulticastAddressProvision mCAddr : mCAddrs) {
                    if (null != mCAddr.getStreamName()
                            && mCAddr.getStreamName().equals(mdfEntity.getStreamName())) {
                        serverProvision.setRateMulticastgroup(getAddress(mCAddr.getAddress()));
                    }
                }
                //Venue organization multi cast port is the broadcast port for rate
                port = orgProvision.getPort();
                serverProvision.setRateMulticastPort(port);
            }
        } else {
            log.info("Receiving the rates from provider since property value is set to false for property:" +
					RATES_FROM_MACHING_ENGINE);
            // temporary using the provider multi cast group and port till Matching
            // engine is ready
            serverProvision.setRateMulticastgroup(getAddress(serverRuntime.getProviderMCAddress()));
            port = serverRuntime.getProviderMCPort();
            serverProvision.setRateMulticastPort(port);
        }
        
        serverProvision.setMulticastTTL(getIntProperty(MulticastTTL, 3));

        if(!isNullOrEmpty(mdfEntity.getAggregationFrequency()))
            serverProvision.setAggregationFrequency(mdfEntity.getAggregationFrequency());

        if(!isNullOrEmpty(mdfEntity.getNodeType()))
            serverProvision.setNodeType(mdfEntity.getNodeType());

        serverProvision.setUseCcypBasedMCastAddresses(serverRuntime.isUseCcypMcastAddress());

        if(serverRuntime.getMdfClusterPartitionType()!=null){
            serverProvision.setPartitionType(serverRuntime.getMdfClusterPartitionType());
        }

        serverProvision.setMaxAggregationAttempts(getIntProperty("maxAggregationAttempts",10));

        validator.validateRuntime(serverProvision);
    }


    public boolean isNullOrEmpty(String s){
        return s != null && s.trim().isEmpty();
    }


    @Override
    public void buildCurrencyProvision(ServerProvisionImpl serverProvision)
            throws Exception {
        updateCcyProvision(serverProvision);
    }

    private Optional<InetAddress> getAddress(String host) {
        try {
            // if host is null it returns loop back inet address.
            return Optional.of(InetAddress.getByName(host));
        } catch (UnknownHostException e) {
            log.warn("Unable to get inet address:{" + host + "}", e);
            return Optional.empty();
        }
    }

    private ServerRuntime getServerRuntime(String venue) throws Exception {
        long sTime = System.currentTimeMillis();
        ReferenceDataService rds = ClientFactory.getFactory()
                .getReferenceDataService();

        ServerRuntimeQuery query = new ServerRuntimeQuery();
        query.setShortName(venue);

        List<OracleEntity> provisionedLps = rds.process(query);
        ServerRuntime serverRuntime = (ServerRuntime) provisionedLps.get(0);

        log.info("RPB.getServerRuntime():Time taken to get server runtime (ms):"
                + (System.currentTimeMillis() - sTime));

        return serverRuntime;
    }

    public OrgProvision getOrgProvision(String orgName) throws Exception {
        long sTime = System.currentTimeMillis();
        ReferenceDataService rds = ClientFactory.getFactory()
                .getReferenceDataService();
        String vsName = properties.getProperty(VIRTUAL_SERVER_NAME);
        String dcName = properties.getProperty(DATA_CENTER_NAME);

        List<String> orgNames = Collections.singletonList(orgName);
        OrgProvisionQuery orgQuery = new OrgProvisionQuery(dcName + vsName, orgNames);

        List<OracleEntity> orgProvisions = rds.process(orgQuery);
        OrgProvision orgProvision = (OrgProvision) orgProvisions.get(0);

        log.info("RPB.getOrgProvision():Time taken to get org:" + orgName
                + " provision(ms):" + (System.currentTimeMillis() - sTime));

        return orgProvision;

    }
    
    public List<OracleEntity> getPriceProvision(String fi,String lp, String venueName){
    	 long sTime = System.currentTimeMillis();
         ReferenceDataService rds = ClientFactory.getFactory().getReferenceDataService();
         PriceProvisionQuery orgQuery = new PriceProvisionQuery(fi,lp,"MDF", venueName);
         try{
        	 List<OracleEntity> list =  rds.process(orgQuery);
             log.info("RPB.getPriceProvision(): fi=" + fi + ", lp=" + lp 
            		 + ", time=" + (System.currentTimeMillis() - sTime));
             return list;
         }catch(Exception e){
        	 log.warn("getPriceProvision. failed to retrieve price provision for fi=" + fi + ", lp=" + lp );
         }
         return Collections.emptyList();
    }

    private MDFEntity getMDFEntity(String vsName, String dcName)
            throws Exception {
        long sTime = System.currentTimeMillis();
        ReferenceDataService rds = ClientFactory.getFactory()
                .getReferenceDataService();
        QueryBuilder builder = new QueryBuilder(MDFEntity.class);
        builder.addStringParam(SNAME, vsName);
        builder.addStringParam(DC, dcName);
        Future<List<? extends ReferenceEntity>> listFuture = rds.retrieve(
                MDFEntity.class, NAMESPACE, builder.build(), null);

        List<? extends ReferenceEntity> referenceEntities = listFuture
                .get(getLongProperty(RDS_CLIENT_TIMEOUT,
                        DEFAULT_RDS_CLIENT_TIMEOUT), TimeUnit.MILLISECONDS);

        if (referenceEntities == null || referenceEntities.isEmpty()) {
            String msg = "MDF Server provisioned data is not available";
            log.error(msg);
            throw new Exception(msg);
        }
        log.info("RPB.getMDFEntity():Time taken to get mdf provision(ms):"
                + (System.currentTimeMillis() - sTime));

        return (MDFEntity) referenceEntities.get(0);
    }

    public boolean buildFIProvision(String fi, ServerProvisionImpl serverProvision)
            throws Exception {
        long sTime = System.currentTimeMillis();
        FIProvisionImpl fiProvision = new FIProvisionImpl(fi);

        OrgProvision orgProv = getOrgProvision(fi);
        fiProvision.setPriceBookMulticastgroup(getPriceBookMCAddress(orgProv,serverProvision));
        fiProvision.setOrgProvision(orgProv);
        fiProvision.setDefaultLEObjId(orgProv.getDefaultLEIndex());
        fiProvision.setIndex(orgProv.getIndex());
        fiProvision.setMarketDepth(orgProv.getMaxDepth());
        if (orgProv.getMdfAggregationType() != null) {
            fiProvision.setAggregationType(orgProv.getMdfAggregationType());
        } else {
            log.info("AggregationType not defined using default. fi=" + fi);
        }
        if(orgProv.getMtFOKTierLimits()!=null && !orgProv.getMtFOKTierLimits().isEmpty()) {
            double[] tiers= new double[orgProv.getMtFOKTierLimits().size()];
            int i=0;
            for (Double tier : orgProv.getMtFOKTierLimits()){
                tiers[i++] = tier;
            }
            fiProvision.setAggregationTiers(tiers);
        }
        fiProvision.setTierCurrency(orgProv.getTierCurrency());
        String fxRateConvention = orgProv.getFxRateConvention();

        updateRateConventionForFI(fxRateConvention, fiProvision, serverProvision);

        // get the convention for the FI
        List<OracleEntity> provisionedLps = getLPProvisions(fi, serverProvision);

        if (provisionedLps.isEmpty()) {
            log.error("No LP provisions for the FI:" + fi + ". No RateBooks will be created.");
            return false;
        }

        LPProvision lpProvision;
        Map<Integer, CurrencyPairProvision> supportedCurrecnyPairs = new HashMap<Integer, CurrencyPairProvision>();
        Map<Integer, Set<Integer>> lpSupportedCccyPairs = new HashMap<Integer, Set<Integer>>();
        Optional<Integer> currencyPairIndex;
        Optional<Long> hashKey;
        Set<Integer> supportedCcyPairs;
        for (int i = 0; i < provisionedLps.size(); i++) {
            lpProvision = (LPProvision) provisionedLps.get(i);
            fiProvision.add(lpProvision, serverProvision);
            supportedCcyPairs = lpSupportedCccyPairs.get(getStreamIndex(serverProvision, lpProvision));
            if (null == supportedCcyPairs) {
                supportedCcyPairs = new HashSet<Integer>();
                lpSupportedCccyPairs.put(
                        getStreamIndex(serverProvision, lpProvision),
                        supportedCcyPairs);
            }
            List<CurrencyPairProvision> ccyPairs = lpProvision.getCcyPairs();
            if (null == ccyPairs || ccyPairs.isEmpty()) {
                log.warn("No supported currency pairs for the relationship between LP:"
                        + getLPName(lpProvision) + ", FI:" + fi);
            } else {
                for (CurrencyPairProvision ccyPair : ccyPairs) {
                    currencyPairIndex = addToCcyPairMappings(ccyPair.getShortName(), serverProvision);
                    if (currencyPairIndex.isPresent()) {
                        supportedCurrecnyPairs.put(currencyPairIndex.get(), ccyPair);
                        supportedCcyPairs.add(currencyPairIndex.get());
                    }
                }
            }

            if( !lpProvision.getShortName().equals(serverProvision.getVenueName()) ){
            	List<OracleEntity> priceProvisions = getPriceProvision(fi, lpProvision.getShortName(), serverProvision.getVenueName());
            	// unique across providers
            	if (null != priceProvisions) {
            		int streamIndex = getStreamIndex(serverProvision, lpProvision);
            		for (OracleEntity ppEntity : priceProvisions) {
            			PPPProvision priceProvision = (PPPProvision)ppEntity;
            			hashKey = getstreamCcyPairHashCode(streamIndex, priceProvision.getCcyPair(), serverProvision,
            					fiProvision);
            			if (hashKey.isPresent()) {
            				fiProvision.putSpreadProvision(hashKey.get(), priceProvision);
            			}
            		}
            	}
            }
        }

        fiProvision.setLPSupportedCcyPairs(lpSupportedCccyPairs);
        fiProvision.setSupportedCcyPairs(supportedCurrecnyPairs);
        fiProvision.setAggregationInterval(orgProv.getAggregationInterval());

        //finally add FIProvision to cache.
        serverProvision.addOrgMapping(orgProv.getShortName(), orgProv.getIndex(), fiProvision);
        log.info("RPB.buildFIProvision fiProvision=" + fiProvision.getShortString() );
        log.info("RPB.buildFIProvision fi=" + fi + " Time taken(ms)=" + (System.currentTimeMillis() - sTime));
        return true;
    }


    public void buildVenueProvision(String fi, ServerProvisionImpl serverProvision)
            throws Exception {
        long sTime = System.currentTimeMillis();
        FIProvisionImpl fiProvision = new FIProvisionImpl(fi);
        OrgProvision orgProv = getOrgProvision(fi);
        fiProvision.setPriceBookMulticastgroup(getPriceBookMCAddress(orgProv,serverProvision));
        fiProvision.setOrgProvision(orgProv);
        fiProvision.setDefaultLEObjId(orgProv.getDefaultLEIndex());
        fiProvision.setIndex(orgProv.getIndex());
        fiProvision.setMarketDepth(orgProv.getMaxDepth());
        if (orgProv.getMdfAggregationType() != null) {
            fiProvision.setAggregationType(orgProv.getMdfAggregationType());
        } else {
            log.info("AggregationType not defined using default. venue=" + fi);
        }
        serverProvision.addOrgMapping(orgProv.getShortName(), orgProv.getIndex(), fiProvision);
        log.info("RPB.buildVenueProvision fi=" + fi + " Time taken(ms)=" + (System.currentTimeMillis() - sTime));
    }

    protected String getLPName(LPProvision lpProvision) {
        String lpName = lpProvision.getShortName();
        if (lpProvision.getRealLP() != null && !lpProvision.getRealLP().equals(lpName)) {
            lpName = lpName + "(" + lpProvision.getRealLP() + ")";
        }
        return lpName;
    }

    private int getStreamIndex(ServerProvisionImpl serverProvision,
                               LPProvision lpProvision) {
        return Util.getStreamIndex(serverProvision, lpProvision);
    }

    protected List<OracleEntity> getLPProvisions(String fi, ServerProvisionImpl serverProvision) {
        ReferenceDataService rds = ClientFactory.getFactory().getReferenceDataService();
        LPProvisionQuery provisionQuery = new LPProvisionQuery(fi);
        provisionQuery.setTvOrgName(serverProvision.getVenueName());

        try {
            return rds.process(provisionQuery);
        } catch (ReferenceDataServiceException e) {
            log.error("Unable to get LP provision for :" + fi, e);
            AlertLoggerFactory.getMessageLogger().log("MDF_LPPROVISION_FAILED", RemoteProvisionBuilder.class.getName(),
					"Fatal error:Unable to get LP provision for :" + fi + ":Cause:" + e.getMessage(), null);
            return Collections.emptyList();
        }
    }

    private Optional<Long> getstreamCcyPairHashCode(int superBankLpStreamIndex,
                                                    String ccyPairName, ServerProvisionImpl serverProvision,
                                                    FIProvisionImpl fiProvision) {

        String[] split = ccyPairName.split(CURRENCY_SEPARATOR);

        if (split.length != 2) {
            log.warn("Util.getCurrencyPairIndex():Unable to load currency pair, ignoring value.Invalid currency pair " +
					"format:"
                    + ccyPairName);
            return Optional.empty();
        }
        int baseccyIdx = serverProvision.getCcyNamesVsIndex().get(split[0]);
        int varccyIndex = serverProvision.getCcyNamesVsIndex().get(split[1]);

        return fiProvision.getSpreadKey(superBankLpStreamIndex, baseccyIdx,
                varccyIndex);
    }

    private void updateRateConventionForFI(String rateConvention,
                                           FIProvisionImpl fiProvision, ServerProvisionImpl serverProvision)
            throws Exception {

        long sTime = System.currentTimeMillis();

        String server_rateConvention = properties.getProperty(PROVISION_RATE_CONVENTIONS, DEFAULT_PROVISION_RATE_CONVENTIONS);

        ReferenceDataService rds = ClientFactory.getFactory().getReferenceDataService();

        if(!server_rateConvention.equalsIgnoreCase(rateConvention)) {
            FXRateConventionProvisionQuery rCProvQuery;
            int skip = 0;
            int limit = getIntProperty(RATE_CONVENTION_PROVISION_PAGE_SIZE, 25);
            List<OracleEntity> rateBasisProvisions;
            CurrencyPairProvision ccyPairProvision;
            List<Long> timeTakenPerPage = new ArrayList<Long>();
            Optional<Integer> ccyPairIndex;
            Map<Integer, CurrencyPairProvision> ccyPairProvisions = new HashMap<Integer, CurrencyPairProvision>();
            long sTimePerPage;
            do {
                sTimePerPage = System.currentTimeMillis();
                rCProvQuery = new FXRateConventionProvisionQuery(rateConvention,
                        skip, limit);
                rateBasisProvisions = rds.process(rCProvQuery);
                for (int i = 0; i < rateBasisProvisions.size(); i++) {
                    ccyPairProvision = (CurrencyPairProvision) rateBasisProvisions.get(i);
                    ccyPairIndex = addToCcyPairMappings(ccyPairProvision.getShortName(), serverProvision);
                    if (ccyPairIndex.isPresent()) {
                        ccyPairProvisions.put(ccyPairIndex.get(), ccyPairProvision);
                    }
                }
                // Update skip for the next fetch
                skip += rateBasisProvisions.size();
                timeTakenPerPage.add(System.currentTimeMillis() - sTimePerPage);
            } while (rateBasisProvisions != null && !rateBasisProvisions.isEmpty()
                    && rateBasisProvisions.size() == limit);

            fiProvision.setRateConventions(ccyPairProvisions);
            log.info("RPB.updateRateConventionForFI():Time taken to get rate convention:"
                    + rateConvention + ", fi=" + fiProvision.getName()
                    + ", time taken per page:"
                    + timeTakenPerPage
                    + " (ms),Total time (ms):"
                    + (System.currentTimeMillis() - sTime));
        }else{
            log.info("RPB.updateRateConventionForFI(): Time taken - convention="
                    + rateConvention + ", fi=" + fiProvision.getName()
                    + " , time(ms)=" +(System.currentTimeMillis() - sTime));
            fiProvision.setRateConventions(serverProvision.getCcyPairIdxVsProvisionMap());
        }
    }

    protected void updateCcyProvision(ServerProvisionImpl serverProvisionImpl)
            throws ReferenceDataServiceException {
        long sTime = System.currentTimeMillis();

        Map<Integer, String> ccyIndexVsNames = new HashMap<Integer, String>();
        Map<String, Integer> ccyNamesVsIndex = new HashMap<String, Integer>();

        ReferenceDataService rds = ClientFactory.getFactory()
                .getReferenceDataService();
        CurrencyProvisionQuery ccyQuery = new CurrencyProvisionQuery();

        // Keeping one since first cp is null @ server
        int skip = 1;
        int limit = getIntProperty(CURRENCY_PROVISION_PAGE_SIZE, 50);
        List<OracleEntity> provisionedcys;
        List<Long> timeTakenPerPage = new ArrayList<Long>();
        long sTimePerPage;
        int maxCcyIndex = 0;
        do {
            sTimePerPage = System.currentTimeMillis();
            ccyQuery.setSkip(skip);
            ccyQuery.setLimit(limit);
            provisionedcys = rds.process(ccyQuery);
            if (provisionedcys != null) {
                CurrencyProvision ccyProvision;
                for (OracleEntity provisionedcy : provisionedcys) {
                    ccyProvision = (CurrencyProvision) provisionedcy;
                    ccyIndexVsNames.put(ccyProvision.getIndex(),
                            ccyProvision.getShortName());
                    ccyNamesVsIndex.put(ccyProvision.getShortName(),
                            ccyProvision.getIndex());
                    maxCcyIndex = maxCcyIndex > ccyProvision.getIndex() ? maxCcyIndex
                            : ccyProvision.getIndex();
                    serverProvisionImpl.addCcyProvision(ccyProvision.getIndex(), ccyProvision);
                }
                // Update skip for the next fetch
                skip += provisionedcys.size();
            }
            timeTakenPerPage.add(System.currentTimeMillis() - sTimePerPage);
        } while (provisionedcys != null && !provisionedcys.isEmpty()
                && provisionedcys.size() == limit);

        serverProvisionImpl.setCcyIndexVsNames(ccyIndexVsNames);
        serverProvisionImpl.setCcyNamesVsIndex(ccyNamesVsIndex);
        serverProvisionImpl.setMaxCcyIndex(maxCcyIndex);

        log.info("RPB.updateCcyProvision():Time taken to get currency provision(ms):"
                + timeTakenPerPage
                + ",Total time (ms):"
                + (System.currentTimeMillis() - sTime));
    }

    protected boolean getBooleanProperty(String proName, boolean defaultValue) {
        String value = properties.getProperty(proName);
        if (null != value) {
            try {
                return Boolean.valueOf(value.trim());
            } catch (NumberFormatException nfe) {
                log.warn("RPB.getBooleanProperty():Invalid value in property:"
                        + proName + ",value:" + value
                        + ".Considering default value:" + defaultValue);
                return defaultValue;
            }
        }

        log.warn("RPB.getBooleanProperty():Invalid value in property:"
                + proName + ",value:" + value + ".Considering default value:"
                + defaultValue);
        return defaultValue;

    }

    protected int getIntProperty(String proName, int defaultValue) {

        String value = properties.getProperty(proName);
        if (null != value) {
            try {
                return Integer.parseInt(value.trim());
            } catch (NumberFormatException nfe) {
                log.warn("RPB.getIntProperty():Invalid value in property:"
                        + proName + ",value:" + value
                        + ".Considering default value:" + defaultValue);
                return defaultValue;
            }
        }
        log.warn("RPB.getIntProperty():Invalid value in property:" + proName
                + ",value:" + value + ".Considering default value:"
                + defaultValue);
        return defaultValue;
    }

    protected long getLongProperty(String proName, long defaultValue) {

        String value = properties.getProperty(proName);
        if (null != value) {
            try {
                return Long.parseLong(value.trim());
            } catch (NumberFormatException nfe) {
                log.warn("RPB.getLongProperty():Invalid value in property:"
                        + proName + ",value:" + value
                        + ".Considering default value:" + defaultValue);
                return defaultValue;
            }
        }
        log.warn("RPB.getLongProperty():Invalid value in property:" + proName
                + ",value:" + value + ".Considering default value:"
                + defaultValue);
        return defaultValue;
    }

    private Optional<Integer> addToCcyPairMappings(String ccyPairName,
                                                   ServerProvisionImpl serverProvision) {

        Map<String, Integer> ccyNamesVsIndex = serverProvision.getCcyNamesVsIndex();
        Optional<Integer> currencyPairIndex = Util.getCurrencyPairIndex(ccyPairName, ccyNamesVsIndex);

        if (currencyPairIndex.isPresent()) {
            serverProvision.addCcyPairMapping(ccyPairName, currencyPairIndex.get());
        }
        return currencyPairIndex;
    }

    private Optional<InetAddress> getPriceBookMCAddress(OrgProvision orgProvision, ServerProvision sp) {

        List<MulticastAddressProvision> multiCastAddresses = orgProvision.getMultiCastAddresses();

        long streamId = sp.isOnDemandAggregation() ? ORG_ONDEMAMD_STREAM_ID : ORG_TICK_STREAM_ID;

        Optional<InetAddress> optional = Optional.empty();
        for (MulticastAddressProvision multicastAddressProvision : multiCastAddresses) {
            if (multicastAddressProvision.getStreamId() == streamId) {
                String address = multicastAddressProvision.getAddress();
                try {
                    return Optional.of(InetAddress.getByName(address));
                } catch (Exception e) {
                    log.warn("RPB.getPriceBookMulticastAddress():Unable to lookup inet address for:"
                            + address + ", for the org:" + orgProvision.getShortName());
                }
            }
        }
        log.warn("RPB.getPriceBookMulticastAddress():No multicast group defined for the org:"
                + orgProvision.getShortName() + " grpType=" + streamId );
        return optional;
    }

    @Override
    public void buildConventions(ServerProvisionImpl serverProvision) throws ReferenceDataServiceException {
        String rateConvention = properties.getProperty(PROVISION_RATE_CONVENTIONS, DEFAULT_PROVISION_RATE_CONVENTIONS);
        updateForRateConvention(rateConvention, serverProvision);
    }


    protected void updateForRateConvention(String rateConvention, ServerProvisionImpl serverProvision) throws ReferenceDataServiceException {

        ReferenceDataService rds = ClientFactory.getFactory()
                .getReferenceDataService();

        FXRateConventionProvisionQuery rCProvisionQuery;
        long sTime = System.currentTimeMillis();
        int skip = 0;
        int limit = getIntProperty(RATE_CONVENTION_PROVISION_PAGE_SIZE, 25);
        List<OracleEntity> rateBasisProvisions;
        CurrencyPairProvision rateBasisProvision;
        List<Long> timeTakenPerPage = new ArrayList<Long>();
        long sTimePerPage;
        do {
            sTimePerPage = System.currentTimeMillis();
            rCProvisionQuery = new FXRateConventionProvisionQuery(
                    rateConvention, skip, limit);
            rateBasisProvisions = rds.process(rCProvisionQuery);
            for (int i = 0; i < rateBasisProvisions.size(); i++) {
                rateBasisProvision = (CurrencyPairProvision) rateBasisProvisions.get(i);
                updateCPProvision(rateBasisProvision, serverProvision);
            }
            // Update skip for the next fetch
            skip += rateBasisProvisions.size();
            timeTakenPerPage.add(System.currentTimeMillis() - sTimePerPage);
        } while (rateBasisProvisions != null
                && !rateBasisProvisions.isEmpty()
                && rateBasisProvisions.size() == limit);
        log.info("RPB.updateForRateConvention():Time taken to get rate convention:" + rateConvention + ", time taken per page:"
                + timeTakenPerPage
                + " (ms),Total time (ms):"
                + (System.currentTimeMillis() - sTime));
    }

    private void updateCPProvision(CurrencyPairProvision rateBasisProvision, ServerProvisionImpl serverProvision) {
        String ccyPairName = rateBasisProvision.getShortName();
        serverProvision.addCcypPairProvision(ccyPairName, rateBasisProvision);
        addToCcyPairMappings(rateBasisProvision.getShortName(), serverProvision);
    }


    public void buildOrgIndexMapForLPs(ServerProvisionImpl serverProvision) {
        try {
            ReferenceDataService rds = ClientFactory.getFactory().getReferenceDataService();
            OrgIndexProvisionQuery oipq = new OrgIndexProvisionQuery();
            List<OracleEntity> resultList = rds.process(oipq);
            for (OracleEntity oe : resultList) {
                OrgIndexProvision oip = (OrgIndexProvision) oe;
                Map<String, Integer> orgidxmap = oip.getOrgIndexes();
                for (Map.Entry<String, Integer> e : orgidxmap.entrySet()) {
                    serverProvision.addOrgMapping(e.getKey(), e.getValue(), null);
                }
            }
        } catch (ReferenceDataServiceException rdse) {
            log.warn("Failed to retrieve orgIndex mapping for LPs.",rdse);
            AlertLoggerFactory.getMessageLogger().log("MDF_ORGINDEXMAPPING_FAILED", RemoteProvisionBuilder.class.getName(),
                    "Fatal error:Failed to retrieve orgIndex mapping for LPs:Cause:" + rdse.getMessage(), null);
        }

    }

    public void buildOrgIndexMapForMDFFIs(ServerProvisionImpl serverProvision) {
        try {
            ReferenceDataService rds = ClientFactory.getFactory().getReferenceDataService();
            OrgIndexProvisionQuery oipq = new OrgIndexProvisionQuery();
            oipq.setShortName("MDFFIS");
            oipq.setTradingVenue(serverProvision.getVenueName());
            List<OracleEntity> resultList = rds.process(oipq);
            for (OracleEntity oe : resultList) {
                OrgIndexProvision oip = (OrgIndexProvision) oe;
                Map<String, Integer> orgidxmap = oip.getOrgIndexes();
                for (Map.Entry<String, Integer> e : orgidxmap.entrySet()) {
                    serverProvision.addOrgMapping(e.getKey(), e.getValue(), null);
                }
            }
        } catch (ReferenceDataServiceException rdse) {
            log.warn("Failed to retrieve orgIndex mapping for LPs.",rdse);
            AlertLoggerFactory.getMessageLogger().log("MDF_ORGINDEXMAPPING_FAILED", RemoteProvisionBuilder.class.getName(),
                    "Fatal error:Failed to retrieve orgIndex mapping for MDFFIs:Cause:" + rdse.getMessage(), null);
        }

    }

    private void buildVenueConfigurations(ServerProvisionImpl serverProvision) {
        Map<String, VenueConfiguration> venueConfigurations = new HashMap<String, VenueConfiguration>();
        String tradingVenue = serverProvision.getVenueName();
        try {
            ReferenceDataService rds = ClientFactory.getFactory().getReferenceDataService();

            if(tradingVenue == null){
                log.warn("failed to retrieve VenueConfigurations since venue name is not specified. venue= " + tradingVenue);
                return;
            }

            VenueConfigurationQuery query = new VenueConfigurationQuery(tradingVenue);
            List<OracleEntity> resultList = rds.process(query);
            for (OracleEntity e : resultList) {
                VenueConfiguration venueConfig = (VenueConfiguration) e;
                for (String ccy : venueConfig.getCurrencyPair()) {
                    venueConfigurations.put(ccy, venueConfig);
                }
            }

            serverProvision.setVenueConfigurations(venueConfigurations);

        } catch (ReferenceDataServiceException rdse) {
            log.warn("Failed to retrieve trading venue configuration. tradingVenue=" + tradingVenue + ", m=" + rdse
                    .getErrorCode(), rdse);
            AlertLoggerFactory.getMessageLogger().log("MDF_TVCONFIG_FAILED", RemoteProvisionBuilder.class.getName(),
                    "Failed to retrieve trading venue configuration. tradingVenue=" + tradingVenue + ", m=" + rdse
                            .getErrorCode(), null);
        } catch (Exception e) {
            log.warn("Failed to retrieve trading venue configuration. tradingVenue=" + tradingVenue, e);
        }
    }

    @Override
    public void setDistributedCacheAddress(ServerProvisionImpl serverProvision) {
        try {
            MulticastAddress address = MulticastAddressPoolService.getInstance().createMulticastAddress(CacheClientC.MDF_CACHE, CacheClientC.MULTICAST_HEARTBEAT);
            serverProvision.setDistributedCacheHeartbeatAddress(address.getMulitcastAddress());
        } catch (Exception e) {
            log.error("Error while setting distributed cache address", e);
        }
    }

}
