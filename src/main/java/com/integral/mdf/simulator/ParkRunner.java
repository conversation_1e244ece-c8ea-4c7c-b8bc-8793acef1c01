package com.integral.mdf.simulator;

import java.util.concurrent.locks.LockSupport;

import static java.util.concurrent.TimeUnit.NANOSECONDS;

public class ParkRunner implements Runnable {
    private static final int PARK_NANOS = 10000;

    private final long iterationCount;

    public ParkRunner(long iterationCount) {
        this.iterationCount = iterationCount;
    }

    @Override
    public void run() {
        long startNanos = System.nanoTime();
        for (long i = 0; i < iterationCount; i++) {
            //LockSupport.parkNanos(PARK_NANOS);
            await();
        }
        long durationNanos = System.nanoTime() - startNanos;
        long durationMillis = NANOSECONDS.toMillis(durationNanos);
        System.out.println(iterationCount + " iterations in " + durationMillis + " ms.");

        long microsPerIteration = NANOSECONDS.toMicros(durationNanos) / iterationCount;
        System.out.println("This means each iteration took " + microsPerIteration + " microseconds");
        System.out.println("This means each iteration took " + (durationNanos/iterationCount) + " nanoseconds");

    }

//    double await(){
//        double d = 0.0;
//        for(int i=0;i<1000;i++){
//          d = d + Math.sqrt(i*144.0d);
//        }
//        return d;
//    }

    void await(){
        System.out.print('c');
        System.out.print('c');
        System.out.print('c');

    }


    public static void main(String[] args) {
        ParkRunner pr = new ParkRunner(100000);
        for(int i=0;i<10;i++)
            pr.run();
    }


}
