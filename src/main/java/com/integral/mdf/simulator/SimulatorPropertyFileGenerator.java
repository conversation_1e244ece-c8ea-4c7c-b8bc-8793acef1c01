package com.integral.mdf.simulator;

import java.io.FileOutputStream;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import com.integral.alert.AlertLoggerFactory;
import com.integral.alert.impl.AlertMBeanC;
import com.integral.alert.impl.DefaultAlertLogger;
import com.integral.log.LogRingBufferMgr;
import com.integral.mdf.ClientInitializer;
import com.integral.mdf.Util;
import com.integral.mdf.data.FIProvision;
import com.integral.mdf.data.ServerProvision;
import com.integral.mdf.data.ServerProvisionImpl;
import com.integral.mdf.proivision.MDFProvisionDataService;
import com.integral.mdf.proivision.builder.MDFProvisionBuilder;
import com.integral.mdf.proivision.builder.RemoteProvisionBuilder;
import com.integral.model.OracleEntity;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.provision.LPProvision;
import com.integral.provision.OrgProvision;
import com.integral.provision.OrgProvisionQuery;
import com.integral.rds.client.ClientFactory;
import com.integral.rds.client.ReferenceDataService;

public class SimulatorPropertyFileGenerator {
	
	public static void main(String[] args) throws Exception {
		
		String serverURL = null;
		String vsName = null;
		String fiName =null;
		String filePath =null;
		// All the parameters are optional. Will use defaults if not provided
		if (args != null) {
			if (args.length >= 4 ) {
				// The first parameter is virtual server name.
				vsName = args[0];
				// The Second parameter is RDS server URL.
				serverURL = args[1];
				// The FI Name.				
				fiName = args[2];
				// The complete file path.				
				filePath = args[3];
			}else{
				throw new IllegalArgumentException("Invalid input. Useage <VIRTUAL_SERVER_NAME,RDS_URI,FI_NAME,FILE>");
			}
		}else{
			throw new IllegalArgumentException("Invalid input. Useage <VIRTUAL_SERVER_NAME,RDS_URI,FI_NAME,FILE>");
		}
		
		Properties props = new Properties();
		props.put("ratePattern", "UnlimitedPaced");
		props.put("rateInterval", "100000000");
		props.put("numRates", "400");
		props.put("ccypNames", "EUR/USD,USD/JPY,EUR/GBP");
		
		ServerProvisionImpl serverProvision = (ServerProvisionImpl)getServerProvision(vsName , serverURL);
		props.put("port", String.valueOf(serverProvision.getRateMulticastPort()));
		props.put("multicastAddress", serverProvision.getRateMulticastgroup().get().getHostAddress());
		props.put("ccyNamesVsIndex", getStringValue(serverProvision.getCcyNamesVsIndex()));

		MDFProvisionDataService.getInstance().provision(fiName);
		
		FIProvision fiProvision = serverProvision.getFIProvision(serverProvision.getOrgIndex(fiName).get());
		
		Collection<LPProvision> lpProvisions = fiProvision.getLPProvisions();
		
		StringBuilder lps = new StringBuilder();
		for (LPProvision lpProvision : lpProvisions) {
			OrgProvision orgProvision = getOrgProvision(lpProvision.getShortName(), vsName);
			lps.append(lpProvision.getStreamIndex()+"@"+orgProvision.getIndex());
			lps.append(",");
		}
		props.put("lpStreamIds", lps.toString());
		
		FileOutputStream fos = new FileOutputStream(filePath);
		props.store(fos, "A Test to write properties");
		fos.flush();
		fos.close();
		
		System.out.println("Output flushed to:"+filePath);
	}
	
	
	private static String getStringValue(Map<String, Integer> ccyNamesVsIndex) {
		
		StringBuilder str = new StringBuilder();
		for (Map.Entry<String, Integer> entry : ccyNamesVsIndex.entrySet()) {
			str.append(entry.getKey()).append("=").append(entry.getValue()).append(",");
		}
		return str.toString();
	}


	public static OrgProvision getOrgProvision(String orgName,String vsName) throws Exception {
		ReferenceDataService rds = ClientFactory.getFactory()
				.getReferenceDataService();
		List<String> orgNames = Collections.singletonList(orgName);
		OrgProvisionQuery orgQuery = new OrgProvisionQuery("NEWYORK" + vsName,
				orgNames);

		List<OracleEntity> orgProvisions = rds.process(orgQuery);
		OrgProvision orgProvision = (OrgProvision) orgProvisions.get(0);
		return orgProvision;
	}

	private static ServerProvision getServerProvision(String vsName,String serverURL ) throws Exception {
		AlertLoggerFactory.setLogger(new DefaultAlertLogger(new AlertMBeanC("mdf.properties"), vsName));
		// start housekeeper worker - it is meant to keep doing low priority
		// tasks.
		Util.housekeeper.getClass();

		// start log
		LogRingBufferMgr bufferMgr = new LogRingBufferMgr(Util.housekeeper);
		LogRingBufferMgr.setInstance(bufferMgr);

		MetricsManager instance = MetricsManager.instance();
		instance.setScheduledExecutorService(Util.scheduler);
		instance.start();
		
		ClientInitializer clientInitializer = new ClientInitializer();
		Properties runTimeProperties = clientInitializer.init(serverURL,vsName,"");
		MDFProvisionBuilder builder = new RemoteProvisionBuilder(runTimeProperties);
		
		MDFProvisionDataService dataService = MDFProvisionDataService.getInstance();
		dataService.init(builder);
		ServerProvision serverProvision = dataService.getServerProvision();		
		
		return serverProvision;
	}

}
