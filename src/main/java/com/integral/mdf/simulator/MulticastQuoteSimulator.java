package com.integral.mdf.simulator;

import java.io.IOException;
import java.io.InputStream;
import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.locks.LockSupport;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.mdf.Util;
import com.integral.mdf.data.MarketStatus;
import com.integral.mdf.data.QuoteC;
import com.integral.mdf.data.TVMarketStatus;

/**
 * Simulates quote update for MDF server.
 * 
 * <AUTHOR>
 *
 */
public class MulticastQuoteSimulator {

	final InetAddress address;
	final MulticastSocket mSocket;
	final int port;
	final long rateInterval;
	final String ratePattern;
	//final Map<String,Integer> currencies = new HashMap<>();
	final int[] ccyps;
	final long numSimulations;
	private Map<String, Integer> ccyIndexMap;
	final Stream[] streamIds;
	private final short valueDate; 
	
	/**
     * An <code>int</code> that represents the number of seconds in a day - does not adjust for leap seconds.
     */
    public static final int SECONDS_PER_DAY = 24 * 60 * 60;
    /**
     * A <code>long</code> that represents the number of milliseconds in a day.
     */
    public static final long MILLISECONDS_PER_DAY = SECONDS_PER_DAY * 1000;
    
	public MulticastQuoteSimulator(int num, Stream[] streamIds, Map<String, Integer> ccyIndexMap, String[] ccyps,
			String addr, int port, String ratePattern, long rateInterval,Short valueDate) throws Exception {
		
		this.ccyIndexMap = ccyIndexMap;

		this.ccyps = initCurrencyPairIndexes(ccyps, ccyIndexMap);
		
		this.address = InetAddress.getByName(addr);
		this.port = port;
		mSocket = new MulticastSocket(port);
		this.ratePattern = ratePattern;
		this.rateInterval = rateInterval;
		this.numSimulations = num;
		this.streamIds = streamIds;
		this.valueDate = valueDate;
	}
	
//	private void initCurrencies(int[] ccyIdxs, String[] ccys) {
//		if( ccyIdxs.length != ccys.length ){
//			System.err.println("currency index array and currency shortname array have different length.");
//			throw new IllegalArgumentException("currency index array and currency shortname array have different length.");
//		}
//		for (int i=0 ; i <  ccys.length ; i++) {
//			currencies.put(ccys[i], ccyIdxs[i]);
//		}
//	}
	
	int[] initCurrencyPairIndexes(String[] ccyps,Map<String,Integer> nameIndexMap ){
		int[] ccypIdx = new int[ccyps.length];
		for(int i=0;i<ccyps.length;i++){
			String[] ccyp = ccyps[i].split("/");
			System.out.println("ccyp=" + ccyps[i] + " , split="+Arrays.toString(ccyp));
			ccypIdx[i] = Util.getCurrencyPairIndex(nameIndexMap.get(ccyp[0]), nameIndexMap.get(ccyp[1]));
		}
		return ccypIdx;
	}

	void simulateRates() {
		if( "UnlimitedPaced".equalsIgnoreCase(ratePattern) ){
			simulateUnlimitedPaced();
		}else{
			simulateSingleBurst();
		}
	}

	void simulateUnlimitedPaced() {
		System.out.printf("%n Starting simulation (UnlimitedPaced) for LPs=%s, ccyp=%s, address=%s, rate-interval=%d", Arrays.toString(streamIds),
				Arrays.toString(ccyps), address, rateInterval);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyMMdd HH:mm:ss.SSS");
		long bookid = 0;
		long st = System.nanoTime();
		long msgSent = 0;

		QuoteC[] quotes = getRawQuotes();
		int cnt = 0;
		Random random = new Random();


		do {

			for (int ccyp : ccyps) {
				// get the template.
				for (int i = 0; i < streamIds.length; i++) {


					QuoteC quote = quotes[random.nextInt(7)];

					quote.setProviderIdx(streamIds[i].lp);
					quote.setStreamIdx(streamIds[i].stream);
					quote.setCcyPairIdx(ccyp);
					quote.setQuoteId(bookid++);
					quote.setValueDate((short) valueDate);
					quote.setQuoteId(cnt++);
					quote.setQuoteCreatedTime(System.currentTimeMillis());
					//System.out.println("sending quote -> " + quote.toString());
					DatagramPacket msg = new DatagramPacket(quote.buffer().byteArray(), quote.buffer().byteArray().length, address, port);
					try {
						mSocket.send(msg);
						msgSent++;
						if (msgSent % 100 == 0) {
							long et = System.nanoTime();
							long tt = et - st;
							double throughput = msgSent * 1000.0 * 1000.0 * 1000.0 / tt;
							System.out.println(sdf.format(new Date())+" throughput(msg/sec)=" + throughput + ", msgSent=" + msgSent + ", tt(ms)=" + tt / 1000000.0);
							st = System.nanoTime();
							msgSent = 0;
						}
						LockSupport.parkNanos(rateInterval);
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			}
		} while (true);
	}

	private QuoteC getRawQuote() {
		QuoteC quote = new QuoteC();

		quote.setPrice(quote.bidTiersOffset(), 1.21);

		quote.setBidTiersNum(quote.getBidTiersNum()+1);
		int bid1 = quote.tierOffset(QuoteC.BUY, 0);
		quote.setPrice(bid1, 1.21);
		quote.setTotalQty(bid1, 1000.0d);
		quote.setShowQty(bid1, 1000.0d);

		quote.setBidTiersNum(quote.getBidTiersNum()+1);
		bid1 = quote.tierOffset(QuoteC.BUY, 1);
		quote.setPrice(bid1, 1.20);
		quote.setTotalQty(bid1, 1000.0d);
		quote.setShowQty(bid1, 1000.0d);

		quote.setBidTiersNum(quote.getBidTiersNum()+1);
		bid1 = quote.tierOffset(QuoteC.BUY, 2);
		quote.setPrice(bid1, 1.19);
		quote.setTotalQty(bid1, 14900000.0d);
		quote.setShowQty(bid1, 14900000.0d);

		quote.setOfferTiersNum(quote.getOfferTiersNum()+1);
		int offer1 = quote.tierOffset(QuoteC.SELL, 0);
		quote.setPrice(offer1, 1.22);
		quote.setTotalQty(offer1, 1000.0d);
		quote.setShowQty(offer1, 1000.0d);

		quote.setOfferTiersNum(quote.getOfferTiersNum()+1);
		offer1 = quote.tierOffset(QuoteC.SELL, 1);
		quote.setPrice(offer1, 1.23);
		quote.setTotalQty(offer1, 1000.0d);
		quote.setShowQty(offer1, 1000.0d);

		quote.setOfferTiersNum(quote.getOfferTiersNum()+1);
		offer1 = quote.tierOffset(QuoteC.SELL, 2);
		quote.setPrice(offer1, 1.24);
		quote.setTotalQty(offer1, 14900000.0d);
		quote.setShowQty(offer1, 14900000.0d);

		return quote;
	}


	private QuoteC[] getRawQuotes() {

		QuoteC[] quotes = new QuoteC[7];
		double[] seedPrices = {1.21,1.22,1.21,1.23,1.21,1.23,1.22};

		for(int i =0 ; i<7;i++){

			QuoteC quote = new QuoteC();

			double bidP1 = seedPrices[i];  //1.21	1.22
			double offerP1 = bidP1 + 0.02; //1.23

			double bidP2 = bidP1 - 0.01;	//1.20	1.22
			double offerP2 = bidP1 + 0.04; //1.24

			double bidP3 = bidP2 - 0.01;	//1.19  1.22
			double offerP3 = bidP1 + 0.06;  //1.25

			double bidP4 = bidP3 - 0.01;	//1.19  1.22
			double offerP4 = bidP1 + 0.08;  //1.25

			double bidP5 = bidP4 - 0.01;	//1.19  1.22
			double offerP5 = bidP1 + 0.10;  //1.25

			//quote.setPrice(quote.bidTiersOffset(), 1.21);

			quote.setBidTiersNum(quote.getBidTiersNum()+1);
			int bid1 = quote.tierOffset(QuoteC.BUY, 0);
			quote.setPrice(bid1, bidP1);
			quote.setTotalQty(bid1, 1000.0d);
			quote.setShowQty(bid1, 1000.0d);

			quote.setBidTiersNum(quote.getBidTiersNum()+1);
			bid1 = quote.tierOffset(QuoteC.BUY, 1);
			quote.setPrice(bid1, bidP2);
			quote.setTotalQty(bid1, 1000.0d);
			quote.setShowQty(bid1, 1000.0d);

			quote.setBidTiersNum(quote.getBidTiersNum()+1);
			bid1 = quote.tierOffset(QuoteC.BUY, 2);
			quote.setPrice(bid1, bidP3);
			quote.setTotalQty(bid1, 2000.0d);
			quote.setShowQty(bid1, 2000.0d);

			quote.setBidTiersNum(quote.getBidTiersNum()+1);
			bid1 = quote.tierOffset(QuoteC.BUY, 3);
			quote.setPrice(bid1, bidP4);
			quote.setTotalQty(bid1, 2000.0d);
			quote.setShowQty(bid1, 2000.0d);

			quote.setBidTiersNum(quote.getBidTiersNum()+1);
			bid1 = quote.tierOffset(QuoteC.BUY, 4);
			quote.setPrice(bid1, bidP5);
			quote.setTotalQty(bid1, 2000.0d);
			quote.setShowQty(bid1, 2000.0d);

			
			//offers
			quote.setOfferTiersNum(quote.getOfferTiersNum()+1);
			int offer1 = quote.tierOffset(QuoteC.SELL, 0);
			quote.setPrice(offer1, offerP1);
			quote.setTotalQty(offer1, 1000.0d);
			quote.setShowQty(offer1, 1000.0d);

			quote.setOfferTiersNum(quote.getOfferTiersNum()+1);
			offer1 = quote.tierOffset(QuoteC.SELL, 1);
			quote.setPrice(offer1, offerP2);
			quote.setTotalQty(offer1, 1000.0d);
			quote.setShowQty(offer1, 1000.0d);

			quote.setOfferTiersNum(quote.getOfferTiersNum()+1);
			offer1 = quote.tierOffset(QuoteC.SELL, 2);
			quote.setPrice(offer1, offerP3);
			quote.setTotalQty(offer1, 2000.0d);
			quote.setShowQty(offer1, 2000.0d);
			
			quote.setOfferTiersNum(quote.getOfferTiersNum()+1);
			offer1 = quote.tierOffset(QuoteC.SELL, 3);
			quote.setPrice(offer1, offerP4);
			quote.setTotalQty(offer1, 1000.0d);
			quote.setShowQty(offer1, 1000.0d);

			quote.setOfferTiersNum(quote.getOfferTiersNum()+1);
			offer1 = quote.tierOffset(QuoteC.SELL, 4);
			quote.setPrice(offer1, offerP5);
			quote.setTotalQty(offer1, 2000.0d);
			quote.setShowQty(offer1, 2000.0d);			

			quotes[i] = quote;

		}

		return quotes;
	}

	void simulateSingleBurst(){
		System.out.printf("%n Starting simulation for fis=%s, ccyp=%s, address=%s", Arrays.toString(streamIds),
				Arrays.toString(ccyps), address);
		long bookid = 0;
		long cnt = 0;
		long msgSent =0;
		long st = System.nanoTime();
		QuoteC quote = getRawQuote();
		do {
			// get the template.
			for (int i = 0; i < streamIds.length; i++) {
				for (int ccyp : ccyps) {
					quote.setProviderIdx(streamIds[i].lp);
					quote.setStreamIdx(streamIds[i].stream);
					quote.setCcyPairIdx(ccyp);
					quote.setQuoteId(bookid++);
					quote.setQuoteCreatedTime(System.currentTimeMillis());
					DatagramPacket msg = new DatagramPacket(quote.buffer().byteArray(),
							quote.buffer().byteArray().length, address, port);
					try {
						mSocket.send(msg);
						msgSent++;
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			}
			cnt++;
		} while (cnt < numSimulations);
		long et = System.nanoTime();
		long tt = et - st;
		double throughput = msgSent * 1000.0 *1000.0/ tt;
		System.out.println("throughput(msg/millsec)=" + throughput + ", msgSent=" + msgSent + ", tt=" + tt);
	}

	public static void main(String[] args) throws Exception {

		if (args.length < 1) {
			System.out.println("USAGE: java MulticastQuoteSimulator <simulator.properties>");
			return;
		}


		InputStream is = ClassLoader.getSystemClassLoader().getResourceAsStream(args[0]);

		Properties prop = new Properties();
		prop.load(is);

		int num = Integer.parseInt(prop.getProperty("numRates", "10"));

		//Stream object ids or indexes.
		String streams = prop.getProperty("lpStreamIds", null);
		if (streams == null) {
			System.out.println("lpStreamIds can't be null.");
			System.exit(-1);
		}
		Stream[] streamIds = toStreamArray(streams);

		//currency pairs to simulate.
		String ccyp = prop.getProperty("ccypNames", null);
		if( ccyp == null ){
			System.out.println("ccypNames can't be null");
		}
		String[] ccyps = ccyp.split(",");
		
		//currency 
		String ccyNamesVsIndexStr = prop.getProperty("ccyNamesVsIndex",null);
		String[] ccyNamesVsIndexes = ccyNamesVsIndexStr.split(",");
		Map<String,Integer> ccyIndexMap = new HashMap<>();
		for(String s:ccyNamesVsIndexes){
			String[] s1 = s.split("="); 
			ccyIndexMap.put(s1[0].trim(),Integer.parseInt(s1[1].trim()));
		}
		
		//rate source multicast address
		String addr = prop.getProperty("multicastAddress", null);
		if (addr == null) {
			System.out.println("multicastAddress can't be null.");
			System.exit(-1);
		}
		
		//port
		int port = Integer.parseInt(prop.getProperty("port", "6070"));
		
		//rate pattern
		String ratePattern = prop.getProperty("ratePattern", "UnlimitedPaced");
		
		//rate simulation interval
		long rateInterval = Long.parseLong( prop.getProperty("rateInterval","100000000") );

		Calendar currentValueDate = Calendar.getInstance();
		currentValueDate.add(Calendar.DATE, 6);
		
		short valueDate = (short)( currentValueDate.getTimeInMillis() / MILLISECONDS_PER_DAY);
		
		//hb port, group
		int hbport = Integer.parseInt(prop.getProperty("hbport", "31914"));
		String hbaddr = prop.getProperty("hbMulticastAddress", "************");
		int hbInterval = Integer.parseInt(prop.getProperty("hbInterval", "5000"));
		
		MulticastQuoteSimulator sim = new MulticastQuoteSimulator(num, streamIds,ccyIndexMap,ccyps, addr,port, ratePattern,rateInterval,valueDate);
		
		sim.simulateHeartBeat(hbport,hbaddr,hbInterval);
		
		sim.simulateRates();
		

		Thread.currentThread().join();
		
	}

	private void simulateHeartBeat(final int hbport, final String hbaddr, final int hbInterval) {
		// TODO Auto-generated method stub
		new Thread(new Runnable() {
			
			@Override
			public void run() {
				
				try(MulticastSocket mSocket = new MulticastSocket(hbport)) {
					
					InetAddress hbaddress = InetAddress.getByName(hbaddr);
					int c = 0;
					MarketStatus status = MarketStatus.OPEN; //OPEN - default
					
					do{
						try {
							
//							if(c++%10==0)
//								status = MarketStatus.CLOSED; //closed
//							else
//								status = MarketStatus.OPEN;//open
							
							for(int ccyp : ccyps){
								TVMarketStatus tvms = new TVMarketStatus(TVMarketStatus.VENUE_TYPE_CLOB);
								tvms.setVenueCode(97);//clob2  in peeyush's db. change it to your matching engine org's index
								UnSafeBuffer buffer = new UnSafeBuffer();
								byte[] buf = new byte[32];
								buffer.init(buf);
								buffer.put((byte)1);
								buffer.putShort((short) 1);//CLOB_INFO
								tvms.setCcyPairIndex(ccyp);
								tvms.setNextMatchingTime(System.currentTimeMillis());
								tvms.setMarketStatus(status);
								tvms.writeTo(buffer);
								DatagramPacket msg = new DatagramPacket(buffer.array(), buffer.array().length, hbaddress, hbport);
								mSocket.send(msg);
							}
							Thread.sleep(hbInterval);
						} catch (Exception e) {
							e.printStackTrace();
						}
					}while(true);
				} catch (UnknownHostException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		},"hb-simulator").start();
	}

	static int[] toIntArray(String s) {
		return Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).toArray();
	}
	
	static Stream[] toStreamArray(String s){
		String[] streamstrs = s.split(",");
		Stream[] streams = new Stream[streamstrs.length];
		for (int i=0;i<streamstrs.length ;i++) {
			String[] s1 = streamstrs[i].split("@"); 
			streams[i] = new Stream(Integer.parseInt(s1[1]),Integer.parseInt(s1[0]));
		}
		return streams;
	}

}
