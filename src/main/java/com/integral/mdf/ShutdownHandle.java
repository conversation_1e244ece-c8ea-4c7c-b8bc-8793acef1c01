package com.integral.mdf;

import java.util.ArrayList;
import java.util.List;
import java.util.Stack;

import com.integral.log.Log;
import com.integral.log.LogFactory;

public class ShutdownHandle extends Thread {

	//Non- thread safe data structure used sicne its to do with shutdown classes.
	private Stack<ShutdownTask> shutdownTasks = new Stack<ShutdownTask>();
	
	private static final ShutdownHandle handle = new ShutdownHandle();

	private final Log log = LogFactory.getLog(this.getClass());

	public static ShutdownHandle getInstance() {
		return handle;
	}

	public void registerTask(ShutdownTask task) {
		shutdownTasks.push(task);
	}

	public List<ShutdownTask> getShutdownTasks() {
		return shutdownTasks;
	}

	public void run() {
		while(!shutdownTasks.empty()){
			ShutdownTask  shutdownTask = shutdownTasks.pop();
			processTask(shutdownTask);
		}
	}

	private void processTask(ShutdownTask shutdownTask) {
		try {
			shutdownTask.shutdown();
		} catch (Throwable t) {
			log.error("Error invoking the shutdown task:"+shutdownTask.getClass().getCanonicalName(), t);
		}
	}
}