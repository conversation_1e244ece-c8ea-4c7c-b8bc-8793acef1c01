package com.integral.mdf;

import com.integral.mdf.data.PriceBook;

public interface PriceBookSink {
	
	/**
	 * Activates this sink for further transmission.
	 */
	void begin();
	
	/**
	 * forwards given book to next destination. 
	 * @param book
	 */
	void accept(PriceBook book);
	
	/**
	 * terminates trasmission of the books. 
	 */
	void end();
	
	/**
	 * returns current state of book.
	 * @return
	 */
	boolean isActive();
}
