package com.integral.mdf.hb;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.util.Optional;

import org.jctools.maps.NonBlockingHashMapLong;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.data.MarketStatus;
import com.integral.mdf.data.ServerProvision;
import com.integral.mdf.data.TVMarketStatus;
import com.integral.mdf.rate.RateDistributionManager;

public class HeartBeatListener implements Runnable {
	
	final int port;
	final MulticastSocket socket;
	ServerProvision provision;
	volatile boolean shutdown = false;
	final RateDistributionManager dm;
	Log log = LogFactory.getLog(HeartBeatListener.class);
	private static int MSG_TYPE_OFFSET = 1;
	private final static short RISKNET_INFO = 0;
	private final static short CLOB_INFO = 1;
	private final static short ADAPTOR_HEARTBEAT = 2;
	private final static short BULK_ADAPTOR_HEARTBEAT = 3;
	long lastUpdateTime = 0;
	private NonBlockingHashMapLong<MarketStatus> marketStatus = new NonBlockingHashMapLong<>();
	final TVMarketStatus ps = new TVMarketStatus(TVMarketStatus.VENUE_TYPE_CLOB);
	final int meOrgIndex;
	final String venueName;
	final long expectedHbinterval;
	volatile boolean isTimeoutNotificationSent = false;

	public HeartBeatListener(ServerProvision provision, RateDistributionManager dm) throws IOException {
		this.port = provision.getMEHBPort();
		socket = new MulticastSocket(port);
		socket.setReuseAddress(true);
		this.provision = provision;
		this.dm = dm;
		this.venueName = provision.getVenueName();
		Optional<Integer> meOrgIndex = provision.getOrgIndex(provision.getVenueName());
		if(!meOrgIndex.isPresent()){
			throw new RuntimeException("Trading venue index not specified.");
		}
		this.meOrgIndex = meOrgIndex.get();
		this.expectedHbinterval = provision.getMEHBInterval();
		new Thread(new Worker(), "hb-reader").start();
	}

	public void start() {
		// 1. start listening join group....
		Optional<InetAddress> multicastgroup = provision.getMEHBMulticastAddress();
		if (!multicastgroup.isPresent()) {
			log.info("HBL.start Not joining the group since there's no multicast group defined");
			return;
		}

		InetAddress mcastaddress = multicastgroup.get();
		try {
			socket.joinGroup(mcastaddress);
		} catch (IOException e) {
			log.warn("HBL.start Error joining multicast group:"+mcastaddress.toString(),e);
			return;
		}
		log.info("HBL.start started heartbeat listener port="+this.port+", address="+multicastgroup.get() + ", meOrgIdx="+meOrgIndex);
	}
	
	public void run(){
		
		final long current = System.currentTimeMillis();
		final long lstupdtime = this.lastUpdateTime;
		if( (current - lstupdtime ) > ( expectedHbinterval * 3)  ){
			log.warn("Mathcing engine heartbeat timed out . lastupdateTime=" + lstupdtime +", ct="+current + ", interval="+ expectedHbinterval);
			if( !isTimeoutNotificationSent ){
				isTimeoutNotificationSent = true;
				this.marketStatus.clear();
				dm.onHeartbeatTimeout();
			}
		}
		
	}
	
	public void onHeartbeat(UnSafeBuffer buffer){
		buffer.skip(1);//skip version
		buffer.getShort();//skip type
		ps.readFrom(buffer);
		
		if(ps.getVenueCode() == this.meOrgIndex ) {
			MarketStatus newMarketStatus = ps.getMarketStatus();  
			MarketStatus currMarketStatus = this.marketStatus.get(ps.getCcyPairIndex()); 
			if (currMarketStatus != newMarketStatus) {
				if (currMarketStatus == null || currMarketStatus == MarketStatus.CLOSED || currMarketStatus == MarketStatus.PAUSE) {
					if (newMarketStatus == MarketStatus.OPEN || newMarketStatus == MarketStatus.PAUSE_MATCHING) {
						// close to open
						Optional<String> currencyPair = provision.getCcyPairName(ps.getCcyPairIndex());
						String ccyPair = currencyPair.isPresent() ? currencyPair.get() : String.valueOf(ps.getCcyPairIndex());
						log.info("HBL.onHeartbeat:c->o venue= " + venueName + ", ccypair= " + ccyPair + ", status=" + newMarketStatus.toString());
						this.dm.onOpenHeartbeat(ps.getCcyPairIndex());
					}
				} else if (currMarketStatus == null || currMarketStatus == MarketStatus.OPEN || currMarketStatus == MarketStatus.PAUSE_MATCHING) {
					if (newMarketStatus == MarketStatus.CLOSED || newMarketStatus == MarketStatus.PAUSE) {
						// open to close
						Optional<String> currencyPair = provision.getCcyPairName(ps.getCcyPairIndex());
						String ccyPair = currencyPair.isPresent() ? currencyPair.get() : String.valueOf(ps.getCcyPairIndex());
						log.info("HBL.onHeartbeat:o->c venue= " + venueName + ", ccypair= " + ccyPair + ", status=" + newMarketStatus.toString());
						this.dm.onCloseHeartbeat(ps.getCcyPairIndex());
					}
				}
				this.marketStatus.put(ps.getCcyPairIndex(),newMarketStatus);
			}
			this.lastUpdateTime = System.currentTimeMillis();
			isTimeoutNotificationSent = false;
			if(log.isDebugEnabled()){
				Optional<String> currencyPair = provision.getCcyPairName(ps.getCcyPairIndex());
				String ccyPair = currencyPair.isPresent() ? currencyPair.get() : String.valueOf(ps.getCcyPairIndex());
				log.debug("HBL.onHeartbeat:recv venue= " + venueName + ", ccypair= " + ccyPair + ", status=" + newMarketStatus.toString());
			}
		}else{
			if(log.isDebugEnabled()){
				log.debug("Received HB for unknown venue. PS="+ps.toString());
			}
		}
	}

	class Worker implements Runnable {

		UnSafeBuffer buffer = new UnSafeBuffer();
		byte[] buf = new byte[ps.getEstimatedSize()*2];

		public Worker() {
		}

		@Override
		public void run() {
			DatagramPacket dp = new DatagramPacket(buf, buf.length);
			do {
				try {
					socket.receive(dp);
					buffer.init(buf);
					short msgType = UnSafeBuffer.getShort(buf, MSG_TYPE_OFFSET);
					if(msgType == CLOB_INFO){
						onHeartbeat(buffer);
					}else{
						if(log.isDebugEnabled()) {
							log.debug("Unknown message received. msgType=" + msgType);
						}
					}
				} catch (Exception e) {
					log.warn("HBL.worker.run():Unable to handle multicast datagram packet.",e);
				} finally {
					buffer.resetMemoryBuffer();
				}
			} while (!shutdown);
		}
	}

	public void stop() {

		// 1. stop listening leave group....
		Optional<InetAddress> multicastgroup = provision.getMEHBMulticastAddress();
		if (!multicastgroup.isPresent()) {
			log.info("HBL.stop Not leaving the group since there's no multicast group defined");
			return;
		}

		InetAddress mcastaddress = multicastgroup.get();
		try {
			socket.leaveGroup(mcastaddress);
		} catch (IOException e) {
			log.warn("Error leaving multicast group:"+mcastaddress.toString(),e);
		}
		log.info("MRS.stop rate listener port="+this.port+", address="+multicastgroup.get());
	}

}
