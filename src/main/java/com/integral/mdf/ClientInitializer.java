package com.integral.mdf;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Properties;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.messaging.config.MessagingConfigUtil;
import com.integral.messaging.config.MessagingConfiguration;
import com.integral.messaging.config.model.ClusterConfiguration;
import com.integral.model.OracleEntity;
import com.integral.provision.ServerRuntime;
import com.integral.provision.ServerRuntimeQuery;
import com.integral.rds.client.ClientFactory;
import com.integral.rds.client.QueryIterator;
import com.integral.rds.client.ReferenceDataService;
import com.integral.rds.config.ServiceConfigMBeanC;
import com.integral.rds.exception.ReferenceDataServiceException;
import com.integral.rds.message.Query;
import com.integral.rds.message.QueryBuilder;
import com.integral.rds.persistence.QueryService;
import com.integral.mdf.proivision.builder.RemoteProvisionBuilder;
import org.apache.commons.lang3.StringUtils;

public class ClientInitializer {

	private static final String SEMI_COLON = ";";
	private static final String COMMA = ",";
	private static final String MULTICAST_PORT = "multicastPort";
	private static final String USER_PREFIX = "userPrefix";
	private static final String ZOO_QUORUM_STRING = "zooQuorumString";
	private static final String ZOO_NAMESPACE = "zooNameSpace";
	private static final String UNDERSCORE_SEPARATOR = "_";
	private static final String IDC_RDS_CLIENT_ID = "Idc.RDS.Client.Id";
	private static final String IDC_SERVER_URL = "Idc.RDS.Server.url";
	private final String configFileName;
	private static final String VIRTUAL_SERVER_NAME = "virtualServerName";
	private static final String DEFAULT_CONFIG_PROPS = "mdf.properties";
	public static final String SD_ADDRESS = "sd.address";

	static Log log = LogFactory.getLog(ClientInitializer.class);

	public ClientInitializer() throws Exception {
		configFileName = DEFAULT_CONFIG_PROPS;
	}

	public ClientInitializer(String configFileName) throws Exception {
		this.configFileName = configFileName;
	}

	public Properties init(String rdsURL, String vsName, String userPrefix)
			throws Exception {
		// initialize RDS client
		Properties mdfProperties = initRDSClient(rdsURL, vsName, userPrefix);
		
		// get runtime environment properties from RDS 
		getRunTimeProperties(userPrefix, mdfProperties);

		// initialize messaging
		initMessagingClient(mdfProperties.getProperty(USER_PREFIX));

		return mdfProperties;
	}

	public Properties initRDSClient(String rdsURL, String vsName,
			String userPrefix) throws Exception {
		Properties mdfProperties = new Properties();

		mdfProperties.load(ClassLoader.getSystemClassLoader()
				.getResourceAsStream(configFileName));

		// Override server URL and Client ID if provided . Else user the default
		if (rdsURL != null) {			
			rdsURL = rdsURL.replaceAll(COMMA, SEMI_COLON);			
			mdfProperties.put(IDC_SERVER_URL, rdsURL);
		}
		if (vsName != null) {
			mdfProperties.put(VIRTUAL_SERVER_NAME, vsName);
			mdfProperties.put(IDC_RDS_CLIENT_ID, vsName);
		}
		if (vsName != null && userPrefix != null) {
			mdfProperties.put(USER_PREFIX, userPrefix);
			mdfProperties.put(IDC_RDS_CLIENT_ID, userPrefix
					+ UNDERSCORE_SEPARATOR + vsName);
		}
		
		ServiceConfigMBeanC.getInstance().initialize(mdfProperties);

		ClientFactory.getFactory().init(ServiceConfigMBeanC.getInstance(),
				ServiceConfigMBeanC.getInstance().getRDSClientId());
		
		ReferenceDataService rds = ClientFactory.getFactory().getReferenceDataService();

		if (rds == null) {
			String msg = "RDSClientStartup. Failed to initialize RDS client";
			log.error(msg);
			throw new Exception(msg);
		}
		
		return mdfProperties;
	}

	protected void getRunTimeProperties(String userPrefix,
			Properties mdfProperties )
			throws ReferenceDataServiceException, Exception {
		ReferenceDataService rds = ClientFactory.getFactory()
				.getReferenceDataService();

		Optional<ServerRuntime> serverRuntimeOption = getServerRuntime(rds,
				mdfProperties.getProperty(VIRTUAL_SERVER_NAME));
		if (serverRuntimeOption.isPresent()) {
			ServerRuntime serverRuntime = serverRuntimeOption.get();
			int marketDataMCPort = serverRuntime.getPriceBookMCPort();
			if (marketDataMCPort > 0) {
				mdfProperties.put(MULTICAST_PORT,
						String.valueOf(marketDataMCPort));
			}

			mdfProperties.put(RemoteProvisionBuilder.DATA_CENTER_NAME, serverRuntime.getDataCenterName());

			if (userPrefix == null) {
				String prefix = serverRuntime.getUserPrefix()+ UNDERSCORE_SEPARATOR
						+ mdfProperties.getProperty(VIRTUAL_SERVER_NAME);
				mdfProperties.put(IDC_RDS_CLIENT_ID,prefix);
				mdfProperties.put(USER_PREFIX, serverRuntime.getUserPrefix());
			}
			
			mdfProperties.put(ZOO_QUORUM_STRING , serverRuntime.getZooQuorumString());
			mdfProperties.put(ZOO_NAMESPACE , serverRuntime.getClusterNamespace());

			if(StringUtils.isNotEmpty(serverRuntime.getServiceDiscoveryAddress())){
				mdfProperties.put(SD_ADDRESS, serverRuntime.getServiceDiscoveryAddress());
			}
		}else{
			throw new Exception("Unable to get server runtime configurations");
		}

	}

	@SuppressWarnings("unchecked")
	public void initMessagingClient(String userPrefix) throws Exception {

		log.info("Querying RDS for messaging configuration");

		Query query = new QueryBuilder(ClusterConfiguration.class)
				.addStringParam(QueryService.NAMESPACE_FIELD,
						MessagingConfigUtil.NAMESPACE).build();

		QueryIterator<ClusterConfiguration> queryIterator = (QueryIterator<ClusterConfiguration>) ClientFactory
				.getFactory()
				.getReferenceDataService()
				.getIterator(ClusterConfiguration.class,
						MessagingConfigUtil.NAMESPACE, query);

		List<ClusterConfiguration> clusterConfigList = new ArrayList<ClusterConfiguration>();

		while (queryIterator.hasNext()) {
			clusterConfigList.add(queryIterator.next());
		}

		log.info("Initializing the Messaging Configuration");

		// initialize the messaging configuration
		MessagingConfiguration.getInstance()
				.init(userPrefix, clusterConfigList);
	}

	private Optional<ServerRuntime> getServerRuntime(ReferenceDataService rds,
			String vsName) throws ReferenceDataServiceException {
		ServerRuntimeQuery runtimeQuery = new ServerRuntimeQuery(vsName);
		List<OracleEntity> runtime = rds.process(runtimeQuery);
		if (!runtime.isEmpty()) {
			return Optional.of((ServerRuntime) runtime.get(0));
		}
		return Optional.empty();
	}

}
