package com.integral.mdf.cluster;

import com.integral.alert.AlertLoggerFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.rmq.RMQMessageGateway;
import com.integral.services.cluster.notification.OnlineOffLineTransitionHandler;

public class OnDemandNodeStateTransitionHandler implements OnlineOffLineTransitionHandler {

    private final Log log = LogFactory.getLog(this.getClass());

    private RMQMessageGateway rmqMessageGateway;

    public OnDemandNodeStateTransitionHandler(RMQMessageGateway rmqMessageGateway) {
        this.rmqMessageGateway = rmqMessageGateway;
    }

    public void onBecomeOnline(String resourceName, String partitionId) {

        log.info("Partition becomes ONLINE for RESOURCE " + resourceName + " PARTITION " + partitionId);
        try {
            int queueNumber = Integer.parseInt(partitionId);

            //Pre-load the subscription request cache for this partition and start aggregation
            //loader.updateCache(partitionId,"load");

            //Start listening to queue here
            rmqMessageGateway.startListener(queueNumber);
        } catch (Exception e) {
            String message = "Error starting queue listener for RESOURCE " + resourceName + " PARTITION " + partitionId;
            log.error(message, e);
            AlertLoggerFactory.getMessageLogger().log("CLUSTER-PARTITION-ONLINE", OnDemandNodeStateTransitionHandler.class.getName(),
                    "Failed during Online transition for RESOURCE " + resourceName + " PARTITION " + partitionId, message);
            //helix built-in retry mechanism should kick in
            throw new RuntimeException(e);
        }
    }


    public void onBecomeOffline(String resourceName, String partitionId) {
        log.info("Partition becomes OFFLINE for RESOURCE " + resourceName + " PARTITION " + partitionId);
        try {
            int queueNumber = Integer.parseInt(partitionId);
            rmqMessageGateway.stopListener(queueNumber);

            //un-load the subscription request cache for this partition and stop aggregation
            //loader.updateCache(partitionId, "unload");

           // checkForBuffersFlush(queueNumber);
        } catch (Exception e) {
            String message = "Error stopping queue listener for RESOURCE " + resourceName + " PARTITION " + partitionId;
            log.error(message, e);
            AlertLoggerFactory.getMessageLogger().log("CLUSTER-PARTITION-OFFLINE", OnDemandNodeStateTransitionHandler.class.getName(),
                    "Failed during Offline transition for RESOURCE " + resourceName + " PARTITION " + partitionId, message);
            //helix built-in retry mechanism should kick in
            throw new RuntimeException("Unable to unbind queue:" + partitionId);
        }
    }

    /*private void checkForBuffersFlush(int queueNumber) {
        long waitTimeInMillis = PositionServerConfig.Instance.getWaitTimeInMillis();
        long sleepTime = PositionServerConfig.Instance.getSleepTimeInNs();
        log.info("Will wait for buffers to get flushed or max wait time(ms):" + waitTimeInMillis);

        long startTime = System.currentTimeMillis();
        boolean isBuffersFlushed;
        boolean isTimedOut;
        do {
            LockSupport.parkNanos(sleepTime);
            isBuffersFlushed = rmqNotificationReferenceService.isBuffersFlushed(queueNumber);
            isTimedOut = waitTimeInMillis < (System.currentTimeMillis() - startTime);
        } while ((!isBuffersFlushed) && (!isTimedOut));
        log.info("Clean up completed for the queue number:" + queueNumber + ",Buffers flushed:" + isBuffersFlushed + ",timedOut:" + isTimedOut);
        if (!isBuffersFlushed) {
            rmqNotificationReferenceService.drainBuffers(queueNumber);
        }
    }*/

}
