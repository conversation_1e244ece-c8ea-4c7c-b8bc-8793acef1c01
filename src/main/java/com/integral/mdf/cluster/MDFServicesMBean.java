package com.integral.mdf.cluster;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Properties;

import com.integral.services.config.ServicesMBean;

public class MDFServicesMBean implements ServicesMBean {

	private static final String VIRTUAL_SERVER_NAME = "virtualServerName";
	private static final String ZOO_QUORUM_STRING = "zooQuorumString";
	private static final String ZOO_NAMESPACE = "zooNameSpace";
	private static final int DEFAULT_ZOO_KEEPER_CONNECTION_TIMEOUT_MILLISECONDS = 15000;
	private static final int DEFAULT_ZOO_KEEPER_SESSION_TIMEOUT_MILLISECONDS = 2000;
	
	private String address = getLocalServerName();
	private Properties properties;

	public MDFServicesMBean(Properties properties) {
		this.properties = properties;
	}

	@Override
	public String getAddress() {
		return address;
	}

	@Override
	public int getConnectionTimeoutMilliseconds() {
		return DEFAULT_ZOO_KEEPER_CONNECTION_TIMEOUT_MILLISECONDS;
	}

	@Override
	public int getDefaultServicePort() {
		return 0;
	}

	@Override
	public ServiceTransport getDefaultServiceTransport() {
		return ServiceTransport.NONE;
	}

	@Override
	public String getHostName() {
		return address;
	}

	@Override
	public String getInstanceId() {
		return properties.getProperty(VIRTUAL_SERVER_NAME);
	}

	@Override
	public String getServicesNamespace() {
		return properties.getProperty(ZOO_NAMESPACE);
	}

	@Override
	public String getZookeeperConnectString() {
		return properties.getProperty(ZOO_QUORUM_STRING);
	}

	@Override
	public int getZookeeperSessionTimeoutMilliseconds() {
		return DEFAULT_ZOO_KEEPER_SESSION_TIMEOUT_MILLISECONDS;
	}

	@Override
	public boolean isSASLAuthEnabled() {
		return false;
	}

	@Override
	public boolean isTestMode() {
		return false;
	}

	public static String getLocalServerName() {
		String hostname = null;
		try {
			hostname = InetAddress.getLocalHost().getHostName();
		} catch (UnknownHostException e) {
			// log.warn("ServicesMBean: ServerHostAddress is not found");
		}
		return hostname;
	}
}
