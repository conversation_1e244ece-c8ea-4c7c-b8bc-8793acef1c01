package com.integral.mdf.cluster;

import java.util.Optional;

import com.integral.alert.AlertLoggerFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.data.FIProvision;
import com.integral.mdf.data.ServerProvision;
import com.integral.mdf.proivision.MDFProvisionDataService;
import com.integral.mdf.rate.RateDistributionManager;
import com.integral.services.cluster.mapped.notification.OnlineOffLineMappedTransitionHandler;

public class ListenerStateTransitionHandler implements	OnlineOffLineMappedTransitionHandler{

	private final Log log = LogFactory.getLog(this.getClass());
	
	private MDFProvisionDataService provisionDataService = MDFProvisionDataService.getInstance();
	
	private RateDistributionManager dm;	
	
	public ListenerStateTransitionHandler(RateDistributionManager dm){
		this.dm = dm;
	}

	public void onBecomeOnline(String venueName, String fiOrgName) {
		log.info("FI becomes ONLINE :" + fiOrgName);
		
		try {
			boolean result = provisionDataService.provision(fiOrgName);
			if(!result){
				log.warn("failed to get reference data for FI" + fiOrgName);
				AlertLoggerFactory.getMessageLogger().log("ParittionOnline",
						"ListenerStateTransitionHandler.onBecomeOnline.FIProvision",
						"failed to get reference data for FI","fi="+fiOrgName);
				return;
			}

			log.info("Provisioned FI:" + fiOrgName);
			
			ServerProvision serverProvision = provisionDataService.getServerProvision();
			Optional<Integer> orgIndex = serverProvision.getOrgIndex(fiOrgName);
			if(orgIndex.isPresent()){
				FIProvision fiProvision = serverProvision.getFIProvision(orgIndex.get());
				if(fiProvision!=null)
					dm.createRateBooks(fiProvision);
				else {
					log.warn("Failed to load FI reference data for FI=" + fiOrgName + ", index=" + orgIndex.get());
					AlertLoggerFactory.getMessageLogger().log("ParittionOnline",
							"ListenerStateTransitionHandler.onBecomeOnline.NullFIProvision",
							"failed to get reference data for FI","fi="+fiOrgName);
				}
				log.info("Completed rate books creation and activation for FI=" + fiOrgName);
			}else{
				log.warn("FI organization not present in the venue:" + fiOrgName);	
			}
		} catch (Exception e) {
			String message = "Error claiming partition for RESOURCE " + venueName + " PARTITION " + fiOrgName;
			log.error(message, e);
			// Helix built-in reassignment mechanism will kick in
			//throw new RuntimeException("Unable to start processing for FI:" + fiOrgName,e);
		}
	}

	public void onBecomeOffline(String venueName, String fiOrgName) {
		log.info("FI becomes OFFLINE :" + fiOrgName);
		
		try {
			//FI is moved to another server/removed. 
			deactivateRateBook(fiOrgName);
			log.info("deactivated all books associated with fi"+fiOrgName);
		} catch (Exception e) {
			String message = "Error renouncing the partition for RESOURCE " + venueName
					+ " PARTITION " + fiOrgName;
			log.error(message, e);
			// Helix built-in reassignment mechanism will kick in
			//throw new RuntimeException("Unable to stop processing for FI:"  + fiOrgName,e);
		}
	}

	@Override
	public void dropped(String venueName, String fiOrgName) {
		log.info("FI becomes DROPPED :" + fiOrgName);
		
		try {
			//FI is removed form the venue.  
			deactivateRateBook(fiOrgName);
		} catch (Exception e) {
			String message = "Error renouncing the partition for RESOURCE " + venueName
					+ " PARTITION " + fiOrgName;
			log.error(message, e);
			// Helix built-in reassignment mechanism will kick in
			throw new RuntimeException("Unable to stop processing for FI:"  + fiOrgName,e);
		}
	}

	protected void deactivateRateBook(String fiOrgName) {
		ServerProvision serverProvision = provisionDataService.getServerProvision();
		if(null!=serverProvision && fiOrgName!=null){
			Optional<Integer> orgIndex = serverProvision.getOrgIndex(fiOrgName);
			if(orgIndex.isPresent()){
				FIProvision fiProvision = serverProvision.getFIProvision(orgIndex.get());
				if(null!=fiProvision){
					dm.removeRateBooks(fiProvision);
					fiProvision.reset();
				}
			}else{
				log.warn("FI organization not present in the venue:" + fiOrgName);	
			}
		}
		log.info("Completed the unprovisioning for the FI:" + fiOrgName);
	}

}