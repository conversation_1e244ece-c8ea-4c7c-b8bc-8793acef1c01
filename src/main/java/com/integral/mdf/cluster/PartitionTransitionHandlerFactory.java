package com.integral.mdf.cluster;

import com.integral.mdf.rate.RateDistributionManager;
import com.integral.services.cluster.mapped.notification.OnlineOffLineMappedTransitionHandler;
import com.integral.virtualserver.MDFPartitionType;

public class PartitionTransitionHandlerFactory {

    public static OnlineOffLineMappedTransitionHandler createHandler(RateDistributionManager dm,MDFPartitionType partitionType) {
        switch (partitionType){
            case ORG: return new ListenerStateTransitionHandler(dm);
            case BOOK: return new PriceBookTaskTransitionHandler(dm);
            case CURRENCYPAIR:
            default: return new OnlineOffLineMappedTransitionHandler() {
                @Override
                public void dropped(String resourceName, String partitionId) {

                }

                @Override
                public void onBecomeOnline(String resourceName, String partitionId) {

                }

                @Override
                public void onBecomeOffline(String resourceName, String partitionId) {

                }
            };
        }
    }

}
