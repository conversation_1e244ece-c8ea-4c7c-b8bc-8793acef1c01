package com.integral.mdf.cluster;

import java.util.Collections;
import java.util.Properties;

import com.integral.model.cluster.ClusterMetaData;
import com.integral.rds.client.ClientFactory;
import com.integral.rds.client.ReferenceDataService;
import com.integral.services.cluster.ClusterManager;
import com.integral.services.cluster.ClusterManagerFactory;
import com.integral.services.cluster.mapped.notification.OnlineOffLineMappedTransitionHandler;
import com.integral.services.cluster.notification.OnlineOffLineTransitionHandler;

public class ClusteringService {
	
	private static final String CLUSTERING_SERVICE_JOIN_CLUSTER = "ClusteringService.joinCluster():";

	private static final String NAMESPACE_DELIMITTER = "-";
	
	private ClusterManagerFactory factory = ClusterManagerFactory.getInstance();
	private ClusterManager clusterManager;
	
	public static ClusteringService service = new ClusteringService();
	
	public static ClusteringService getInstance(){
		return service;
	}
	
	public boolean joinCluster(Properties properties,String clusterName, OnlineOffLineTransitionHandler handler
			) throws Exception{
		
		ReferenceDataService rds = ClientFactory.getFactory().getReferenceDataService();
		
		if (rds == null) {
			throw new Exception("RDS Client not initialized");
		}
		
		MDFServicesMBean mdfServicesMBean = new MDFServicesMBean(properties);
		String nameSpacedClusterName = mdfServicesMBean.getServicesNamespace() + NAMESPACE_DELIMITTER + clusterName;		
		ClusterMetaData clusterMetaData = (ClusterMetaData) rds.retrieveById(ClusterMetaData.class, nameSpacedClusterName,ClusterMetaData.NAMESPACE);
		
		if(null == clusterMetaData){
			throw new Exception (CLUSTERING_SERVICE_JOIN_CLUSTER+"Failed to join cluster.Unable to get cluster metadata:" + nameSpacedClusterName); 
		}
		//init manager 
		clusterManager = factory.createClusterManager(mdfServicesMBean, Collections.singletonList(clusterMetaData));
		
		//start the cluster instance
		boolean result = clusterManager.startInstance(clusterName, handler);
		
		return result;
	}
	
	public boolean leaveCluster(String clusterName) {
		if(clusterManager!=null){
			clusterManager.stopInstance(clusterName);
			return true;
		}
		return false;
	}
	

}
