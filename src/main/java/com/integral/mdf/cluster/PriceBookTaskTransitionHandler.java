package com.integral.mdf.cluster;

import com.integral.alert.AlertLoggerFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.data.FIProvision;
import com.integral.mdf.data.ServerProvision;
import com.integral.mdf.proivision.MDFProvisionDataService;
import com.integral.mdf.rate.RateDistributionManager;
import com.integral.services.cluster.mapped.notification.OnlineOffLineMappedTransitionHandler;

import java.util.Optional;

public class PriceBookTaskTransitionHandler implements	OnlineOffLineMappedTransitionHandler{

	private final Log log = LogFactory.getLog(this.getClass());

	private MDFProvisionDataService provisionDataService = MDFProvisionDataService.getInstance();

	private RateDistributionManager dm;

	public PriceBookTaskTransitionHandler(RateDistributionManager dm){
		this.dm = dm;
	}

	public void onBecomeOnline(String venueName, String bookName) {
		log.info("PriceBook becomes ONLINE :" + bookName);
		
		try {
			String[] components = bookName.split("/");
			if(components.length<3){
				log.warn("Unable to process partition. bookName=" + bookName + ", venue/resource="+venueName);
				//throw new RuntimeException("Unable to process partition. bookName=" + bookName);
				AlertLoggerFactory.getMessageLogger().log("ParittionOnline",
						"PriceBookTaskTransitionHandler.onBecomeOnline",
						"Invalid partition name","book="+bookName+", venue="+venueName);
				return;
			}
			String org = components[0];
			String ccyp = components[1] + '/' + components[2];

			boolean result = provisionDataService.provisionIfNot(org);
			if(!result){
				log.warn("failed to get reference data for FI" + org + ", ccyp="+ccyp);
				AlertLoggerFactory.getMessageLogger().log("ParittionOnline",
						"ListenerStateTransitionHandler.onBecomeOnline.FIProvision",
						"failed to get reference data for FI","fi="+org+ ", ccyp="+ccyp);
				return;
			}

			ServerProvision serverProvision = provisionDataService.getServerProvision();
			Optional<Integer> orgIndex = serverProvision.getOrgIndex(org);
			Optional<Integer> cpIdx = serverProvision.getCcyPairIndex(ccyp);

			if(orgIndex.isPresent()){
				FIProvision fiProvision = serverProvision.getFIProvision(orgIndex.get());
				if(cpIdx.isPresent() && fiProvision != null)
					dm.createRateBook(fiProvision,cpIdx.get());
				else {
					log.info("ccypair index not found. ccyp=" + ccyp);
					log.warn("ccupIdc not present or Failed to load FI reference data for FI=" + org
							+ ", index=" + orgIndex.get()
							+ ", ccyp=" + ccyp
							+ ", ccypIdx="+cpIdx);
					AlertLoggerFactory.getMessageLogger().log("ParittionOnline",
							"ListenerStateTransitionHandler.onBecomeOnline.NullFIProvision",
							"failed to get reference data for FI","fi="+org);
				}
			}else{
				log.warn("FI organization not present in the book=" + bookName
						+ ", venue="+venueName+", org="+org+", ccyp="+ccyp);
			}
			log.info("Completed rate books creation and activation for the book=" + bookName);
		} catch (Exception e) {
			String message = "Error claiming partition for RESOURCE=" + venueName + ", PARTITION=" + bookName;
			log.error(message, e);
			// Helix built-in reassignment mechanism will kick in
			//throw new RuntimeException("Unable to start processing for book=" + bookName,e);
		}
	}

	public void onBecomeOffline(String venueName, String bookName) {
		log.info("partition state changed to OFFLINE. partition=" + bookName + ", resource="+venueName);
		try {
			//book is moved to another server/removed.
			deactivateRateBook(bookName);
		} catch (Exception e) {
			String message = "onBecomeOffline() Error renouncing the partition for RESOURCE " + venueName
					+ " PARTITION " + bookName;
			log.error(message, e);
			// Helix built-in reassignment mechanism will kick in
			//throw new RuntimeException("Unable to stop processing for FI:"  + bookName,e);
		}
	}

	@Override
	public void dropped(String venueName, String bookName) {
		log.info("partition state changed to DROPPED. partition=" + bookName + ", resource="+venueName);
		
		try {
			//FI is removed form the venue.  
			deactivateRateBook(bookName);
		} catch (Exception e) {
			String message = "dropped() Error renouncing the partition for RESOURCE " + venueName
					+ " PARTITION " + bookName;
			log.error(message, e);
			// Helix built-in reassignment mechanism will kick in
			//throw new RuntimeException("Unable to stop processing for FI:"  + bookName,e);
		}
	}

	protected void deactivateRateBook(String bookName) {
		ServerProvision serverProvision = provisionDataService.getServerProvision();
		if(null!=serverProvision && bookName!=null){
			String[] components = bookName.split("/");
			String fiOrgName = components[0];
			String ccyp = components[1] + '/' + components[2];
			Optional<Integer> cpIdx = serverProvision.getCcyPairIndex(ccyp);
			Optional<Integer> orgIndex = serverProvision.getOrgIndex(fiOrgName);
			if(orgIndex.isPresent()){
				FIProvision fiProvision = serverProvision.getFIProvision(orgIndex.get());
				if(null!=fiProvision){
					if(cpIdx.isPresent())
						dm.removeRateBook(fiProvision,cpIdx.get(), fiProvision.getAggregationType());
					else
						log.info("deactivateRateBook - ccyp index not found. cp="+ccyp);
					//fiProvision.reset();
				}else{
					log.info("deactivateRateBook - FI provision not found. fi="+fiOrgName);
				}
			}else{
				log.warn("FI organization's index not found. name=" + fiOrgName);
			}
		}
		log.info("Completed un-provisioning for the book=" + bookName);
	}

}