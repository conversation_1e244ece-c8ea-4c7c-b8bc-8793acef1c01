package com.integral.mdf;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.cluster.ClusteringService;

public class ClusterInstanceShutdown implements ShutdownTask {

	private String clusterName;
	
	private final Log log = LogFactory.getLog(this.getClass());

	public ClusterInstanceShutdown(String clusterName) {
		this.clusterName = clusterName;
	}

	public boolean shutdown() {
		log.info("Leaving the cluster:" + clusterName);
		ClusteringService clusteringHelperService = ClusteringService.getInstance();
		clusteringHelperService.leaveCluster(this.clusterName);		
		return true;
	}

}
