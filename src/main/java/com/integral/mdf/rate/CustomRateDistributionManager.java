package com.integral.mdf.rate;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.data.FIProvision;
import com.integral.mdf.data.ServerProvision;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;


public class CustomRateDistributionManager extends RateDistributionManager{
    private static final Log log = LogFactory.getLog(CustomRateDistributionManager.class);
    private final ConcurrentHashMap<Long, RateBook> customRateBooks = new ConcurrentHashMap<>();
    public CustomRateDistributionManager(ServerProvision serverProvision) throws IOException {
        super(serverProvision);
    }

    public Optional<RateBook> getRateBook(long requestId) {
        return Optional.ofNullable(customRateBooks.get(requestId));
    }

    protected RateBook getOrCreateRateBook(long requestId, FIProvision fi, int cpIdx, double[] tiers, List<String> providers, double requestedSize, boolean isTermCcyAgg){

        RateBook rateBook = customRateBooks.get(requestId);

        //Create if there's no associated rate book for the key.
        if (rateBook == null) {
            RateBook newRateBook = newRateBook(fi,cpIdx, tiers, providers, requestedSize, isTermCcyAgg, requestId);
            rateBook = customRateBooks.putIfAbsent(requestId, newRateBook);
            if (rateBook == null) {
                rateBook = newRateBook;
            }
        }

        // add the FI index to the active indices
        activeFIOrgIndices.add(fi.getIndex());

        return rateBook;
    }
    private RateBook newRateBook(FIProvision fi, int cpIdx, double[] tiers, List<String> providers, double requestedSize, boolean isTermCcyAgg, long requestId) {
        RateProcessor rp = rateProcessors[getRateProcessorIndex(cpIdx)];
        if(serverProvision.isAggregationTimeSliced()){
            return new TimeSlicedRateBook(cpIdx,fi,serverProvision,rp, tiers, providers, requestedSize, isTermCcyAgg, requestId, true);
        }else{
            return new RateBook(cpIdx, fi, serverProvision,rp, tiers, providers, requestedSize, isTermCcyAgg, requestId, true);
        }
    }

    public RateBook createRateBook(String requestId, FIProvision fiProvision, int cpIdx, double[] tiers, List<String> providers, double requestedSize, boolean isTermCcyAgg) {

        //create or get existing rate book for FI & cpIdx
        long reqIdLong = Long.parseLong(requestId); //checked in the isValidRequest method
        RateBook rateBook = getOrCreateRateBook(reqIdLong, fiProvision, cpIdx, tiers, providers, requestedSize, isTermCcyAgg);

        //add for credit update index
        addToCreditIndex(fiProvision,rateBook);

        //add the rate book to every rate channels (provisioned LP's) which supports this currency pair
        addToRateChannel(fiProvision,rateBook);

        startRateSource(cpIdx);

        return rateBook;
    }

    public boolean deactivateRateBook(long requestId){
        Optional<RateBook> book = getRateBook(requestId);
        if(!book.isPresent()) return false;

        //stop rate publication
        book.get().deActivate();
        //remove last cached rate
        book.get().withdrawAllLastRates();
        log.info("deactivated ratebook=" + book.get().getBookKey());
        return true;
    }
}
