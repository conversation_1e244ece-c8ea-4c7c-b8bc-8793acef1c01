package com.integral.mdf.rate;

import com.integral.commons.Counter;
import com.integral.commons.pool.ObjectPool;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.PriceBookSink;
import com.integral.mdf.Util;
import com.integral.mdf.data.PriceBook;
import com.integral.mdf.data.ProvisionedQuoteC;
import com.integral.mdf.data.QuoteC;
import com.integral.pipeline.metrics.Metrics;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.commons.pool.MPMCObjectPool;
import com.integral.mdf.data.QuotePoolFactory;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicReference;

public class RateChannel {

	final public AtomicReference<QuoteC> rawRate = new AtomicReference<QuoteC>(null);
    final ObjectPool<QuoteC> rawRatePool;

    final int streamIndex;
	final public ChannelMetrics cm;
	final long key;
	final int ccypidx;
	volatile boolean isActive = true;
	List<RateBook> books = new CopyOnWriteArrayList<RateBook>();
	final int processorId;
	final RateProcessor rateProcessor;

    //array index inside taskset. Taskset is processed by rate processor
    final private int tasksetPosition;
    final Log log = LogFactory.getLog(this.getClass());
    final Log inActiveLog = LogFactory.getLog("rates.inactive");

    public RateChannel(String mKey, int streamIndex, int bcIdx, int tcIdx, RateProcessor processor) {
		this.streamIndex = streamIndex;
		this.rateProcessor = processor;
        this.processorId = processor.getProcessorId();
		ccypidx = Util.getCurrencyPairIndex(bcIdx, tcIdx); 
		key = Util.getRateChannelKey(streamIndex, bcIdx, tcIdx);
		cm = new ChannelMetrics(mKey);
		MetricsManager.instance().register(cm);
        this.rawRatePool = new MPMCObjectPool<QuoteC>(new QuotePoolFactory(), 5, 5 , true);
        tasksetPosition = rateProcessor.assignTaskId(this);
	}

	public long getKey() {
		return key;
	}

	public int getStreamIndex() {
		return streamIndex;
	}

	public int getCcypIdx(){
		return this.ccypidx;
	}
	
	public List<RateBook> getRateBooks(){
		return books;
	}
	
	public void addRatebook(RateBook book){
		for (RateBook rateBook : books) {
			if(rateBook.equals(book)){
				//already added .Assuming book need rates , so activating book here.
				book.activate();
				return;
			}
		}
		books.add(book);
		log.info("rc.addRateBook :Added ratebook=" + book.getBookKey() + ", to channel name=" + cm.mKey + ", key=" + key);
	}
	
	/**
	 * 
	 * @param fiIndex
	 * @param ccyPairIdx
	 * @return instance of book which was removed or null if book is not found.
	 */
	public RateBook removeRatebook(int fiIndex,Integer ccyPairIdx, int aggregationType){
		RateBook book = getRatebook(fiIndex, ccyPairIdx, aggregationType);
		if(book!=null){
			books.remove(book);
		}
		return book;
	}
	
	public RateBook getRatebook(int fiIndex,Integer ccyPairIdx,int aggregationType){
		for (RateBook rateBook : books) {
			if(rateBook.getFIIndex()== fiIndex && ccyPairIdx.equals(rateBook.getCcypIdx())
			&& aggregationType == rateBook.aggregationType){
				return rateBook;
			}
		}
		return null;
	}
	
	public boolean isActive() {
		return isActive;
	}
	
	public void activate(){
		this.isActive = true;
	}
	
	public void inactivate(){
		this.isActive = false;
	}
	
	public static class ChannelMetrics implements Metrics {
		public int recv;
		public int inactive;
		public int dropped;
		private String mKey;
		final Counter trsmtime = new Counter("trsm");

		public ChannelMetrics(String mKey) {
			this.mKey =mKey;
		}

		public void recordTransmitTime(long l) {
			trsmtime.record(l);
		}

		@Override
		public StringBuilder report() {
			StringBuilder message = new StringBuilder(64);
			message.append(mKey);
			message.append(", rec=").append(recv);
			message.append(", drp=").append(dropped);
			message.append(", ina=").append(inactive);
			message.append(", ").append(trsmtime.toString());
			
			this.dropped =0;
			this.recv =0;
			this.inactive =0;
			trsmtime.reset();
			return message;
		}
	}

	/**
	 * Called by I/O thread
	 * @param rate
	 */
	public int receiveRate(QuoteC rate) {

        QuoteC local = rawRatePool.borrowObject();
        local.readFrom(rate);

        if(isInActive(local)){
        	inActiveLog.info(local.toString());
		}

        QuoteC old = rawRate.getAndSet(local);
        if (old != null) {
            //Return the original quote back to pool
            rawRatePool.returnObject(old);
            cm.dropped++;
        }else{
            this.rateProcessor.getRateUpdateChangeSet().set(tasksetPosition);
        }
	    return processorId;
    }

    boolean isInActive(QuoteC local){
		int bidTiers = local.getBidTiersNum();
		int offerTiers = local.getOfferTiersNum();
		if(bidTiers < 1 &&  offerTiers < 1){
			return true;
		}

		if(local.getShowQty(local.tierOffset(QuoteC.BUY, 0)) == 0.00
				&& local.getShowQty(local.tierOffset(QuoteC.SELL, 0)) == 0.00 ){
			return true;
		}

		if(local.getPrice(local.tierOffset(QuoteC.BUY, 0)) == 0.00
				&& local.getPrice(local.tierOffset(QuoteC.SELL, 0)) == 0.00 ){
			return true;
		}
		return false;
	}


    /*
        Called by RateProcessor Thread
     */
    public void updateRateBooks(PriceBookSink sink, RateProcessor.RateProcessorMetrics rpm) {

        QuoteC quote = rawRate.getAndSet(null);
        if(quote!=null){
            try{
                for(int i=0;i<books.size();i++){
                    RateBook aBook = books.get(i);
                    if(aBook.isActive()){
                        aBook.handleUpdate(quote);
                    }else{
                        aBook.bm.inactDrp++;
                    }
                }
            }finally {
                rawRatePool.returnObject(quote);
            }
        }
	}
	
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + (int) (key ^ (key >>> 32));
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		RateChannel other = (RateChannel) obj;
		if (key != other.key)
			return false;
		return true;
	}
	
	//DO NOT DELETE 
//	public void processRate(){
//		QuoteC rate = rawRate.getAndSet(null);
//		if(rate!=null){
//			for(int i=0;i<books.size();i++){
//				RateBook aBook = books.get(i);
//				aBook.handleUpdate(rate);
//			}
//		}
//	}

}
