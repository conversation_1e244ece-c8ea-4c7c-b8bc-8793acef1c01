package com.integral.mdf.rate;

import com.integral.mdf.data.FIProvision;
import com.integral.mdf.data.PriceBook;
import com.integral.mdf.data.ServerProvision;

import java.util.List;

/**
 * Aggregates only at fixed interval
 */
public class TimeSlicedRateBook extends RateBook {

    //minimum wait until next update is pushed out.
    final long updateinterval;

    public TimeSlicedRateBook(Integer ccypidx, FIProvision fiProvision, ServerProvision serverProvision,
                              RateProcessor rateProcessor) {
        super(ccypidx, fiProvision, serverProvision, rateProcessor);
        this.updateinterval = fiProvision.getAggregationInterval() * 1000; //microseconds
    }
    public TimeSlicedRateBook(Integer ccypidx, FIProvision fiProvision, ServerProvision serverProvision,
                              RateProcessor rateProcessor, double[] tiers, List<String> providers, double requestedSize, boolean isTermCcyAgg, long requestId, boolean isCustomAggregator) {
        super(ccypidx, fiProvision, serverProvision, rateProcessor, tiers, providers, requestedSize, isTermCcyAgg, requestId, isCustomAggregator);
        this.updateinterval = fiProvision.getAggregationInterval() * 1000; //microseconds
    }

    public PriceBook aggregate(){

        if(System.nanoTime()-pricebook.getTimeEffectiveNano()<updateinterval){
            if(log.isDebugEnabled()) {
                log.debug("TSRB.aggregate skipped within interval. book=" + getBookKey());
            }
            return null;
        }
        return super.aggregate();
    }
}
