package com.integral.mdf.rate.treeset;

import com.integral.mdf.rate.treeset.MergeOnConflictTreeMap.ValueMergeHandler;
import com.integral.util.Tuple;

public class AddTupleValueMergeHandler implements ValueMergeHandler<Tuple<Double,Double>> {

	@Override
	public Tuple<Double, Double> merge(Tuple<Double, Double> oldVal, Tuple<Double, Double> newVal) {
		return new Tuple<>(oldVal.first,oldVal.second+newVal.second);
	}

}
