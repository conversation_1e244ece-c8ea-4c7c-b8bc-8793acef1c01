package com.integral.mdf.rate.treeset;

import com.integral.mdf.rate.treeset.MergeOnConflictTreeMap.ValueMergeHandler;

/**
 * Add both values of tuples 
 * @returns    
 * <AUTHOR>
 *
 */
public class AddAddMergeHandler implements ValueMergeHandler<DoubleInteger> {

	@Override
	public DoubleInteger merge(DoubleInteger oldVal, DoubleInteger newVal) {
		newVal.first = oldVal.first+newVal.first;
		newVal.second = oldVal.second+newVal.second;
		return newVal;
	}

}
