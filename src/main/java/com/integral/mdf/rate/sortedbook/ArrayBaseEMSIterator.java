package com.integral.mdf.rate.sortedbook;

/**
 * Created by pulkit on 11/13/14.
 */
public abstract class ArrayBaseEMSIterator<T> implements EMSIterator<T> {

    protected BookPriceArray<T> rangeSortedArray;
    protected InternalNode<T> currentNode;
    protected int currentKey;
    protected int currentIndex;
    protected int seedKey;
    protected boolean initiated;

    public void reset(){
        initiated = false;
    }

    public void remove(){
        throw new UnsupportedOperationException();
    }
}
