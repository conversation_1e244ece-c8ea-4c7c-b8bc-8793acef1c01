/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.integral.mdf.rate.sortedbook;

import com.integral.mdf.rate.treeset.MergeOnConflictTreeMap.ValueMergeHandler;

/**
 * <AUTHOR>
 */
public class ArrayNode<T> extends Node {

    private InternalNode<T> firstChild;
    
    ValueMergeHandler<T> mergeHandler;

    public ArrayNode(int key) {
        this(key,null);
    }
    
    public ArrayNode(int key, ValueMergeHandler<T> mergeHandler) {
        super(key);
        this.mergeHandler = mergeHandler;
    }

    /**
     * 
     * @param newNode
     * @return true is nodes are merged else false if nodes are chained.
     */
    public boolean addNode(InternalNode<T> newNode) {
        if (getFirstChild() == null) {
            setFirstChild(newNode);
            newNode.setParent(this);
            // resetting the previous and next reference to be on the safer side
            newNode.setPrev(null);
            newNode.setNext(null);

        } else if( mergeHandler != null ){ 
        	T oldVal = getFirstChild().getPayload();
        	T newVal = newNode.getPayload();
        	T val = mergeHandler.merge(oldVal, newVal);
        	getFirstChild().setPayload(val);
        	//no need to update count;
        	return true;
        }else {
            newNode.setNext(getFirstChild());
            newNode.setParent(this);
            getFirstChild().setPrev(newNode);
            setFirstChild(newNode);
        }
        setCount(getCount() + 1);
        return false;
    }

    /**
     * @return the firstChild
     */
    public InternalNode<T> getFirstChild() {
        return firstChild;
    }

    /**
     * @param firstChild the firstChild to set
     */
    public void setFirstChild(InternalNode<T> firstChild) {
        this.firstChild = firstChild;
    }

    public void copyFrom(ArrayNode<T> source) {
        setCount(source.getCount());
        InternalNode<T> cNode = source.getFirstChild();
        setFirstChild(cNode);
        //There might be some elements which have no child, these dont need to be processed furhter.
        if (cNode != null) {
            cNode.setParent(this);
            //setParent(this);
            //set parent on all the inner nodes as they will still be pointing to the older parent.
            while (cNode.getNext() != null) {
                cNode = cNode.getNext();
                cNode.setParent(this);
            }
        }
    }

    public void clear() {
        setCount(0);
        setFirstChild(null);
        setParent(null);
    }
}
