package com.integral.mdf.rate.sortedbook;

import java.io.PrintWriter;

import org.openjdk.jol.info.GraphLayout;
import org.openjdk.jol.vm.VM;

import com.integral.util.Tuple;

import static java.lang.System.out;

public class ObjectSize {

	public static void main(String[] args) throws Exception {
        out.println(VM.current().details());

        PrintWriter pw = new PrintWriter(System.out, true);

        Integer[] arr = new Integer[10];
        for (int i = 0; i < 10; i++) {
            arr[i] = new Integer(i);
        }
        
        int[] ints = new int[10];
        long[] longs = new long[10];
        
        long size = GraphLayout.parseInstance((Object) arr).totalSize();
        out.println("Integer[10] size =" + size);
        
        size = GraphLayout.parseInstance((Object) ints).totalSize();
        out.println("ints[10] size =" + size);

        size = GraphLayout.parseInstance((Object) longs).totalSize();
        out.println("longs[10] size =" + size);
        
        RangeSortedArray<Tuple<Double,Double>> bids = new RangeSortedArray<Tuple<Double,Double>>(1);
        size = GraphLayout.parseInstance((Object) bids).totalSize();
        out.println("RangeSortedArray<Tuple<Double,Double>>(1) empty size =" + size);

        bids = new RangeSortedArray<Tuple<Double,Double>>(1);
        bids.add(100, new Tuple<Double,Double>(1.0d,10.0d));
        size = GraphLayout.parseInstance((Object) bids).totalSize();
        out.println("RangeSortedArray<Tuple<Double,Double>>(1) full size =" + size);

        bids = new RangeSortedArray<Tuple<Double,Double>>(100);
        size = GraphLayout.parseInstance((Object) bids).totalSize();
        out.println("RangeSortedArray<Tuple<Double,Double>>(100) empty size =" + size);
        int i = 100;
        while(i > 0){
        	bids.add(i, new Tuple<Double,Double>(1.0d,10.0d));
        	i--;
        }
        out.println("count =" + bids.getCount());
        size = GraphLayout.parseInstance((Object) bids).totalSize();
        out.println("RangeSortedArray<Tuple<Double,Double>>(100) full size =" + size);

        bids = new RangeSortedArray<Tuple<Double,Double>>(1000);
        size = GraphLayout.parseInstance((Object) bids).totalSize();
        out.println("RangeSortedArray<Tuple<Double,Double>>(1000) size =" + size);
        i = 1000;
        while(i > 0){
        	bids.add(i, new Tuple<Double,Double>(1.0d,10.0d));
        	i--;
        }
        out.println("count =" + bids.getCount());
        size = GraphLayout.parseInstance((Object) bids).totalSize();
        out.println("RangeSortedArray<Tuple<Double,Double>>(1000) full size =" + size);
        
        
//        bids = new RangeSortedArray<Tuple<Double,Double>>(10000);
//        size = GraphLayout.parseInstance((Object) bids).totalSize();
//        out.println("RangeSortedArray<Tuple<Double,Double>>(10000) size =" + size);
        
//        String current = GraphLayout.parseInstance((Object) bids).toPrintable();
//        pw.println(current);
        
//        String last = null;
//        for (int c = 0; c < 100; c++) {
//            String current = GraphLayout.parseInstance((Object) bids).toPrintable();
//
//            if (last == null || !last.equalsIgnoreCase(current)) {
//                pw.println(current);
//                last = current;
//            }
//
//            System.gc();
//        }
//
//        pw.close();
    }
	
}
