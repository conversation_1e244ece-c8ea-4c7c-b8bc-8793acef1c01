package com.integral.mdf.rate.sortedbook;

/**
 * User: pulkit
 * Date: 2/28/13
 * Time: 5:49 PM
 */
public interface EMSIterator<T> {

    /**
     *
     * @return the next element or null, if none exists
     */
    T getNext();

    /**
     * This will reset the iterator and calling the getNext() will start from the beginning again.
     */
    void reset();

    /**
     * Removes the element obtained from the most recent call to getNext()
     */
    void remove();
}
