/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.integral.mdf.rate.sortedbook;

/**
 * <AUTHOR>
 */
public class InternalNode<T> extends Node<T> {

    private T payload;
    private InternalNode<T> prev;
    private InternalNode<T> next;

    public InternalNode(int key) {
        super(key);
    }

    /**
     * @return the payload
     */
    public T getPayload() {
        return payload;
    }

    /**
     * @param payload the payload to set
     */
    public void setPayload(T payload) {
        this.payload = payload;
    }

    /**
     * @return the prev
     */
    public InternalNode<T> getPrev() {
        return prev;
    }

    /**
     * @param prev the prev to set
     */
    public void setPrev(InternalNode<T> prev) {
        this.prev = prev;
    }

    /**
     * @return the next
     */
    public InternalNode<T> getNext() {
        return next;
    }

    /**
     * @param next the next to set
     */
    public void setNext(InternalNode<T> next) {
        this.next = next;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof InternalNode)) return false;

        InternalNode that = (InternalNode) o;

        if (payload != null ? !payload.equals(that.payload) : that.payload != null) return false;

        return true;
    }

    public void reset() {
        setNext(null);
        setPayload(null);
        setCount(0);
        setNext(null);
        setParent(null);
        setKey(-1);
    }
}
