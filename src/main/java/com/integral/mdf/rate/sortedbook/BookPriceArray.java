package com.integral.mdf.rate.sortedbook;

/**
 * User: pulkit
 * Date: 5/28/13
 * Time: 12:15 PM
 */
public interface BookPriceArray<T> {
    /**
     * This method will have to figure out where to insert the object.
     * 1) Maintain the min/max index value.
     * 2) Maintain the min/max index key.
     *
     * @param key
     * @param object
     * @return {@link com.integral.ems.book.InternalNode} created for inserting this object into the structure.
     */
    InternalNode<T> add(int key, T object);

    @Override
    String toString();

    T getMinimum();

    T getMaximum();

    int getCount();

    boolean recalculate();

    void decrementCount();

    void incrementCount();

    /**
     * There are 3 possible cases
     * 1) This is the only element in the leaf 9 in the example on top.
     * 2) This is the leaf of a chain. e.g 2
     * 3) This is an intermediate node in the vertical list. e.g 5
     * <p/>
     * Since delete can modify the structure i.e change the max/min do call {@link #recalculate()}. This is not called
     * by default so that in case there is a batching of records deletes followed by inserts the cost is kept to a minimum.
     *
     * @param externalReference
     */
    void remove(InternalNode<T> externalReference);

    int getIndexForKey(int key);

    int getMinKey();

    int getMaxKey();

    int getMinIndex();

    int getMaxIndex();

    EMSIterator<T> createNewWorstPriceIterator(int key);

    EMSIterator<T> createNewBestPriceIterator(int key);
    
    ArrayNode<T> getMinNode();

    ArrayNode<T> getMaxNode();

    ArrayNode<T> getNodeAtIndex(int index);

}
