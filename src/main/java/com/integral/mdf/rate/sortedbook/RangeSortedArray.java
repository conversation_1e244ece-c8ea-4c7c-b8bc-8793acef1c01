package com.integral.mdf.rate.sortedbook;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.rate.treeset.MergeOnConflictTreeMap.ValueMergeHandler;

/**
 * <p/>
 * This datastructure is maintained like follows
 * <p/>
 * keys   | 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 |
 * count        0   0   2   0   4   0   0   1   1    0
 *                      |       |           |   |
 *                      3       5           8   9
 *                      |       |           |   |
 *                      3       5          null null
 *                      |       |
 *                     null     5
 *                              |
 *                              5
 *                              |
 *                             null
 * <p/>
 * minIndex:- 3
 * maxIndex:- 9
 * rowSize:- 11
 * zeroIndexValue:- 0
 * count:- 8
 */
public class RangeSortedArray<T> implements BookPriceArray<T> {

    protected static final int DEFAULT_SIZE = 4096;
    protected Log logger = null;
    protected ArrayNode<T>[] allNodes;
    //protected static Log rateLog = LogFactory.getLog(RangeSortedArray.class);

    /**
     * Size of the array.
     */
    protected int rowSize;

    /**
     * Minimum index in the array at which an element is present;
     */
    protected int minIndex;
    /**
     * Value of key which maps to zeroIndex.
     */
    protected int zeroIndexValue = -1;
    /**
     * Maximum index in the array at which an element is present.
     */
    protected int maxIndex;
    /**
     * Minimum key stored in the array at minIndex.
     */
    protected int minKey;
    /**
     * Maximum key stored in the array at maxIndex.
     */
    protected int maxKey;
    /**
     * Count of the current number of elements in the Array
     */
    protected int count;

    /**
     * This field is set when the last object at the minKey is removed.
     */
    protected boolean minRemoved;
    /**
     * This field is set when the last object at the maxKey is removed.
     */
    protected boolean maxRemoved;


    /**
     *   Lowest index which will trigger the book to resize.
     */
    protected int lowerThresholdIndex;

    /**
     *  Highest index which will trigger the book to resize.
     */
    protected int higherThresholdIndex;

    /**
     * By resizing what we mean is that the zeroIndex will be adjusted.
     * new midValue will be currentMin+currentMax/2
     * all the values will either be left shifted or right shifted.
     */

    /**
     * If a new element is added which ending up at index < zeroIndexValue*(100+lowerThresholdPercent). It will trigger resize.
     */
    protected static int lowerThresholdPercent;
    /**
     * If a new element is added which ending up at index > zeroIndexValue*(100+higherThresholdPercent). It will trigger resize.
     */
    protected static int higherThresholdPercent;
    /**
     * Storing an additional midValue for the entire ArrayRange
     */
    protected final int midValue;

    /**
     * adding this logger to record the book movements.
     */
    //private static Log rateErrorMessageLog = LogFactory.getLog( "com.integral.is.rates.error.xml" );
    private String loggerSuffix;
    
    /**
     * User can control the behavior when keys are same. if mergehandler is specified then keys will be merged into
     */
    ValueMergeHandler<T> mergeHandler;

    public RangeSortedArray() {
       this(DEFAULT_SIZE);
    }

    public RangeSortedArray(int size) {
        this(size,"");
    }
    
    public RangeSortedArray(int size,ValueMergeHandler<T> mergeHandler) {
        this(size,"",mergeHandler);
     }
    
    public RangeSortedArray(int size, String loggerSuffix) {
    	this(size,loggerSuffix,null);
    }

    public RangeSortedArray(int size, String loggerSuffix,ValueMergeHandler<T> mergeHandler) {
        logger = LogFactory.getLog(loggerSuffix);
        initSize(size);
        this.loggerSuffix = loggerSuffix;
        allNodes = new ArrayNode[rowSize];
        midValue = (rowSize / 2);

        /**
         * initialize only elements in the first row of allNodes. As ideally market movements should be within a factor of size.
         */
        for (int i = 0; i < rowSize; i++) {
            allNodes[i] = new ArrayNode<T>(i,mergeHandler);
        }
        this.mergeHandler = mergeHandler;
        initMetadata();
       // logger.info("Created Array with number Of Elements:"+rowSize);
    }

    protected void initSize(int size){
        this.rowSize = size * 3;
    }

    /* This method is to be used for enhancing the RangeSortedBook */
    protected void initMetadata(){
        lowerThresholdPercent = 20;
        higherThresholdPercent = 80;
    }

    /**
     * This method will have to figure out where to insert the object.
     * 1) Maintain the min/max index value.
     * 2) Maintain the min/max index key.
     *
     * @param key
     * @param object
     * @return {@link InternalNode} created for inserting this object into the structure.
     */
    @Override
    public InternalNode<T> add(int key, T object) {
        if(key == 0){
            throw new KeyValidationException("Key Cannot be 0");
        }
//        if(logger.isDebugEnabled()){
//            logger.debug("Adding Object with key:"+key+" to the book:"+toString());
//        }
        //TODO-BA need to pool this object
        InternalNode<T> node = new InternalNode<T>(key);
        node.setPayload(object);
        if (zeroIndexValue == -1) {
            incrementCount(); // increment the count - first entry to array.
            zeroIndexValue = key - midValue;
            allNodes[midValue].addNode(node);
            setBitAtIndex(midValue);
            minIndex = midValue;
            maxIndex = midValue;
            minKey = key;
            maxKey = key;
            lowerThresholdIndex = zeroIndexValue + (rowSize*lowerThresholdPercent)/100;
            higherThresholdIndex = zeroIndexValue + (rowSize*higherThresholdPercent)/100;
//            if(logger.isDebugEnabled())
//                logger.debug("Setting the zeroIndexValue:"+zeroIndexValue+" lowerThresholdIndex: "+lowerThresholdIndex+" higherThresholdPercent: "+higherThresholdIndex+" highestIndexValue: "+(zeroIndexValue+rowSize));
        } else {
            int index = key - zeroIndexValue;
//            if(logger.isDebugEnabled()){
//                logger.debug("Index for the record with key:"+key+" is:"+index);
//            }
            if (index >= 0 && index < rowSize) {
                boolean merged = allNodes[index].addNode(node);
                setBitAtIndex(index);
                if (index < minIndex) {
                    minIndex = index;
                    minKey = key;
                } else if (index > maxIndex) {
                    maxIndex = index;
                    maxKey = key;
                }
                if(!merged){
                	incrementCount();
                }
            } else if (index < 0 || index >= rowSize) {
                // We dont set an element at the max element
                node = null;
                //decrementCount();
                //TODO-EMS need to add the details that which rate is getting dropped here
                logger.warn("Dropping rate requested index:"+index+" current book"+this);
            }

        }

        return node;
    }

    private int findNPower2(int key) {
        int c = 0;
        for (; key > 0; key >>= 1) {
            c += key & 1;
        }
        return c;
    }

    @Override
    public String toString() {
        StringBuilder sbr = new StringBuilder();
        sbr.append("RSA - ");
        sbr.append(" minK:").append(minKey);
        sbr.append(" maxK:").append(maxKey);
        sbr.append(" minInd:").append(minIndex);
        sbr.append(" maxInd:").append(maxIndex);
        sbr.append(" midInd:").append(midValue);
        sbr.append(" midIndVal:").append(zeroIndexValue + midValue);
        sbr.append(" lrThInd:").append(lowerThresholdIndex);
        sbr.append(" hrThInd:").append(higherThresholdIndex);
        sbr.append(" zrIndVal:").append(zeroIndexValue);
        sbr.append(" rSz:").append(rowSize);
        sbr.append(" cnt:").append(count);
//        if (logger.isDebugEnabled()) {
//            for (int i = minIndex; i <= maxIndex; i++) {
//                if (allNodes[i].getCount() > 0) {
//                    sbr.append(" k:").append(i).append(" oc:").append(allNodes[i].getCount());
//                }
//            }
//        }
        return sbr.toString();
    }

    public int getMinKey() {
        if (count > 0) {
            return minKey;
        } else {
            return -1;
        }
    }

    public ArrayNode<T> getMinNode() {
        if (count > 0) {
            return allNodes[minIndex];
        } else {
            return null;
        }
    }

    public ArrayNode<T> getMaxNode() {
        if (count > 0) {
            return allNodes[maxIndex];
        } else {
            return null;
        }
    }

    @Override
    public T getMinimum() {
        if (count > 0) {
            return allNodes[minIndex].getFirstChild().getPayload();
        } else {
            return null;
        }
    }

    public int getMaxKey() {
        if (count > 0) {
            return maxKey;
        } else {
            return -1;
        }
    }

    @Override
    public T getMaximum() {
        if (count > 0) {
            return allNodes[maxIndex].getFirstChild().getPayload();
        } else {
            return null;
        }
    }

    @Override
    public int getCount() {
        return count;
    }

    @Override
    public boolean recalculate() {
//        if (logger.isDebugEnabled()) {
//            logger.debug("Current Structure:" + toString());
//        }
        if (getCount() == 0) {
//            if (logger.isDebugEnabled()) {
//                logger.debug("Resetting the book as count is 0");
//            }
            reset();
            return false;
        }
        /*
         * Recalculate the min/max index/key
         */
        if (allNodes[minIndex].getCount() == 0) {
            // there seems to be a minimum on the rightside.
            int i = minIndex + 1;
            for (; i <= maxIndex; i++) {
                if (allNodes[i].getCount() > 0) {
                    minIndex = i;
                    minKey = zeroIndexValue + i;
                    break;
                }
            }
            if (i == maxIndex && allNodes[i].getCount() == 0) {
                // There are no nodes now. reset everything
                reset();
            }
        }
        if (allNodes[maxIndex].getCount() == 0) {
            // there seems to be a maximum on the leftside.
            int i = maxIndex - 1;
            for (; i >= minIndex; i--) {
                if (allNodes[i].getCount() > 0) {
                    maxIndex = i;
                    maxKey = zeroIndexValue + i;
                    break;
                }
            }
            if (i == minIndex && allNodes[i].getCount() == 0) {
                // There are no nodes now. reset everything
                reset();
            }
        }

        if (maxKey > higherThresholdIndex || minKey < lowerThresholdIndex) {
            int oldMin = minIndex;
            int oldMax = maxIndex;
            int newMidValue = (minKey + maxKey) / 2;
            int currentMidValue = zeroIndexValue + midValue;
            int diffInZeroValue = newMidValue - currentMidValue;
            StringBuilder sbr = null;
//            if(rateErrorMessageLog.isDebugEnabled()) {
//                sbr = new StringBuilder(100);
//                sbr.append(loggerSuffix);
//                sbr.append(" Attempt move book indexes, maxInd: " + maxIndex + " hrThInd: " + higherThresholdIndex + " minInd:" + minIndex + " lrThrInd: " + lowerThresholdIndex);
//                sbr.append("-> Cur Book: " + this);
//                sbr.append("--> Cur midVal: " + currentMidValue + " new midVal:" + newMidValue + " diffInZeroVal:" + diffInZeroValue);
//                rateErrorMessageLog.debug(sbr.toString());
//            }
//            if(rateErrorMessageLog.isDebugEnabled()) {
//                sbr = new StringBuilder(100);
//                sbr.append(loggerSuffix);
//            }
            //CASE: 1)
            if (currentMidValue == newMidValue) {
//                if(rateErrorMessageLog.isDebugEnabled())
//                    sbr.append("---> Cur midVal and new midVal are same");

                return false;
            }
            //CASE: 2)
            if (maxKey > higherThresholdIndex && minKey < lowerThresholdIndex) {
//                if(rateErrorMessageLog.isDebugEnabled())
//                    sbr.append("---> minKey: " + minKey + " is < lrThInd:" + lowerThresholdIndex + " & maxKey: " + maxKey + " is > hrThInd: " + higherThresholdIndex + ", no resize");

                return false;
            }
            // We do not have to care about the ArrayIndexOutOfBound Exception as the only time it can happen is when there are elements on both sides of the threshold and for those cases we dont do anything
            if (maxKey > higherThresholdIndex) {
//                if(rateErrorMessageLog.isDebugEnabled())
//                    sbr.append("----> Mov book to h-side");

                for (int i = oldMin; i <= oldMax; i++) {
                    allNodes[i - diffInZeroValue].copyFrom(allNodes[i]);
                    copyBitAndClear(i, i - diffInZeroValue);
                    allNodes[i].clear();
                }
            } else if (minKey < lowerThresholdIndex) {
//                if(rateErrorMessageLog.isDebugEnabled())
//                    sbr.append("----> Mov book to l-side");

                for (int i = oldMax; i >= oldMin; i--) {
                    allNodes[i - diffInZeroValue].copyFrom(allNodes[i]);
                    copyBitAndClear(i, i - diffInZeroValue);
                    allNodes[i].clear();
                }
            }
//            if(rateErrorMessageLog.isDebugEnabled())
//                sbr.append(" Cur zIndVal: " + zeroIndexValue + " New zIndVal: " + (zeroIndexValue + diffInZeroValue));

            zeroIndexValue = zeroIndexValue + diffInZeroValue;

//            if(rateErrorMessageLog.isDebugEnabled())
//                sbr.append("-> Cur minInd: " + minIndex + " New minInd: " + (minKey - zeroIndexValue));

            minIndex = minKey - zeroIndexValue;

//            if(rateErrorMessageLog.isDebugEnabled())
//                sbr.append("--> Cur maxInd: " + maxIndex + " New maxInd: " + (maxKey - zeroIndexValue));

            maxIndex = maxKey - zeroIndexValue;

//            if(rateErrorMessageLog.isDebugEnabled())
//                sbr.append("---> Cur lrThInd: " + lowerThresholdIndex + " New lrThInd: " + (lowerThresholdIndex + diffInZeroValue));

            lowerThresholdIndex = lowerThresholdIndex + diffInZeroValue;

//            if(rateErrorMessageLog.isDebugEnabled())
//                sbr.append("----> Cur hrThInd: " + higherThresholdIndex + " New hrThInd: " + (higherThresholdIndex + diffInZeroValue));

            higherThresholdIndex = higherThresholdIndex + diffInZeroValue;

//            if(rateErrorMessageLog.isDebugEnabled()) {
//                sbr.append("-----> After :" + toString());
//                rateErrorMessageLog.debug(sbr.toString());
//            }
            return true;
        }
        return false;
    }

    @Override
    public void decrementCount() {
        count--;
    }

    @Override
    public void incrementCount() {
        count++;
    }

    /**
     * There are 3 possible cases
     * 1) This is the only element in the leaf 9 in the example on top.
     * 2) This is the leaf of a chain. e.g 2
     * 3) This is an intermediate node in the vertical list. e.g 5
     * <p/>
     * Since delete can modify the structure i.e change the max/min do call {@link #recalculate()}. This is not called
     * by default so that in case there is a batching of records deletes followed by inserts the cost is kept to a minimum.
     *
     * @param externalReference
     */
    @Override
    public void remove(InternalNode<T> externalReference) {
        ArrayNode<T> parentNode = (ArrayNode<T>) externalReference.getParent();

        /**
         * Set the dirty flag for min/max in case of deletion operation.
         */
        if (externalReference.getKey() == minKey && parentNode.getCount() == 1) {
            minRemoved = true;
        }
        if (externalReference.getKey() == maxKey && parentNode.getCount() == 1) {
            maxRemoved = true;
        }
        // case 1) above
        if (externalReference.getParent().getCount() == 1) {
            parentNode.setFirstChild(null);
            clearBitAtIndex(parentNode.getKey());
        } else {
            // case 2)
            if (externalReference.getNext() == null) {
                InternalNode<T> previousNode = externalReference.getPrev();
                previousNode.setNext(null);
                externalReference.setPrev(null);
            } else {
                // if the node was directly connected to the parent
                InternalNode<T> nextChild = externalReference.getNext();
                InternalNode<T> prevChild = externalReference.getPrev();
                if (prevChild == null) {
                    parentNode.setFirstChild(nextChild);
                    nextChild.setPrev(null);
                } else {
                    // hmm this is in the middle of the list, we can handle it.
                    prevChild.setNext(nextChild);
                    nextChild.setPrev(prevChild);
                }

            }
        }
        parentNode.setCount(parentNode.getCount() - 1);
        externalReference.reset();
        decrementCount();
    }

    public void reset() {
        minIndex = 0;
        maxIndex = 0;
        zeroIndexValue = -1;
        lowerThresholdIndex =-1;
        higherThresholdIndex=-1;
        minKey = 0;
        maxKey = 0;
        count = 0;
        minRemoved = false;
        maxRemoved = false;
        resetPopulationSet();
    }

    public int getMinIndex() {
        return minIndex;
    }

    public int getMaxIndex() {
        return maxIndex;
    }

    public ArrayNode<T> getNodeAtIndex(int index) {
        if (index > maxIndex || index < minIndex) {
            throw new ArrayIndexOutOfBoundsException("Access requested for index: " + index + " but max Index is:" + maxIndex);
        }
        return allNodes[index];
    }

    @Override
    public EMSIterator<T> createNewWorstPriceIterator(int key) {
        return new WorstPriceIterator<T>(this, key);
    }

    @Override
    public EMSIterator<T> createNewBestPriceIterator(int key) {
        return new BestPriceIterator<T>(this, key);
    }


    @Override
    public int getIndexForKey(int key){
        return key - zeroIndexValue;
    }

    protected void setBitAtIndex(int index){
    }

    protected void clearBitAtIndex(int index){
    }

    public void copyBitAndClear( int fromIndex, int toIndex){
    }

    protected void resetPopulationSet(){
    }
}

