package com.integral.mdf.rate.sortedbook;

/**
 * User: pulkit
 * Date: 8/1/13
 * Time: 4:59 PM
 */
public class OutOfRangeException extends Exception {
    /**
     * index at which error occured during insertion process.
     */
    private int errorIndex = -1;

    public OutOfRangeException(int errorIndex) {
        this.errorIndex = errorIndex;
    }

    public OutOfRangeException() {
    }

    public int getErrorIndex() {
        return errorIndex;
    }
}
