package com.integral.mdf.rate.sortedbook;

/**
 * * This class goes from the best price to the worst price until the seedKey is matched.
 *
 * [1 2 3 4 5 6 7]
 * seedKey = 4, getNext will return [7 6 5 4 null]
 * seedKey =0, return [7 6 5 4 3 2 1 null]
 * seedKey = 100 return [null]
 *
 * User: pulkit
 * Date: 5/28/13
 * Time: 5:22 PM
 */
public class BestPriceIterator<T> extends ArrayBaseEMSIterator<T> implements EMSIterator<T> {

    public BestPriceIterator(BookPriceArray<T> array, int key) {
        this.rangeSortedArray = array;
        this.seedKey = key;
    }

    @Override
    public T getNext() {
        if (!initiated) {
            /**
             * Need to decide whether this check should happen everytime or the firstime.
             * rangeSortedArray can be null in a case when the historical matching is disabled
             */
            if ( rangeSortedArray == null || rangeSortedArray.getCount() == 0 || seedKey > rangeSortedArray.getMaxKey() ) {
                return null;
            }
            currentKey = rangeSortedArray.getMaxKey();
            currentIndex = rangeSortedArray.getMaxIndex();
            currentNode = rangeSortedArray.getMaxNode().getFirstChild();
            initiated = true;
            return currentNode.getPayload();
        } else {
            /**
             * If there is a vertical chaining at currentKey
             */
            if (currentNode.getNext() != null) {
                currentNode = currentNode.getNext();
                return currentNode.getPayload();
            } else {
                /**
                 * Check next node where count is non zero
                 */
                currentKey = currentKey - 1;
                while (currentKey >= seedKey && currentIndex >rangeSortedArray.getMinIndex()) {
                    currentIndex = currentIndex - 1;
                    if (rangeSortedArray.getNodeAtIndex(currentIndex).getCount() > 0) {
                        currentNode = rangeSortedArray.getNodeAtIndex(currentIndex).getFirstChild();
                        return currentNode.getPayload();
                    }else{
                        currentKey = currentKey - 1;
                    }
                }
            }
        }
        return null;
    }
}
