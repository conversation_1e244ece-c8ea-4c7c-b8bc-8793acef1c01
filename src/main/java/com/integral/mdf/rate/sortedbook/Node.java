/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.integral.mdf.rate.sortedbook;

/**
 *
 * <AUTHOR>
 */
public abstract class Node<T> {

    private int key;
    private int count;

    private Node<T> parent;

    public Node(int key) {
        this.key = key;
    }


    /**
     * @return the key
     */
    public int getKey() {
        return key;
    }

    /**
     * @param key the key to set
     */
    public void setKey(int key) {
        this.key = key;
    }

    /**
     * @return the parent
     */
    public Node<T> getParent() {
        return parent;
    }

    /**
     * @param parent the parent to set
     */
    public void setParent(Node<T> parent) {
        this.parent = parent;
    }

    /**
     * @return the count
     */
    public int getCount() {
        return count;
    }

    /**
     * @param count the count to set
     */
    public void setCount(int count) {
        this.count = count;
    }
}
