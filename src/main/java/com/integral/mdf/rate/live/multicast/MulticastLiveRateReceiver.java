package com.integral.mdf.rate.live.multicast;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.net.SocketException;
import java.nio.ByteBuffer;
import java.util.Optional;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.data.ServerProvision;
import com.integral.mdf.rate.live.MarketRate;
import com.integral.mdf.rate.live.MarketRateC;
import com.integral.mdf.rate.live.MarketRateSerializerFactory;
import com.integral.mdf.rate.live.RateConversionCache;

public class MulticastLiveRateReceiver {

	private final int port;
	private InetAddress mcGroupAddress;
	private MulticastSocket multicastSocket;
	private Thread receiverThread;
	private ByteBuffer messageBuffer;
	
	private static final String LR_MC_READER = "liverate-mc-reader";
	
	static Log log = LogFactory.getLog(MulticastLiveRateReceiver.class);
	
	private MarketRateSerializerFactory factory = MarketRateSerializerFactory.instance();
	
	private  RateConversionCache cache = RateConversionCache.getInstance(); 
	
	public MulticastLiveRateReceiver(ServerProvision sp){
		this.port = sp.getLiveRateMulticastPort();
		Optional<InetAddress> multicastgroup = sp.getLiveRateMulticastgroup();
		if (!multicastgroup.isPresent()) {
			log.info("Not joining the Live rate multi cast group since there's no multicast group is defined");
			return;
		}
		this.mcGroupAddress = multicastgroup.get();
		messageBuffer = ByteBuffer.allocate(sp.getLiveRateMessageSize());
		start();
	}
	
	private void start() {
        try {
            multicastSocket = new MulticastSocket(port);
            multicastSocket.joinGroup(mcGroupAddress);
            receiverThread = new Thread(new SocketListenerWorker(),LR_MC_READER);
            receiverThread.start();
            log.info("MulticastLiveRateReceiver.init():Started listening for live rates on port:"+ port+", Multicast group:"+mcGroupAddress);
        } catch (Exception ex) {
            log.error("MR Unable to join Multicast group:"+ mcGroupAddress, ex);
        }
    }
	
	 private class SocketListenerWorker implements Runnable {
		 private MarketRate rate = new MarketRateC();
		 
	        @Override
	        public void run() {
	            while (true) {
	                try {
	                    DatagramPacket dp = new DatagramPacket(messageBuffer.array(), messageBuffer.capacity());
	                    multicastSocket.receive(dp);
	                        if (log.isDebugEnabled()) {
	                            log.debug("New message received for " + dp.getAddress() + " with port " + dp.getPort() +
	                            		" with length " + dp.getLength());
	                        }
	                        byte[] data = dp.getData();
	                        int version = data[0];

	                        if ( dp.getLength() > 5) {
	                        	if( version == 2 || version == 6 || version == 10 || version == 11 || version == 13){
	                        		messageBuffer.get();//move the pointer for version
	                        		factory.getDeSerializerForVersion(version).deserialize(messageBuffer, rate);
	                        		cache.update(rate);
	                        		messageBuffer.clear();
	                        		rate.reset();
	                        	} else {
	                        		if (log.isDebugEnabled()) {
	                        			log.debug("Message dropped due to unsupported version:" + version);
	                        		}
	                        	}
	                        }else{
                        		if(log.isDebugEnabled()){
                        			log.debug("Not valid rate. version:"+version+", length:"+dp.getLength());
                        		}
	                        }
	                } catch (SocketException e) {
	                    log.error("MulticastLiveRateReceiver.SocketListenerWorker.run():SocketException receiving at: " + mcGroupAddress + ":" + port + " error: " + e.getMessage(), e);
	                    messageBuffer.clear();
	                } catch (IOException e) {
	                    log.error("MulticastLiveRateReceiver.SocketListenerWorker.run():Multicast socket receiving at: " + mcGroupAddress + ":" + port + " error: " + e.getMessage(), e);
	                    messageBuffer.clear();
	                } catch (Throwable ex) {
	                    log.error("MulticastLiveRateReceiver.SocketListenerWorker.run():Unexpected error happended", ex);
	                    messageBuffer.clear();
	                }
	            }
	        }
	    }

}
