package com.integral.mdf.rate.live;

import java.nio.ByteBuffer;

/**
 * Serializer for {@link com.integral.is.message.MarketRate market rate} objects.
 *
 * <AUTHOR>
 */
public interface MarketRateSerializer {
	
    /**
     * Quotes are the default = 0.
     */
    public static final int PRICE_TYPE_QUOTES = 0;
    
    //Masks for Market Rate flag.
    public static final int MASK_IS_QUOTEID_LONG = 1;
    public static final int MASK_IS_STALE = 1 << 1;
    public static final int MASK_IS_AGGREGATED = 1 << 2;
    public static final int MASK_IS_INCREMENTAL = 1 << 3;
    public static final int MASK_IS_HOURGLASS = 1 << 4;

    //Masks for flags within a tier.
    public static final int MASK_HAS_BID = 1;
    public static final int MASK_HAS_OFFER = 1; //same as HAS_BID
    public static final int MASK_HAS_PROVIDER_INDEX = 1 << 1;
    public static final int MASK_HAS_PROVIDER_QUOTEID = 1 << 2;
    public static final int MASK_IS_PROVIDER_QUOTEID_LONG = 1 << 3;
    public static final int MASK_HAS_MIN_LIMIT = 1 << 4;
    public static final int MASK_IS_LIMIT_LONG = 1 << 5;
    public static final int MASK_IS_MIN_LIMIT_LONG = 1 << 6;
    public static final int MASK_IS_FLASH_ORDER = 1 << 7;

    public boolean deserialize(ByteBuffer in, MarketRate rate);

    int getProviderIndex(byte[] packet, int startOffset);

    int getStreamIndex(byte[] packet, int startOffset);

    int getBaseCcyIndex(byte[] packet, int startOffset);

    int getVarCcyIndex(byte[] packet, int startOffset);

}
