package com.integral.mdf.rate.live;

import java.io.UnsupportedEncodingException;
import java.nio.ByteBuffer;

import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 * <AUTHOR>
 */
public class V2MarketRateSerializer implements MarketRateSerializer {
    protected Log log = LogFactory.getLog(this.getClass());

    //The "|" character represented as an encoded byte.

    //    static String defaultCharset = MulticastConfigurationFactory.getMulticastMBean().getDefaultCharset();
    static String defaultCharset = "ISO-8859-1";
    //Initialize with | value
    public static byte PIPE_SEPARATOR_BYTE = new byte[]{'|'}[0];

    static {
        try {
            PIPE_SEPARATOR_BYTE = "|".getBytes(defaultCharset)[0];
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    String QUOTE_SEPARATOR = "~";

    protected int getTimeEffectiveFromPrice(MarketPrice price, MarketRate rate) {
        if (price.getTimeEffective() != 0) {   //Rate sent by adaptor would already have been set on this - if not - then serializaation would have set it during set Rate timings.
            return (int) (rate.getRateSentByAdapter() - price.getTimeEffective());
        } else {
            return 0;
        }
    }

    int toInt(byte b) {
        return b & 0xFF;
    }

    byte toByte(int i) {
        if (i > 0xFF) {
            throw new LimitBreachedException("Field Limit Breached in V2 Serializer.");
        }
        return (byte) i;
    }

    byte[] getBytesForString(String str) {
        byte[] bytes = null;
        try {
            bytes = str.getBytes(defaultCharset);
        } catch (UnsupportedEncodingException e) {
            log.error("Encoding exception while serialization str to bytes 1 ", e);
            bytes = new byte[]{};
        }
        return bytes;
    }

    String getStringForBytes(byte[] bytes) {
        String str = null;
        try {
            str = new String(bytes, defaultCharset);
        } catch (UnsupportedEncodingException e) {
            log.error("Encoding exception while serialization bytes to str 2 ", e);
        }
        return str;
    }

    protected MarketRate headerDeserialize(ByteBuffer in, MarketRate rate) {
//        int version = in.get();
        int orgIndex = in.getInt();
        int streamIndex = toInt(in.get());
        int baseCcyIndex = toInt(in.get());
        int varCcyIndex = toInt(in.get());
        //we donot do anything with Tenor yet, just a place holder.
        in.get();

        rate.setProviderIndex(orgIndex);
        rate.setStreamIndex(streamIndex);
        rate.setBaseCcyIndex(baseCcyIndex);
        rate.setVarCcyIndex(varCcyIndex);

        return rate;
    }

    protected MarketRate rateTimingsDeserialize(ByteBuffer in, MarketRate rate) {
        //Read the timings now.
        long timing1 = in.getLong();
        long timing2 = in.getLong();
        long timing3 = timing2 + in.getShort();
        rate.setRateEffective(timing1);
        rate.setRateReceivedByAdapter(timing2);
        rate.setRateSentByAdapter(timing3);

        //TODOL remove timing usage altogether, as this is redundant information.
//        rate.getTiming().setTime(ISCommonConstants.EVENT_TIME_RATE_EFFECTIVE, timing1);
//        rate.getTiming().setTime(ISCommonConstants.EVENT_TIME_DISP_ADAPTER_REC_RATE, timing2);
//        rate.getTiming().setTime(ISCommonConstants.EVENT_TIME_DISP_ADAPTER_SENT_RATE, timing3);
        return rate;
    }

    protected MarketRate rateDataDeserialize(ByteBuffer in, MarketRate rate) {

        byte marketRateFlag = in.get();

        rate.setStale((marketRateFlag & MASK_IS_STALE) != 0);
        rate.setIncremental((marketRateFlag & MASK_IS_INCREMENTAL) != 0);
        rate.setAggregated((marketRateFlag & MASK_IS_AGGREGATED) != 0);

        if ((marketRateFlag & MASK_IS_QUOTEID_LONG) == 1) {
            long quoteId = in.getLong();
            rate.setQuoteId(quoteId);
        } else {
            parseStringToDelim(in);
//            rate.setQuoteId(quoteId);
        }
        if((marketRateFlag & MASK_IS_HOURGLASS) == MASK_IS_HOURGLASS){
            rate.setPriceSubType(MarketRate.PRICE_SUBTYPE_HOURGLASS);
        }

        byte limitCcyIndex = in.get();
        rate.setLimitCcyIndex(limitCcyIndex);

        long valueDate = in.getLong();
        rate.setValueDate(valueDate);


        int priceType = in.get();
        if (priceType == -1) {
            rate.setPriceType(PRICE_TYPE_QUOTES);
        } else {
            rate.setPriceType(priceType);
        }
        return rate;
    }

    /**
     * De serialize a byte stream into a {@link com.integral.is.message.MarketRateC} instance.
     *
     * @param in
     * @param rate
     * @return
     */
    public boolean deserialize(ByteBuffer in, MarketRate rate) {
        rate = headerDeserialize(in, rate);
        rate = rateTimingsDeserialize(in, rate);
        rate = rateDataDeserialize(in, rate);

        int numTiers = toInt(in.get());
        //override to read only top tier
        numTiers = numTiers > 1 ? 1 : numTiers;

        MarketPrice prevBidPrice = null;
        MarketPrice prevOfferPrice = null;
        
        int bidTiers=0;
        int offerTiers=0;

        int maxTiers = MarketRateC.MAX_TIERS;
        if(numTiers>=maxTiers) {
			for (int i = 0; i < maxTiers; i++) {
			    byte tierFlag = in.get();
			    //Check if this tier has a bid. 0 indicates no bid price for tier.
			    if (tierFlag != 0) {
			        MarketPrice pooledPrice = rate.getBidTier(i);
			        MarketPrice bidPrice = deserializeMarketPrice(in, tierFlag, prevBidPrice, pooledPrice);
			        rate.setBidPrice(i, bidPrice);
			        prevBidPrice = bidPrice;
			        bidTiers++;
			    }
			    //offer tier flag.  0 indicates no offer price for tier.
			    tierFlag = in.get();
			    if (tierFlag != 0) {
			        MarketPrice pooledPrice = rate.getOfferTier(i);
			        MarketPrice offerPrice = deserializeMarketPrice(in, tierFlag, prevOfferPrice, pooledPrice);
			        rate.setOfferPrice(i, offerPrice);
			        prevOfferPrice = offerPrice;
			        offerTiers++;
			    }
			}
		}
        
        rate.setNoOfBidTiers(bidTiers);
        rate.setNoOfOfferTiers(offerTiers);
        //populateShortNames(rate);
        return true;
    }

    protected MarketPrice deserializeMarketPrice(ByteBuffer in, byte tierFlag, MarketPrice prevPrice, MarketPrice pooledPrice) {
        MarketPrice price = pooledPrice == null ? new MarketPriceC() : pooledPrice;
//        MarketPriceC price = new MarketPriceC( );
        if ((tierFlag & MASK_HAS_PROVIDER_INDEX) > 0) {
            int providerIndex = in.getInt();
            price.setProviderIndex(providerIndex);
        }
        if ((tierFlag & MASK_HAS_PROVIDER_QUOTEID) > 0) {
            if ((tierFlag & MASK_IS_PROVIDER_QUOTEID_LONG) > 0) {
                long quoteId = in.getLong();
                price.setProviderQuoteId(quoteId);
            } else {
                String quoteId = parseStringToDelim(in);
                price.setProviderQuoteId(quoteId);
            }
        }
        if ((tierFlag & MASK_HAS_MIN_LIMIT) > 0) {
            if ((tierFlag & MASK_IS_MIN_LIMIT_LONG) > 0) {
                price.setMinLimit(in.getLong());
            } else {
                price.setMinLimit(in.getInt());
            }
        }
        if ((tierFlag & MASK_IS_LIMIT_LONG) > 0) {
            price.setLimit(in.getLong());
        } else {
            price.setLimit(in.getInt());
        }
        price.setTotalLimit( price.getLimit() );

        if ((tierFlag & MASK_IS_FLASH_ORDER) > 0) {
           price.setIsFlashOrderPrice(true);
        }

        price.setRate(in.getDouble());

        if (price.getProviderIndex() <= 0) {
            if (prevPrice != null && prevPrice.getProviderIndex() != 0) {
                price.setProviderIndex(prevPrice.getProviderIndex());
            }
        }
        if (price.getProviderQuoteId() == null) {
            if (prevPrice != null && prevPrice.getProviderQuoteId() != null) {
                price.setProviderQuoteId(prevPrice.getProviderQuoteId());
            }
        }

        if (price.getLongProviderQuoteId() <= 0) {
            if (prevPrice != null && prevPrice.getLongProviderQuoteId() != 0) {
                price.setProviderQuoteId(prevPrice.getLongProviderQuoteId());
            }
        }
        
        //populateShortNames(price);
        return price;
    }

    String parseStringToDelim(ByteBuffer in) {

        int startPosition = in.position();
        int endPosition = startPosition;

        while (in.hasRemaining()) {
            if (in.get() == PIPE_SEPARATOR_BYTE) {
                //at this point, position is at the byte after the "|" character.
                //rewind it by 1.
                endPosition = in.position() - 1;
                break;
            }
        }

        //Just a null string.
        if (endPosition == startPosition) {
            return null;
        }

        //rewind to start position.
        in.position(startPosition);
        int len = endPosition - startPosition;
        byte[] b = new byte[len];
        in.get(b, 0, len);

        //Get the pipe char again, and ignore it.
        if (in.hasRemaining())
            in.get();

        return getStringForBytes(b); //new String(b, defaultCharset);
    }

    public int getProviderIndex(byte[] packet, int startOffset) {

        //skip the version byte, and start with index=1;

        int orgIndex = 0;
        orgIndex |= packet[startOffset + 1] & 0xFF;
        orgIndex <<= 8;
        orgIndex |= packet[startOffset + 2] & 0xFF;
        orgIndex <<= 8;
        orgIndex |= packet[startOffset + 3] & 0xFF;
        orgIndex <<= 8;
        orgIndex |= packet[startOffset + 4] & 0xFF;
        return orgIndex;
    }

    public int getStreamIndex(byte[] packet, int startOffset) {
        return packet[startOffset + 5] & 0xFF;
    }

    public int getBaseCcyIndex(byte[] packet, int startOffset) {
        return packet[startOffset + 6] & 0xFF;
    }

    public int getVarCcyIndex(byte[] packet, int startOffset) {
        return packet[startOffset + 7] & 0xFF;
    }

    @SuppressWarnings("serial")
	public static final class LimitBreachedException extends RuntimeException {
        public LimitBreachedException(String message) {
            super(message);
        }
    }
}