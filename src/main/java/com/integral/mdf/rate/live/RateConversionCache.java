package com.integral.mdf.rate.live;

import java.util.Optional;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import org.jctools.maps.NonBlockingHashMap;

public class RateConversionCache {

	Log log = LogFactory.getLog(this.getClass());
	private static final RateConversionCache cache = new RateConversionCache();

	private RateConversionCache() {
	}

	public static RateConversionCache getInstance() {
		return cache;
	}

	private NonBlockingHashMap<Integer,NonBlockingHashMap<Integer,Double>> rateConversionCache = new NonBlockingHashMap<Integer,NonBlockingHashMap<Integer,Double>>();

	private void update(Integer fromCcy, Integer toCcy, Double rate) {
		NonBlockingHashMap<Integer,Double> nonBlockingHashMapLong = rateConversionCache.get(fromCcy);
		if (nonBlockingHashMapLong == null) {
			NonBlockingHashMap<Integer,Double> newNonBlockingHashMapLong = new NonBlockingHashMap<Integer,Double>();
			nonBlockingHashMapLong = rateConversionCache.putIfAbsent(fromCcy,
					newNonBlockingHashMapLong);
			if (nonBlockingHashMapLong == null) {
				nonBlockingHashMapLong = newNonBlockingHashMapLong;
			}
		}
		nonBlockingHashMapLong.put(toCcy, rate);
		if(log.isDebugEnabled()){
			String r = rate!=null?rate.toString():"null";
			log.debug( "RC rate updated b="+ fromCcy + ", v=" + toCcy +  ", r=" + r );
		}
	}

	public Optional<Double> getRate(Integer fromCcy, Integer toCcy) {

		NonBlockingHashMap<Integer,Double> nonBlockingHashMapLong = rateConversionCache
				.get(fromCcy);

		if (nonBlockingHashMapLong != null) {
			Double rate = nonBlockingHashMapLong.get(toCcy);
			if(rate!=null){
				return Optional.of(rate);
			}
		}
		
		//Look up for reverse  
		nonBlockingHashMapLong = rateConversionCache.get(toCcy);
		
		if (nonBlockingHashMapLong != null) {
			Double value = nonBlockingHashMapLong.get(fromCcy);
			if(value==null)
				return Optional.empty();
			value =  1.00d/value;
			return Optional.of(value);
		}

		return Optional.empty();
	}

	public void update(MarketRate rate) {
		int bCcyIndex = rate.getBaseCcyIndex();
		int vCcyIndex = rate.getVarCcyIndex();

		double bidRate = rate.getNoOfBidTiers() > 0 ? rate.getBidRate(0) : 0.0d;
		double offerRate = rate.getNoOfOfferTiers() > 0 ? rate.getOfferRate(0) : 0.0d;

		if(bidRate > 0.0d && offerRate > 0.0d){
			double midRate = (rate.getBidRate(0) + rate.getOfferRate(0)) / 2;
			update(bCcyIndex, vCcyIndex, midRate);
		}else if(bidRate > 0.0d){
			update(bCcyIndex, vCcyIndex, bidRate);
		}else if(offerRate > 0.0d){
			update(bCcyIndex, vCcyIndex, offerRate);
		}else{
			if(log.isDebugEnabled()){
				log.debug("RC.update invalid rate b="+ bCcyIndex + ", v=" + vCcyIndex +  ", br=" + bidRate + ", or=" + offerRate );
			}
		}
	}


	public void clear(){
		log.info("RC.clear clearing rate cache. requested by " + getStacktrace());
		rateConversionCache.clear();
	}

	private String getStacktrace(){
		StackTraceElement[] elements = Thread.currentThread().getStackTrace();
		StringBuilder sb = new StringBuilder();
		for(int i=1;i<4 && elements.length > 4;i++) {
			sb.append(elements[i].getClassName());
			sb.append("@").append(elements[i].getMethodName());
			sb.append("->");
		}
		return sb.toString();
	}

}
