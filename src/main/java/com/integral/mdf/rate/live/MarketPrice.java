package com.integral.mdf.rate.live;


/**
 * Represents one side of a MarketRate = bid/offer.
 */
public interface MarketPrice {

    long getMinLimit();

    long getLimit();

    int getProviderIndex();

    double getRate();
    
    double getSpotRate();
    
    double getForwardPoints();
    
    String getProviderQuoteId();

    long getLongProviderQuoteId();

    void setMinLimit(long limit);

    void setLimit(long limit);

    void setProviderIndex(int index);

    void setRate(double rate);

    void setProviderQuoteId(String providerQuoteId);

    void setProviderQuoteId(long longProviderQuoteId);

    public long getTimeEffective();

    public void setTimeEffective( long timeEffective );
    
    public void  setSpotRate(double spotRate);
    
    public void  setForwardPoints(double forwardPoints);
    
    public void reset();

    public int getTTL();

    public void setTTL(int timeToLive);

    public boolean isFlashOrderPrice();

    public void setIsFlashOrderPrice(boolean isFlashOrderPrice);

    public boolean isRateSourceDisplayedOrder();

    public void setIsRateSourceDisplayedOrder(boolean isRateSourceDisplayedOrder);

    public boolean isRateSourcePeggedOrder();

    public void setIsRateSourcePeggedOrder(boolean isRateSourcePeggedOrder);

    public double getTotalLimit();

    public void setTotalLimit(double totLimit);
}
