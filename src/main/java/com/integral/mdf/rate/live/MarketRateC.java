package com.integral.mdf.rate.live;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;

/**
 * Implements MarketRate interface. Represents Streaming SPOT Price only.
 */
public class MarketRateC implements MarketRate, Cloneable
{
    public static final int MAX_TIERS = 1;
	public static final int BID = 0;
    /**
     * Represents a mid price, value of 1.
     */
    public static final int MID = 1;
    /**
     * Represents an offer price, value of 2.
     */
    public static final int OFFER = 2;

	public static final int PRICE_TYPE_MULTI_TIER = 1;
	
	String providerShortName;
	int providerIndex;

	// QuoteId represented as a long integer, if available.
	long longQuoteId = -1;

	String streamId;
	int streamIndex = 0;

	String baseCcy;
	int baseCcyIndex = -1;

	String varCcy;
	int varCcyIndex = -1;

	String limitCcy;
	int limitCcyIndex = -1;

	int priceType = 0;
	int priceSubType = 0;
	
	long valueDate = 0;

	// Flags
	boolean aggregated;
	boolean incremental;
	boolean stale;

	long maxBidLimit = 0;
	long maxOfferLimit = 0;

	// Tiers
	ArrayList<MarketPrice> bidPrices = new ArrayList<MarketPrice>();
	ArrayList<MarketPrice> offerPrices = new ArrayList<MarketPrice>();

	boolean isDirty = true;

	boolean bidSortingRequired = false;
	boolean offerSortingRequired = false;

	long rateRcvdByAdapter = 0;
	long rateSentByAdapter = 0;
	long rateEffectiveAtProvider = 0;

	String serverId;
	String bookname;
	String quoteId;

	// Below attributes are derived from guid. Its sequence no from adaptor.
	long sequenceNo;

	//Indicates whether the rate is Outright display order
	boolean isOutrightDisplayOrder = false;

    // this variable will be true if external providers raw rate has value date
    private transient boolean isExternalProvidersValueDate = true;
	private int noOfBidTiers;
	private int noOfOfferTiers;
	
	public MarketRateC clone()
	{
		MarketRateC clone = new MarketRateC( MAX_TIERS, MAX_TIERS );
		clone.providerShortName = this.providerShortName;
		clone.providerIndex = this.providerIndex;
		clone.longQuoteId = this.longQuoteId;
		clone.streamId = this.streamId;
		clone.streamIndex = this.streamIndex;
		clone.baseCcy = this.baseCcy;
		clone.baseCcyIndex = this.baseCcyIndex;
		clone.varCcy = this.varCcy;
		clone.varCcyIndex = this.varCcyIndex;
		clone.limitCcy = this.limitCcy;
		clone.limitCcyIndex = this.limitCcyIndex;
		clone.priceType = this.priceType;
		clone.aggregated = this.aggregated;
		clone.incremental = this.incremental;
		clone.stale = this.stale;
		clone.maxBidLimit = this.maxBidLimit;
		clone.maxOfferLimit = this.maxOfferLimit;
		clone.bidSortingRequired = this.bidSortingRequired;
		clone.offerSortingRequired = this.offerSortingRequired;
		clone.rateRcvdByAdapter = this.rateRcvdByAdapter;
		clone.rateSentByAdapter = this.rateSentByAdapter;
		clone.rateEffectiveAtProvider = this.rateEffectiveAtProvider;
        clone.sequenceNo = this.sequenceNo;
        clone.quoteId = this.quoteId;
		for ( Iterator<MarketPrice> iterator = bidPrices.iterator(); iterator.hasNext(); )
		{
			MarketPrice marketPrice = iterator.next();
			clone.bidPrices.add( new MarketPriceC( marketPrice ) );
		}

		for ( Iterator<MarketPrice> iterator = offerPrices.iterator(); iterator.hasNext(); )
		{
			MarketPrice marketPrice = iterator.next();
			clone.offerPrices.add( new MarketPriceC( marketPrice ) );
		}
        clone.isOutrightDisplayOrder = this.isOutrightDisplayOrder;
        clone.isExternalProvidersValueDate = this.isExternalProvidersValueDate;
		return clone;
	}

	public MarketRateC()
	{
		this( MAX_TIERS, MAX_TIERS );
	}

	public MarketRateC( int maxBidTiers, int maxOfferTiers )
	{
		bidPrices = new ArrayList<MarketPrice>();
		ensureTierCapacity( maxBidTiers, bidPrices );
		offerPrices = new ArrayList<MarketPrice>();
		ensureTierCapacity( maxOfferTiers, offerPrices );
	}

	public long getQuoteIdAsLong()
	{
		return longQuoteId;
	}

	public boolean isAggregated()
	{
		return aggregated;
	}

	public boolean isIncremental()
	{
		return incremental;
	}

	public String getStreamId()
	{
		return streamId;
	}

	public int getStreamIndex()
	{
		return streamIndex;
	}

	public int getBaseCcyIndex()
	{
		return baseCcyIndex;
	}

	public int getVarCcyIndex()
	{
		return varCcyIndex;
	}

	public int getLimitCcyIndex()
	{
		return limitCcyIndex;
	}

	public int getProviderIndex()
	{
		return providerIndex;
	}

	public String getBaseCcy()
	{
		return baseCcy;
	}

	public String getVariableCcy()
	{
		return varCcy;
	}

	public String getLimitCcy()
	{
		return limitCcy;
	}

	public double getBidRate()
	{
		return getBidRate( 0 );
	}

	public double getBidRate( int tier )
	{
		bidRangeCheck( tier );

		MarketPrice price = bidPrices.get( tier );
		return price.getRate();

	}

	public double getOfferRate()
	{
		return getOfferRate( 0 );
	}

	public double getOfferRate( int tier )
	{
		offerRangeCheck( tier );

		MarketPrice price = offerPrices.get( tier );
		return price.getRate();
	}

	public double getBidSpotRate()
	{
		return getBidSpotRate( 0 );
	}

	public double getBidSpotRate( int tier )
	{
		bidRangeCheck( tier );

		MarketPrice price = bidPrices.get( tier );
		return price.getSpotRate();

	}

	public double getOfferSpotRate()
	{
		return getOfferSpotRate( 0 );
	}

	public double getOfferSpotRate( int tier )
	{
		offerRangeCheck( tier );

		MarketPrice price = offerPrices.get( tier );
		return price.getSpotRate();
	}
	
	public double getBidForwardPoints()
	{
		return getBidForwardPoints( 0 );
	}

	public double getBidForwardPoints( int tier )
	{
		bidRangeCheck( tier );

		MarketPrice price = bidPrices.get( tier );
		return price.getForwardPoints();

	}

	public double getOfferForwardPoints()
	{
		return getOfferForwardPoints( 0 );
	}

	public double getOfferForwardPoints( int tier )
	{
		offerRangeCheck( tier );

		MarketPrice price = offerPrices.get( tier );
		return price.getForwardPoints();
	}
	
	public double getBidLimit()
	{
		return getBidLimit( 0 );
	}

	public double getBidLimit( int tier )
	{
		bidRangeCheck( tier );

		MarketPrice price = bidPrices.get( tier );
		return price.getLimit();

	}

	public double getMaxBidLimit()
	{
		return maxBidLimit;
	}

	public double getOfferLimit()
	{
		return getOfferLimit( 0 );
	}

	public double getOfferLimit( int tier )
	{
		offerRangeCheck( tier );

		MarketPrice price = offerPrices.get( tier );
		return price.getLimit();
	}

	public double getMaxOfferLimit()
	{
		return maxOfferLimit;
	}

	public String getProviderShortName()
	{
		return providerShortName;
	}

	public boolean isStale()
	{
		return stale;
	}

	public int getBidTierSize()
	{
		return bidPrices.size();
	}

	public int getOfferTierSize()
	{
		return offerPrices.size();
	}

	public int getPriceType()
	{
		return priceType;
	}

	public boolean isBidSortingRequired()
	{
		return bidSortingRequired;
	}

	public boolean isOfferSortingRequired()
	{
		return offerSortingRequired;
	}

	public void sort()
	{
		if ( this.getPriceType() == PRICE_TYPE_MULTI_TIER )
		{
			return;
		}

		if ( bidSortingRequired || offerSortingRequired )
		{
			isDirty = true;
		}
		if ( this.isBidSortingRequired() )
		{
			Collections.sort( bidPrices, new Desc() );
			this.bidSortingRequired = false;
		}
		if ( this.isOfferSortingRequired() )
		{
			Collections.sort( offerPrices, new Asc() );
			this.offerSortingRequired = false;
		}
	}

	private static class Asc implements Comparator<MarketPrice>
	{
		public int compare( MarketPrice o1, MarketPrice o2 )
		{
			/*
			 * broker adaptor appends zero rate tier's on offer side to match
			 * the no. of bid tiers. Hence tier with zero rate should be the
			 * last.
			 */
			if ( o1.getRate() == 0.0 )
			{
				return 1;
			}
			else if ( o2.getRate() == 0.0 )
			{
				return -1;
			}
			else
			{
				return Double.compare( o1.getRate(), o2.getRate() );
			}
		}
	}

	private static class Desc implements Comparator<MarketPrice>
	{
		public int compare( MarketPrice o1, MarketPrice o2 )
		{
			return Double.compare( o1.getRate(), o2.getRate() ) * -1;
		}
	}

	public int getNumTiers()
	{
		return bidPrices.size() > offerPrices.size() ? bidPrices.size() : offerPrices.size();
	}

	public int getNumBidTiers()
	{
		return bidPrices.size();
	}

	public int getNumOfferTiers()
	{
		return offerPrices.size();
	}

	public MarketPrice getBidTier( int tierIndex )
	{
		bidRangeCheck( tierIndex );
		return bidPrices.get( tierIndex );
	}

	public MarketPrice getOfferTier( int tierIndex )
	{
		offerRangeCheck( tierIndex );
		return offerPrices.get( tierIndex );
	}

	private void bidRangeCheck( int index )
	{
		if ( index >= bidPrices.size() )
		{
			throw new IndexOutOfBoundsException( "Index: " + index + ", Size: " + bidPrices.size() );
		}
	}

	private void offerRangeCheck( int index )
	{
		if ( index >= offerPrices.size() )
		{
			throw new IndexOutOfBoundsException( "Index: " + index + ", Size: " + offerPrices.size() );
		}
	}

	// Setters

	public void setProviderShortName( String providerShortName )
	{
		this.providerShortName = providerShortName;
	}

	public void setQuoteId( long quoteId )
	{
		this.longQuoteId = quoteId;
		this.quoteId = Long.toString( quoteId );
	}

	public void setStreamId( String streamId )
	{
		this.streamId = streamId;
	}

	public void setProviderIndex( int providerIndex )
	{
		this.providerIndex = providerIndex;
	}

	public void setStreamIndex( int streamIndex )
	{
		this.streamIndex = streamIndex;
	}

	public void setBaseCcyIndex( int baseCcyIndex )
	{
		this.baseCcyIndex = baseCcyIndex;
	}

	public void setVarCcyIndex( int varCcyIndex )
	{
		this.varCcyIndex = varCcyIndex;
	}

	public void setLimitCcyIndex( int limitCcyIndex )
	{
		this.limitCcyIndex = limitCcyIndex;
	}

	public void setBaseCcy( String baseCcy )
	{
		this.baseCcy = baseCcy;
	}

	public void setVarCcy( String varCcy )
	{
		this.varCcy = varCcy;
	}

	public void setLimitCcy( String limitCcy )
	{
		this.limitCcy = limitCcy;
	}

	public void setPriceType( int priceType )
	{
		this.priceType = priceType;
	}

	public void setAggregated( boolean aggregated )
	{
		this.aggregated = aggregated;
	}

	public void setIncremental( boolean incremental )
	{
		this.incremental = incremental;
	}

	public void setStale( boolean stale )
	{
		this.stale = stale;
	}

	public void addBidPrice( MarketPrice price )
	{
		this.bidPrices.add( price );
		if ( price.getLimit() > maxBidLimit )
		{
			maxBidLimit = price.getLimit();
		}
		int size = bidPrices.size();
		if ( size > 1 )
		{
			MarketPrice prevPrice = bidPrices.get( size - 2 );
			if ( prevPrice != null && price.getRate() > prevPrice.getRate() )
			{
				bidSortingRequired = true;
			}
		}
	}
	
	public ArrayList<MarketPrice> getBidPrices()
	{
		return this.bidPrices;
	}

	public void setBidPrices( ArrayList<MarketPrice> bidPrices )
	{
		this.bidPrices.clear();
		maxBidLimit = 0;
		MarketPrice prevPrice = null;
		for ( Iterator<MarketPrice> iterator = bidPrices.iterator(); iterator.hasNext(); )
		{
			MarketPrice price = iterator.next();
			this.bidPrices.add( price );
			if ( price.getLimit() > maxBidLimit )
			{
				maxBidLimit = price.getLimit();
			}
			if ( prevPrice != null && price.getRate() > prevPrice.getRate() )
			{
				bidSortingRequired = true;
			}
			prevPrice = price;
		}
	}
	
	public ArrayList<MarketPrice> getOfferPrices()
	{
		return this.offerPrices;
	}

	public void setOfferPrices( ArrayList<MarketPrice> offerPrices )
	{
		this.offerPrices.clear();
		maxOfferLimit = 0;
		MarketPrice prevPrice = null;
		for ( Iterator<MarketPrice> iterator = offerPrices.iterator(); iterator.hasNext(); )
		{
			MarketPrice price = iterator.next();
			this.offerPrices.add( price );
			if ( price.getLimit() > maxOfferLimit )
			{
				maxOfferLimit = price.getLimit();
			}
			if ( prevPrice != null && price.getRate() < prevPrice.getRate() )
			{
				offerSortingRequired = true;
			}
			prevPrice = price;
		}
	}

	public void addOfferPrice( MarketPrice price )
	{
		this.offerPrices.add( price );
		if ( price.getLimit() > maxOfferLimit )
		{
			maxOfferLimit = price.getLimit();
		}
		int size = offerPrices.size();
		if ( size > 1 )
		{
			MarketPrice prevPrice = offerPrices.get( size - 2 );
			if ( prevPrice != null && price.getRate() < prevPrice.getRate() )
			{
				offerSortingRequired = true;
			}
		}
	}

	public void setBidPrice( int index, MarketPrice price )
	{
		this.bidPrices.set( index, price );
		if ( price.getLimit() > maxBidLimit )
		{
			maxBidLimit = price.getLimit();
		}
		int size = bidPrices.size();
		if ( size > 1 )
		{
			MarketPrice prevPrice = bidPrices.get( size - 2 );
			if ( prevPrice != null && price.getRate() > prevPrice.getRate() )
			{
				bidSortingRequired = true;
			}
		}
	}
	
	public void setOfferPrice( int index, MarketPrice price )
	{
		this.offerPrices.set( index, price );
		if ( price.getLimit() > maxOfferLimit )
		{
			maxOfferLimit = price.getLimit();
		}
		int size = offerPrices.size();
		if ( size > 1 )
		{
			MarketPrice prevPrice = offerPrices.get( size - 2 );
			if ( prevPrice != null && price.getRate() < prevPrice.getRate() )
			{
				offerSortingRequired = true;
			}
		}
	}
	

	// METHODS required for backward compatibility with first version of
	// MarketRate deserialization.

	/**
	 * Set quoteId.
	 * 
	 * @param quoteId
	 * @param tierIndex
	 */
	public void setProviderQuoteId( String quoteId, int tierIndex )
	{
		MarketPriceC price = getOrCreateBidPrice( tierIndex );
		if ( price != null )
		{
			price.setProviderQuoteId( quoteId );
		}
		price = getOrCreateOfferPrice( tierIndex );
		if ( price != null )
		{
			price.setProviderQuoteId( quoteId );
		}
	}

	public String getProviderQuoteId( int tierIndex )
	{
		MarketPriceC price = getOrCreateBidPrice( tierIndex );
		if ( price != null )
		{
			return price.getProviderQuoteId();
		}
		price = getOrCreateOfferPrice( tierIndex );
		if ( price != null )
		{
			return price.getProviderQuoteId();
		}
		return null;
	}

	public String getProviderQuoteId( int tierIndex, int bidOffer )
	{
		MarketPriceC price = null;
		if ( bidOffer == BID )
		{
			price = getOrCreateBidPrice( tierIndex );
		}
		else if ( bidOffer == OFFER )
		{
			price = getOrCreateOfferPrice( tierIndex );
		}
		if ( price != null )
		{
			return price.getProviderQuoteId();
		}

		return null;
	}

	/**
	 * Return the quoteId of the first tier. If the tier
	 * {@link MarketRateTier#hasBid() has bid rate set}, then the quoteId from
	 * the {@link MarketRateTier#getBid() bid side} is return. If the tier has
	 * offer rate set, then the quoteId from the
	 * {@link MarketRateTier#getOffer() offer side} is sent.
	 * 
	 * @return
	 */
	public String getProviderQuoteId()
	{
		throw new UnsupportedOperationException( "Unsupported." );
	}

	public MarketPriceC getOrCreateBidPrice( int tierIndex )
	{
		if ( tierIndex < bidPrices.size() )
		{
			MarketPriceC price = ( MarketPriceC ) bidPrices.get( tierIndex );
			if ( price == null )
			{
				price = new MarketPriceC();
				bidPrices.set( tierIndex, price );
			}
			return price;
		}
		return null;
	}

	public MarketPriceC getOrCreateOfferPrice( int tierIndex )
	{
		if ( tierIndex < offerPrices.size() )
		{
			MarketPriceC price = ( MarketPriceC ) offerPrices.get( tierIndex );
			if ( price == null )
			{
				price = new MarketPriceC();
				offerPrices.set( tierIndex, price );
			}
			return price;
		}
		return null;
	}

	public boolean setBidRate( double rate, int tierIndex )
	{
		MarketPriceC price = getOrCreateBidPrice( tierIndex );
		if ( price != null )
		{
			price.setRate( rate );
			if ( tierIndex > 0 )
			{
				MarketPriceC prevPrice = getOrCreateBidPrice( tierIndex - 1 );
				if ( prevPrice != null && rate > prevPrice.getRate() )
				{
					bidSortingRequired = true;
				}
			}
			return true;
		}
		return false;
	}

	public boolean setOfferRate( double rate, int tierIndex )
	{
		MarketPriceC price = getOrCreateOfferPrice( tierIndex );
		if ( price != null )
		{
			price.setRate( rate );
			if ( tierIndex > 0 )
			{
				MarketPriceC prevPrice = getOrCreateOfferPrice( tierIndex - 1 );
				if ( prevPrice != null && rate < prevPrice.getRate() )
				{
					offerSortingRequired = true;
				}
			}
			return true;
		}
		return false;
	}
	
	@Override
	public boolean setBidSpotRate(double spotRate, int tierIndex) 
	{
		MarketPriceC price = getOrCreateBidPrice( tierIndex );
		if ( price != null )
		{
			price.setSpotRate( spotRate );
			return true;
		}
		return false;
	}

	@Override
	public boolean setOfferSpotRate(double spotRate, int tierIndex) 
	{
		MarketPriceC price = getOrCreateOfferPrice(tierIndex);
		if ( price != null )
		{
			price.setSpotRate(spotRate);
			return true;
		}
		return false;
	}

	public boolean setBidLimit( long limit, int tierIndex )
	{
		MarketPriceC price = getOrCreateBidPrice( tierIndex );
		if ( price != null )
		{
			price.setLimit( limit );
            price.setTotalLimit( limit );
			if ( limit > maxBidLimit )
			{
				maxBidLimit = limit;
			}
			return true;
		}
		return false;
	}

	public boolean setOfferLimit( long limit, int tierIndex )
	{
		MarketPriceC price = getOrCreateOfferPrice( tierIndex );
		if ( price != null )
		{
			price.setLimit( limit );
            price.setTotalLimit( limit );
			if ( limit > maxOfferLimit )
			{
				maxOfferLimit = limit;
			}
			return true;
		}
		return false;
	}

	private void ensureTierCapacity( int maxTiers, ArrayList<MarketPrice> list )
	{
		for ( int i = list.size(); i < maxTiers; i++ )
		{
			list.add( new MarketPriceC() );
		}
	}

	public long getRateReceivedByAdapter()
	{
		// if ( rateRcvdByAdapter == 0 )
		// {
		// Timing t = getTiming();
		// if ( t != null )
		// {
		// rateRcvdByAdapter = t.getLongTime(
		// ISCommonConstants.EVENT_TIME_DISP_ADAPTER_REC_RATE );
		// }
		// }
		return rateRcvdByAdapter;
	}

	public long getRateSentByAdapter()
	{
		// if ( rateSentByAdapter == 0 )
		// {
		// Timing t = getTiming();
		// if ( t != null )
		// {
		// rateSentByAdapter = t.getLongTime(
		// ISCommonConstants.EVENT_TIME_DISP_ADAPTER_SENT_RATE );
		// }
		// }
		return rateSentByAdapter;
	}

	public void setRateReceivedByAdapter( long rateRcvdByAdapter )
	{
		this.rateRcvdByAdapter = rateRcvdByAdapter;
	}

	public void setRateSentByAdapter( long rateSentByAdapter )
	{
		this.rateSentByAdapter = rateSentByAdapter;
	}

	public long getRateEffective()
	{
		return rateEffectiveAtProvider;
	}

	public void setRateEffective( long rateEffectiveAtProvider )
	{
		this.rateEffectiveAtProvider = rateEffectiveAtProvider;
	}

	public String getServerId()
	{
		return serverId;
	}

	public void setServerId( String serverId )
	{
		this.serverId = serverId;
	}

	public String getBookname()
	{
		return bookname;
	}

	public void setBookname( String bookname )
	{
		this.bookname = bookname;
	}
	
	/**
	 * Extracted SequenceNo from quoteId ( guid )
	 * @return
	 */
	public long getSequenceNo()
	{
		return sequenceNo;
	}

	public void reset()
	{
		this.providerShortName = null;
		this.providerIndex = 0;
		this.longQuoteId = -1;
		this.streamId = null;
		this.streamIndex = 0;
		this.baseCcy = null;
		this.baseCcyIndex = -1;
		this.varCcy = null;
		this.varCcyIndex = -1;
		this.limitCcy = null;
		this.limitCcyIndex = -1;
		this.priceType = 0;
		this.aggregated = false;
		this.incremental = false;
		this.stale = false;
		this.maxBidLimit = 0;
		this.maxOfferLimit = 0;
		// Tiers
		for ( MarketPrice mp : bidPrices )
			mp.reset();
		for ( MarketPrice mp : offerPrices )
			mp.reset();
		this.quoteId = null;
		this.isDirty = true;
		this.bidSortingRequired = false;
		this.offerSortingRequired = false;
		this.rateRcvdByAdapter = 0;
		this.rateSentByAdapter = 0;
		this.rateEffectiveAtProvider = 0;
		this.serverId = null;
		this.bookname = null;
        this.isExternalProvidersValueDate = true;
		this.priceSubType = 0;
		this.noOfBidTiers = 0;
		this.noOfOfferTiers = 0;
	}

	public boolean isOutrightDisplayOrder()
	{
		return isOutrightDisplayOrder;
	}
	
	public void setIsOutrightDisplayOrder(boolean isOutrightDisplayOrder)
	{
		this.isOutrightDisplayOrder = isOutrightDisplayOrder;
	}

    public boolean isExternalProvidersValueDate(){
        return this.isExternalProvidersValueDate;
    }

    public void setIsExternalProvidersValueDate(boolean isExtProvsValueDate){
        this.isExternalProvidersValueDate = isExtProvsValueDate;
    }

    public double getBidTotalLimit()
    {
        return getBidTotalLimit( 0 );
    }

    public double getBidTotalLimit( int tier )
    {
        bidRangeCheck( tier );

        MarketPrice price = bidPrices.get( tier );
        return price.getTotalLimit();

    }

    public double getOfferTotalLimit()
    {
        return getOfferTotalLimit( 0 );
    }

    public double getOfferTotalLimit( int tier )
    {
        offerRangeCheck( tier );

        MarketPrice price = offerPrices.get( tier );
        return price.getTotalLimit();

    }

	@Override
	public int getPriceSubType() {
		return priceSubType;
	}

	@Override
	public void setPriceSubType(int priceSubType) {
		this.priceSubType = priceSubType;
	}

	@Override
	public void setValueDate(long valueDate) {
		this.valueDate = valueDate;		
	}

	@Override
	public long getValueDate() {
		return valueDate;
	}

	public int getNoOfBidTiers() {
		return noOfBidTiers;
	}

	public void setNoOfBidTiers(int noOfBidTiers) {
		this.noOfBidTiers = noOfBidTiers;
	}

	public int getNoOfOfferTiers() {
		return noOfOfferTiers;
	}

	public void setNoOfOfferTiers(int noOfOfferTiers) {
		this.noOfOfferTiers = noOfOfferTiers;
	}

	@Override
	public void setQuoteId(String quoteId) {
		this.quoteId = quoteId;		
	}
}
