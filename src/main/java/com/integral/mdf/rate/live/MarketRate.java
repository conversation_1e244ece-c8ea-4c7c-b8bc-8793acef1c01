// Copyright (c) 2005 Integral Development Corp.  All rights reserved.
package com.integral.mdf.rate.live;

import java.util.ArrayList;

/**
 * Represents a MarketRate that is streaming through the system.
 *
 * <AUTHOR> Development Corp.
 */
public interface MarketRate /*extends ISMessage*/
{

    public static final int PRICE_TYPE_MULTI_PRICE = 0;
    public static final int PRICE_TYPE_MULTI_TIER = 1;
    public static final int PRICE_TYPE_ORDERS = 2;
    public static final int PRICE_SUBTYPE_HOURGLASS = 1;

    public MarketRate clone();

    /**
     * Is this rate an aggregated from different providers. This also implies that each of the side of the rates in
     * all the tiers will have a providerIndex, providerQuoteIds populated.
     *
     * @return
     */
    boolean isAggregated();

    boolean isIncremental();

    /**
     * QuoteId assigned  by the provider to this rate.
     *
     * @return
     */
    @Deprecated
    String getProviderQuoteId();

    /**
     * Get the stream that this message is for.
     *
     * @return
     */
    String getStreamId();

    int getStreamIndex();

    /**
     * returns base currency
     *
     * @return
     */
    String getBaseCcy();


    /**
     * returns variable currency
     *
     * @return
     */
    String getVariableCcy();

    /**
     * Limit currency is the monetary unit for the bid limit and offer limit amounts.
     *
     * @return limit currency
     */
    String getLimitCcy();

    /**
     * get bid rate
     *
     * @return
     */
    double getBidRate();

    /**
     * get Bid rate for tier
     *
     * @param tier
     * @return
     */
    double getBidRate( int tier );

    /**
     * get offer rate
     *
     * @return
     */
    double getOfferRate();

    /**
     * Get offer rate for tier
     *
     * @param tier
     * @return
     */
    double getOfferRate( int tier );

	/**
	 *    
	 * @param get Bid Forward Points 
	 * @return
	 */
	public double getBidForwardPoints();
	/**
	 *    
	 * @param get Bid Forward Points for tier
	 * @return
	 */
	public double getBidForwardPoints( int tier );
	
	/**
	 *    
	 * @param get Offer Forward Points 
	 * @return
	 */
	public double getOfferForwardPoints();
	/**
	 *    
	 * @param get Offer Forward Points for tier
	 * @return
	 */
	public double getOfferForwardPoints( int tier );
	
	/**
	 *    
	 * @param get Bid SpotRate
	 * @return
	 */
	public double getBidSpotRate();
	/**
	 *    
	 * @param get Bid SpotRatefor tier
	 * @return
	 */
	public double getBidSpotRate( int tier );
	
	/**
	 *    
	 * @param get Offer SpotRate
	 * @return
	 */
	public double getOfferSpotRate();

	/**
	 *    
	 * @param get Offer SpotRatefor tier
	 * @return
	 */
	public double getOfferSpotRate( int tier );

    /**
     * get bid limit
     *
     * @return
     */
    double getBidLimit();

    /**
     * get bidlimit for tier
     *
     * @param tier
     * @return
     */
    double getBidLimit( int tier );

    /**
     * get max bid limit
     *
     * @return
     */
    double getMaxBidLimit();

    /**
     * get offer limit
     *
     * @return
     */
    double getOfferLimit();

    /**
     * get offer limit for tier
     *
     * @param tier
     * @return
     */
    double getOfferLimit( int tier );

    /**
     * get max offer limit
     *
     * @return
     */
    double getMaxOfferLimit();

    /**
     * @return
     */
    String getProviderShortName();

    int getProviderIndex();

    /**
     * Check whether rate is stale. Stale means that the MarketRate is not executable.
     * For example, indicative rates are marked as stale.
     * This field does NOT mean that a previous MarketRate is invalid.
     *
     * @return
     */
    boolean isStale();

    void setStale( boolean value );

    /**
     * @return
     */
    int getBidTierSize();

    /**
     * @return
     */
    int getOfferTierSize();

    /*
    * use getProviderQuoteId(tier, bidOffer)
    */

    String getProviderQuoteId( int tier );

    /**
     * @param tier
     * @param bidOffer - one of {@link com.integral.finance.dealing.DealingPrice#BID} or {@link com.integral.finance.dealing.DealingPrice#OFFER}
     * @return
     */
    String getProviderQuoteId( int tier, int bidOffer );

    /**
     * <li> 0 = {@link #PRICE_TYPE_MULTI_PRICE multiPrice}
     * <li> 1 = {@link #PRICE_TYPE_MULTI_TIER multiTier}
     * <li> 2 = {@link #PRICE_TYPE_ORDERS orders}
     *
     * @return
     */
    int getPriceType();

    void setPriceType( int priceType );

    boolean isBidSortingRequired();

    boolean isOfferSortingRequired();

    void sort();

    // Operations on Tiers.

    int getNumTiers();

    MarketPrice getBidTier( int tierIndex );

    int getBaseCcyIndex();

    int getVarCcyIndex();

    int getLimitCcyIndex();

    int getNumBidTiers();

    int getNumOfferTiers();
    
    ArrayList<MarketPrice> getBidPrices();
    
    ArrayList<MarketPrice> getOfferPrices();

    MarketPrice getOfferTier( int tierIndex );

    void setProviderShortName( String providerShortName );

    void setQuoteId( String quoteId );

    void setStreamId( String streamId );

    void setProviderIndex( int providerIndex );

    void setStreamIndex( int streamIndex );

    void setBaseCcyIndex( int baseCcyIndex );

    void setVarCcyIndex( int varCcyIndex );

    void setLimitCcyIndex( int limitCcyIndex );

    void setBaseCcy( String baseCcy );

    void setVarCcy( String varCcy );

    void setLimitCcy( String limitCcy );

    void setAggregated( boolean aggregated );

    void setIncremental( boolean incremental );

    void addBidPrice( MarketPrice price );

    void setBidPrices( ArrayList<MarketPrice> bidPrices );

    void setOfferPrices( ArrayList<MarketPrice> offerPrices );

    void addOfferPrice( MarketPrice price );

    void setProviderQuoteId( String quoteId, int tierIndex );

    MarketPriceC getOrCreateBidPrice( int tierIndex );

    MarketPriceC getOrCreateOfferPrice( int tierIndex );

    boolean setBidRate( double rate, int tierIndex );

    boolean setOfferRate( double rate, int tierIndex );
    
    boolean setBidSpotRate( double rate, int tierIndex );
    
    boolean setOfferSpotRate( double rate, int tierIndex );
    
    boolean setBidLimit( long limit, int tierIndex );

    boolean setOfferLimit( long limit, int tierIndex );

    long getRateReceivedByAdapter();

    long getRateSentByAdapter();

    long getRateEffective();

    void setRateReceivedByAdapter( long rateRcvdByAdapter );

    void setRateSentByAdapter( long rateSentByAdapter );

    void setRateEffective( long rateEffectiveAtProvider );

    String getBookname();

    void setBookname(String bookname);
    
	public void setBidPrice( int index, MarketPrice price );

	public void setOfferPrice( int index, MarketPrice price );

	/**
	 * Extracted SequenceNo from quoteId ( guid )
	 * @return
	 */
	public long getSequenceNo();
	
	/**
	 *  Returns true if it is a Outright Resting Order
	 * @return
	 */
	public boolean isOutrightDisplayOrder();
	
	public void setIsOutrightDisplayOrder(boolean isOutrightDisplayOrder);

    public boolean isExternalProvidersValueDate();

    public void setIsExternalProvidersValueDate(boolean isExtProvsValueDate);

    public double getBidTotalLimit();

    public double getBidTotalLimit(int tier);

    public double getOfferTotalLimit();

    public double getOfferTotalLimit(int tier);

    public int getPriceSubType();
    public void setPriceSubType(int subType);
    
    public void setValueDate(long valueDate);
    
    public long getValueDate();

	public int getNoOfBidTiers() ;

	public void setNoOfBidTiers(int noOfBidTiers);

	public int getNoOfOfferTiers() ;

	public void setNoOfOfferTiers(int noOfOfferTiers);
	
    public void reset();

	public void setQuoteId(long quoteId);
}