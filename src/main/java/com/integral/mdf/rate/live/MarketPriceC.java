package com.integral.mdf.rate.live;


/**
 * <AUTHOR>
 */
public class MarketPriceC implements MarketPrice {

    String providerQuoteId;
    long longProviderQuoteId = 0;
    long minLimit;
    long limit;
    long timeEffective;
    int timeToLive;
    int providerIndex = 0;
    double rate;
    double spotRate;
    double forwardPoints;
    boolean isFlashOrderPrice;
    boolean isRateSourceDisplayedOrder;
    boolean isRateSourcePeggedOrder;
    double totalLimit;


    public long getMinLimit() {
        return minLimit;
    }

    public long getLimit() {
        return limit;
    }

    public int getProviderIndex() {
        return providerIndex;
    }

    public double getRate() {
        return rate;
    }

    public void setMinLimit(long limit) {
        this.minLimit = limit;
    }

    public double getTotalLimit() {
        return totalLimit;
    }

    public void setTotalLimit( double totalLimit ) {
        this.totalLimit = totalLimit;
    }

    public MarketPriceC() {
    }

    public MarketPriceC(MarketPrice other) {
        this.providerQuoteId = other.getProviderQuoteId();
        this.longProviderQuoteId = other.getLongProviderQuoteId();
        this.minLimit = other.getMinLimit();
        this.limit = other.getLimit();
        this.providerIndex = other.getProviderIndex();
        this.rate = other.getRate();
        this.spotRate = other.getSpotRate();
        this.forwardPoints = other.getForwardPoints();
        this.isRateSourceDisplayedOrder = other.isRateSourceDisplayedOrder();
        this.isRateSourcePeggedOrder = other.isRateSourcePeggedOrder();
        this.totalLimit = other.getTotalLimit();
    }

    public void setLimit(long limit) {
        this.limit = limit;
    }

    public void setProviderIndex(int index) {
        this.providerIndex = index;
    }

    public void setRate(double rate) {
        this.rate = rate;
    }

    public String getProviderQuoteId() {
        return providerQuoteId;
    }

    public void setProviderQuoteId(String providerQuoteId) {
        this.providerQuoteId = providerQuoteId;
    }

    public long getLongProviderQuoteId() {
        return longProviderQuoteId;
    }

    public void setProviderQuoteId(long longProviderQuoteId) {
        this.longProviderQuoteId = longProviderQuoteId;
        this.providerQuoteId = Long.toString(longProviderQuoteId);
    }

    public long getTimeEffective() {
        return timeEffective;
    }

    public void setTimeEffective(long timeEffective) {
        this.timeEffective = timeEffective;
    }

    public double getSpotRate() {
        return spotRate;
    }

    public double getForwardPoints() {
        return forwardPoints;
    }

    public void setSpotRate(double spotRate) {
        this.spotRate = spotRate;
    }

    public void setForwardPoints(double forwardPoints) {
        this.forwardPoints = forwardPoints;
    }

    public int getTTL() {
        return timeToLive;
    }

    public void setTTL(int timeToLive) {
        this.timeToLive = timeToLive;
    }

    public boolean isFlashOrderPrice() {
        return isFlashOrderPrice;
    }

    public void setIsFlashOrderPrice(boolean isFlashOrderPrice) {
        this.isFlashOrderPrice = isFlashOrderPrice;
    }

    public boolean isRateSourceDisplayedOrder() {
        return isRateSourceDisplayedOrder;
    }

    public void setIsRateSourceDisplayedOrder(boolean isRateSourceDisplayedOrder) {
        this.isRateSourceDisplayedOrder = isRateSourceDisplayedOrder;
    }

    public boolean isRateSourcePeggedOrder() {
        return isRateSourcePeggedOrder;
    }

    public void setIsRateSourcePeggedOrder(boolean isRateSourcePeggedOrder) {
        this.isRateSourcePeggedOrder = isRateSourcePeggedOrder;
    }

    public void reset() {
        this.providerQuoteId = null;
        this.longProviderQuoteId = 0;
        this.minLimit = 0;
        this.limit = 0;
        this.timeEffective = 0;
        this.providerIndex = 0;
        this.rate = 0;
        this.spotRate = 0;
        this.forwardPoints = 0;
        this.timeToLive = 0;
        this.isFlashOrderPrice = false;
        this.isRateSourceDisplayedOrder = false;
        this.isRateSourcePeggedOrder = false;
        this.totalLimit = 0;
    }
}
