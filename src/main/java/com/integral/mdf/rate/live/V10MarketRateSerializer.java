package com.integral.mdf.rate.live;

import java.nio.ByteBuffer;

import com.integral.log.Log;
import com.integral.log.LogFactory;

public class V10MarketRateSerializer extends V2MarketRateSerializer implements MarketRateSerializer {
    protected Log log = LogFactory.getLog(this.getClass());

    short toShort(int i) {
        if (i > 0xFFFF) {
            throw new LimitBreachedException("Field Limit Breached in V10 Serializer.");
        }
        return (short) i;
    }

    int toInt(short b) {
        return b & 0xFFFF;
    }

    protected MarketRate headerDeserialize(ByteBuffer in, MarketRate rate) {
//        int version = in.get();
        int orgIndex = in.getInt();
        int streamIndex = toInt(in.get());
        int baseCcyIndex = toInt(in.getShort());
        int varCcyIndex = toInt(in.getShort());
        //we donot do anything with Tenor yet, just a place holder.
        in.get();

        rate.setProviderIndex(orgIndex);
        rate.setStreamIndex(streamIndex);
        rate.setBaseCcyIndex(baseCcyIndex);
        rate.setVarCcyIndex(varCcyIndex);

        return rate;
    }


    public int getProviderIndex(byte[] packet, int startOffset) {

        //skip the version byte, and start with index=1;

        int orgIndex = 0;
        orgIndex |= packet[startOffset + 1] & 0xFF;
        orgIndex <<= 8;
        orgIndex |= packet[startOffset + 2] & 0xFF;
        orgIndex <<= 8;
        orgIndex |= packet[startOffset + 3] & 0xFF;
        orgIndex <<= 8;
        orgIndex |= packet[startOffset + 4] & 0xFF;
        return orgIndex;
    }

    public int getStreamIndex(byte[] packet, int startOffset) {
        return packet[startOffset + 5] & 0xFF;
    }

    public int getBaseCcyIndex(byte[] packet, int startOffset) {
        int baseCcyIndex = 0;
        baseCcyIndex |= packet[startOffset + 6] & 0xFF;
        baseCcyIndex <<= 8;
        baseCcyIndex |= packet[startOffset + 7] & 0xFF;
        return baseCcyIndex;
    }

    public int getVarCcyIndex(byte[] packet, int startOffset) {
        int varCcyIndex = 0;
        varCcyIndex |= packet[startOffset + 8] & 0xFF;
        varCcyIndex <<= 8;
        varCcyIndex |= packet[startOffset + 9] & 0xFF;
        return varCcyIndex;
    }

}