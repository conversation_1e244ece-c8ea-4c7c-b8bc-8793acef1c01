package com.integral.mdf.rate.live;

/**
 * A factory that returns a serializer instance based on the version of serialization you
 * want to deal with.
 *
 * <AUTHOR>
 */
public class MarketRateSerializerFactory {

    //workhorse - almost all PAs
    V2MarketRateSerializer v2Serializer = new V2MarketRateSerializer();

    //broker adaptor
    V6MarketRateSerializer v6MarketRateSerializer = new V6MarketRateSerializer();

    // To support currency indexes more than 255.
    //workhorse - almost all PAs - replaces V2
    V10MarketRateSerializer v10Serializer = new V10MarketRateSerializer();

    // New delimiter - fix delimeter for rates from PAs.
    V13MarketRateSerializer v13Serializer = new V13MarketRateSerializer();

    // Broker adaptor - Replaces V6
    V11MarketRateSerializer v11MarketRateSerialzier = new V11MarketRateSerializer();

    /**
     * Get the serializer for appropriate version. An exception is thrown if no serializer was found for
     * the specified version.
     *
     * @param version
     * @return
     * @throws IllegalAccessException
     */
    public MarketRateSerializer getDeSerializerForVersion(int version) {
        switch (version) {
            case 2:
                return v2Serializer;
            case 6:
                return v6MarketRateSerializer;
            case 10:
                return v10Serializer;
            case 11:
                return v11MarketRateSerialzier;
            case 13:
                return v13Serializer;
        }
        return null;
    }

    private static MarketRateSerializerFactory instance = new MarketRateSerializerFactory();

    public static MarketRateSerializerFactory instance() {
        return instance;
    }

    private MarketRateSerializerFactory() {
    }
}
