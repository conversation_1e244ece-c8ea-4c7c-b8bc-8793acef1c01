package com.integral.mdf.rate;


import java.util.concurrent.atomic.AtomicLongArray;

/**
 * A BitSet that support writing and reading by parallel threads.
 */
public class ConcurrentBitSet {

    /*
    * BitSets are packed into arrays of "words."  Currently a word is
    * a long, which consists of 64 bits, requiring 6 address bits.
    * The choice of word size is determined purely by performance concerns.
    */
    private final static int ADDRESS_BITS_PER_WORD = 6;
    private final static int BITS_PER_WORD = 1 << ADDRESS_BITS_PER_WORD;

    volatile AtomicLongArray words;
    volatile int wordsInUse = 0;

    public ConcurrentBitSet(int numBits) {
        words = new AtomicLongArray(wordIndex(numBits) + 1);
    }

    /* Used to shift left or right for a partial word mask */
    private static final long WORD_MASK = 0xffffffffffffffffL;

    public void set(int bitIndex) {
        if (bitIndex < 0)
            throw new IndexOutOfBoundsException("bitIndex < 0: " + bitIndex);

        unsafeSet(bitIndex);

    }

    /**
     * If it's guaranteed that bitIndex will always be > 0, then you can use this method.
     *
     * @param bitIndex
     */
    public void unsafeSet(int bitIndex) {
        int wordIndex = wordIndex(bitIndex);

        //expandTo(wordIndex);
        if (wordIndex >= words.length()) {
            expandTo(wordIndex);
        }

        //wordsInUse defines the internal boundary in the buffer that
        if (wordIndex >= wordsInUse) {
            wordsInUse = wordIndex + 1;
        }        

        long wordValue = words.get(wordIndex);
        //the only other thread that can change the value of the word is the sweep thread.
        //And the sweep thread resets the value of the word to 0L.
        if (!words.compareAndSet(wordIndex, wordValue, wordValue | (1L << bitIndex))) {
            words.set(wordIndex, (1L << bitIndex));
        }
    }

    public void expandTo(int maxIndex) {
        //long = 64bits.  Make space for 64 more entries in the bitmap.
        AtomicLongArray newWords = new AtomicLongArray(maxIndex + 1);

        for (int i=0; i < words.length(); i++) {
            newWords.set(i, words.get(i));
        }

        for (int i=words.length(); i < newWords.length(); i++) {
            newWords.set(i, 0L);
        }

        words = newWords;
    }

    int wordIndex(int bitIndex) {
        return bitIndex >> ADDRESS_BITS_PER_WORD;
    }

    public void clear(int bitIndex) {
        if (bitIndex < 0)
            throw new IndexOutOfBoundsException("bitIndex < 0: " + bitIndex);
        int wordIndex = wordIndex(bitIndex);

        //expandTo(wordIndex);
        if (wordIndex > words.length()) {
            throw new IndexOutOfBoundsException("workdIndex > array length: " + wordIndex);
        }

        if (wordsInUse <= wordIndex) {
            wordsInUse++;
        }

        long wordValue = words.get(wordIndex);
        words.weakCompareAndSet(wordIndex, wordValue, wordValue & ~(1L << bitIndex));
    }

    public void setValue(int bitIndex, boolean value) {
        if (value)
            set(bitIndex);
        else
            clear(bitIndex);
    }

    public boolean get(int bitIndex) {
        if (bitIndex < 0)
            throw new IndexOutOfBoundsException("bitIndex < 0: " + bitIndex);

        int wordIndex = wordIndex(bitIndex);
        return (wordIndex < wordsInUse) && ((words.get(wordIndex) & (1L << bitIndex)) != 0);
    }

    public ClearingBitIterator iterator() {
        return new ClearingBitIterator();
    }

    /**
     * An interator through the bit set that will clear the bits as it reads them. This is used for sweeping a
     * changeset atomically, while also clearing the bits at the same time.
     * Only use this when you intend to sweep the entire bitset.
     */
    public class ClearingBitIterator {
        private int wordIndex = -1;
        private long word = 0;

        public int nextSetBit(int fromIndex) {
            if (fromIndex < 0)
                throw new IndexOutOfBoundsException("fromIndex < 0: " + fromIndex);

            return unsafeNextSetBit(fromIndex);
        }

        public int unsafeNextSetBit(int fromIndex) {

            int u = wordIndex(fromIndex);
            if (u >= wordsInUse)
                return -1;

            if (u != wordIndex) {
                wordIndex = u;
                word = words.getAndSet(wordIndex, 0L) & (WORD_MASK << fromIndex);
            } else {
                word &= (WORD_MASK << fromIndex);
            }

            if (word != 0)
                return (wordIndex * BITS_PER_WORD) + Long.numberOfTrailingZeros(word);

            while (true) {

                if (++wordIndex == wordsInUse)
                    return -1;

                word = words.getAndSet(wordIndex, 0L);

                if (word != 0)
                    return (wordIndex * BITS_PER_WORD) + Long.numberOfTrailingZeros(word);
            }
        }

        public void reset() {
            word = 0;
            wordIndex = -1;
        }
    }
}
