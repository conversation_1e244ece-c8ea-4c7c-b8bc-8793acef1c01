package com.integral.mdf.rate;

import java.util.ListIterator;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.LockSupport;

import com.integral.commons.Counter;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.PriceBookSink;
import com.integral.mdf.data.PriceBook;
import com.integral.mdf.data.ServerProvision;
import com.integral.pipeline.metrics.Metrics;
import com.integral.pipeline.metrics.MetricsManager;
import org.HdrHistogram.Histogram;

public class RateProcessor extends Thread {

	Log log = LogFactory.getLog(this.getClass());

	// Task id generator
	final private CopyOnWriteArrayList<RateChannel> taskIds = new CopyOnWriteArrayList<RateChannel>();

	// change set keeps tracks of changed task ids. In this case task are
	// RateChannels which has new rates from LPs And the tasks is to provision raw rates and
	// and do new aggregation snd send update to gateways
	final private ConcurrentBitSet rateUpdateChangeSet = new ConcurrentBitSet(512);


	// Task id generator
	final private CopyOnWriteArrayList<RateBook> notificationTasks = new CopyOnWriteArrayList<RateBook>();

	//change set keeps tracks of updates to rate books. This is fan-out within channels.
	final private ConcurrentBitSet notificationChangeSet = new ConcurrentBitSet(512);


	// thread park status to avoid extra call to park -how useful it is ?
	final private AtomicBoolean parked = new AtomicBoolean(false);

	private boolean isShutdown = false;
	
	//thread idle interval in micro seconds - default is 1000us ~ 1 millisec 
	int idleInterval = 1000;
	
	final byte processorId;

	PriceBookSink sink;
	
	final RateProcessorMetrics rpm;
	
	public RateProcessor(ServerProvision sp, byte processorId, PriceBookSink sink){
		super("RP-"+processorId);
		int tmpIdleInterval = sp.getThreadIdleTimeInterval();
		if(tmpIdleInterval > 0){
            this.idleInterval = tmpIdleInterval;
        }
		this.processorId = processorId;
		this.sink = sink;
		this.rpm = new RateProcessorMetrics("k=RP-"+processorId);
		MetricsManager.instance().register(rpm);
	}
	
	public byte getProcessorId(){
		return this.processorId;
	}

    /**
     * Assigns taskId to channel for rate updates
     * @param channel
     * @return
     */
	public Integer assignTaskId(RateChannel channel) {
		int nextIndex;
		synchronized (taskIds) {
			nextIndex = taskIds.size();
			taskIds.add(channel);
		}
		return nextIndex;
	}

    /**
     * Assigns taskId to RateBook for config notifications
     * @param book
     * @return
     */
	public Integer assignTaskId(RateBook book) {
		int nextIndex;
		synchronized (notificationTasks) {
			nextIndex = notificationTasks.size();
			notificationTasks.add(book);
		}
		return nextIndex;
	}

	//DO NOT DELETE
//	public void updateKeyToIndex(int index, Long key) {
//		taskIds.set(index, key);
//	}

	public ListIterator<RateChannel> getAssignedChannels() {
		return taskIds.listIterator();
	}

	@Override
	public void run() {

		ConcurrentBitSet.ClearingBitIterator bitIt = rateUpdateChangeSet.iterator();
		ConcurrentBitSet.ClearingBitIterator notifbitIt = notificationChangeSet.iterator();

		long ratesProcessedInLoop;

		while (!isShutdown) {
			ratesProcessedInLoop = 0;
			rpm.loop++;
			try {
				//
				bitIt.reset();
				for (int currIndex = bitIt.nextSetBit(0); currIndex >= 0;
                     currIndex = bitIt.nextSetBit(currIndex + 1)) {
					RateChannel channel = taskIds.get(currIndex);
					channel.updateRateBooks(sink,rpm);
					ratesProcessedInLoop++;
					rpm.rbitset++;
				}

				notifbitIt.reset();
				for (int currIndex = notifbitIt.nextSetBit(0); currIndex >= 0;
					 currIndex = notifbitIt.nextSetBit(currIndex + 1)) {
					RateBook book = notificationTasks.get(currIndex);
                    long ns0 = System.nanoTime();
					PriceBook aggregate = book.aggregate();
                    long ns1 = System.nanoTime();
					if(aggregate!=null){
					    sink.accept(aggregate);
                    }
                   // rpm.aggrtime.record((ns1-ns0)/10000);// (10*10=100)us buckets
                   // rpm.sendtime.record((System.nanoTime()-ns1)/(5*1000));// (5*10=50)us buckets
					rpm.aggrtime.recordValue(ns1-ns0);// (10*10=100)us buckets
					rpm.sendtime.recordValue(System.nanoTime()-ns1);// (5*10=50)us buckets
					ratesProcessedInLoop++;
                    rpm.nbitset++;
                }

				if (ratesProcessedInLoop == 0 && idleInterval > 0) {
					parked.set(true);
					LockSupport.parkNanos(idleInterval * 1000);
				}

			} catch (Exception e) {
				log.error("unable to process rate update. index=" + processorId ,e);
			}
			parked.set(false);
		}

	}

	public void unpark() {
		rpm.unparked++;
		if(parked.getAndSet(false)){
			LockSupport.unpark(this);
		}
	}

	public ConcurrentBitSet getRateUpdateChangeSet(){
        return this.rateUpdateChangeSet;
    }

	public ConcurrentBitSet getNotificationChangeSet() {
		return this.notificationChangeSet;
	}
	
	public void shutdown(){
		this.isShutdown = true;
	}
	
	
	public class RateProcessorMetrics implements Metrics {
		
		long loop=0;
		long rbitset=0;
		long nbitset=0;
		long unparked=0;
		//Counter sendtime = new Counter("mcst");
		//Counter aggrtime = new Counter("at");
		StringBuilder message = new StringBuilder(400);
		final String key ;
		Histogram sendtime = new Histogram(3);
		Histogram aggrtime = new Histogram(3);


		public RateProcessorMetrics(String key){
			this.key=key;
		}

		@Override
		public StringBuilder report() {
			message.setLength(0);
			message.append(key);
			message.append(", loop=").append(loop);
			message.append(", rbitset=").append(rbitset);
            message.append(", nbitset=").append(nbitset);
			message.append(", unparked=").append(unparked);
			message.append(", ").append(getString(sendtime,"mcst",1.0d));
			message.append(", ").append(getString(aggrtime,"at",1.0d));

			loop=0;
			rbitset=0;
            nbitset=0;
			unparked=0;
			sendtime.reset();
			aggrtime.reset();
			return message;
		}

		public StringBuilder getString(Histogram histogram,String key, double divisor){
			StringBuilder report = new StringBuilder(key).append('=').append(histogram.getTotalCount());
			report.append(" [ min=").append(histogram.getMinValue()/divisor);
			report.append(",").append( histogram.getValueAtPercentile(50.00)  / divisor );
			report.append(",").append( histogram.getValueAtPercentile(90.00)  / divisor );
			report.append(",").append( histogram.getValueAtPercentile(99.00)  / divisor );
			report.append(",").append( histogram.getValueAtPercentile(99.90)  / divisor );
			report.append(",").append( histogram.getValueAtPercentile(99.99)  / divisor );
			report.append(",").append( histogram.getValueAtPercentile(100.00)  / divisor );
			report.append("]");
			return report;
		}
		
	}

}
