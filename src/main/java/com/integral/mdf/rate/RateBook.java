package com.integral.mdf.rate;

import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;

import com.integral.notifications.MaxCreditInfo;
import com.integral.provision.MDFAggregationType;
import org.jctools.maps.NonBlockingHashMapLong;

import com.integral.commons.pool.MPMCObjectPool;
import com.integral.commons.pool.ObjectPool;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.Util;
import com.integral.mdf.data.CreditLimitInfo;
import com.integral.mdf.data.FIProvision;
import com.integral.mdf.data.PostAggregationAction;
import com.integral.mdf.data.PriceBook;
import com.integral.mdf.data.PriceBookFactory;
import com.integral.mdf.data.ProvisionedQuoteC;
import com.integral.mdf.data.QuoteC;
import com.integral.mdf.data.QuotePoolFactory;
import com.integral.mdf.data.ServerProvision;
import com.integral.mdf.rate.provisioning.ProvisioningCalculator;
import com.integral.pipeline.metrics.Metrics;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.provision.CurrencyPairProvision;
import com.integral.provision.CurrencyProvision;
import com.integral.provision.LPProvision;

/**
 * RateBook is at FI+ccyp level. It is a collection of rates from multiple LPs.
 * Rates are provisioned for each FI.
 * <AUTHOR>
 *
 */
public class RateBook {

	private static final double DEFAULT_TICK_VALUE = 0.01d;
	private static final int DEFAULT_ROUNDING_TYPE = BigDecimal.ROUND_DOWN;
	private static final int DEFAULT_PRECISION = 4;
	private final int MAX_AGGREGATON_ATTEMPTS;
	private static final String RM = "k=rm";
	private static final String BM = "k=bm";

	//<Long,ProvisioningCalculator>
	//stream - ProvisioningCalculator map
	final NonBlockingHashMapLong<ProvisioningCalculator> pcalcRateIndex = new NonBlockingHashMapLong<ProvisioningCalculator>();

	//LPLE(objectid) to ProvisioningCalculator
	final NonBlockingHashMapLong<List<ProvisioningCalculator>> pcalcCreditIndex = new NonBlockingHashMapLong<List<ProvisioningCalculator>>();

	final private RateProcessor processor;

	//array index inside taskset. Taskset is processed by rate processor
	final private int tasksetPosition;

	private boolean isActive = true;

	final private int fiOrgIndex;

	final private Integer ccypidx;

	final PriceBook pricebook;

	final ObjectPool<QuoteC> rawRatePool;

	protected final Log log = LogFactory.getLog(this.getClass());

	final BookMetrics bm;

	//Accessed by only single thread
	final Set<Long> staleProviderKeys = new HashSet<Long>(50);

	final int precision;

	final double tickValue;
	final int roundingType;

	final String fiKey;
	final int baseCcyIdx;
	final int varCcyIdx;
	final int aggregationType;
	final String rateMetricsKey;
	final Set<Long> providers = new HashSet<>();
	final double requestedSize;
	final boolean termCcyAggregation;
	final boolean customAggregation;

	public RateBook(Integer ccypidx, FIProvision fiProvision,
					ServerProvision serverProvision, RateProcessor rateProcessor, double[] tiers, List<String> providers, double requestedSize, boolean termCcyAggregation, long requestId, boolean customAggregation) {
		this.fiOrgIndex = fiProvision.getIndex();
		this.ccypidx = ccypidx;
		//TODO replace it with Id in Enum.
		this.aggregationType = fiProvision.getAggregationType().getIndex();
		this.requestedSize = requestedSize;
		this.termCcyAggregation = termCcyAggregation;
		this.customAggregation = customAggregation;

		baseCcyIdx = Util.getBaseCurrencyIndex(ccypidx);
		varCcyIdx = Util.getVarCurrencyIndex(ccypidx);
		byte processorId = rateProcessor.getProcessorId();
		//three times the size of
		int poolSize = fiProvision.getLPProvisions().size() * 3 + 2;//make sure size is atleast 2
		this.rawRatePool = new MPMCObjectPool<QuoteC>(new QuotePoolFactory(), poolSize, poolSize , true);

		Optional<String> ccyPairName = serverProvision.getCcyPairName(ccypidx);

		this.precision = getPrecision(serverProvision, ccyPairName);
		this.roundingType = getRoundingType(serverProvision,baseCcyIdx);
		this.tickValue = getTickValue(serverProvision,baseCcyIdx);

		//for OnDemand aggregation
		boolean isTiersModifiable = serverProvision.isOnDemandAggregation() && !customAggregation;
		double[] aggregationTiers = customAggregation ? tiers : fiProvision.getAggregationTiers();

		this.pricebook = PriceBookFactory.create(fiProvision.getAggregationType(),
				precision,serverProvision.getMaxPriceBookDepth(), aggregationTiers,isTiersModifiable, requestedSize, termCcyAggregation, requestId);
		this.pricebook.setLogKey(getLogKey(fiProvision.getName(),serverProvision.getCcyName(baseCcyIdx),serverProvision.getCcyName(varCcyIdx)));

		fiKey = getFIKey(fiProvision, baseCcyIdx, varCcyIdx, serverProvision);
		rateMetricsKey = getRateMetricsKey(baseCcyIdx, varCcyIdx, fiProvision.getName(), processorId, serverProvision,fiProvision.getAggregationType(), requestedSize, providers, termCcyAggregation);

		//For all the LP provisioned for the FI create their corresponding calculator
		int streamIndex;
		for (LPProvision lp : fiProvision.getLPProvisions()) {
			streamIndex = Util.getStreamIndex(serverProvision, lp);
			if(providers != null && !providers.isEmpty() && providers.contains(lp.getShortName())) this.providers.add((long)streamIndex);
//			if (streamIndex == 0) {
//				log.info("Droping the LP. Invalid stream index:" + streamIndex + ",lp:"+ getLPName(lp) + ",streamName:"+ lp.getStreamName() +", fi=" +fiProvision.getName() + ", ccyp=" + ccyPairName.get());
//				continue;
//			}
			if(fiProvision.isSupportedCcyPairForLP(streamIndex,ccypidx)){

				String mKey = rateMetricsKey + ", lp=" + getLPName(lp) + ", s=" + lp.getStreamName() + ", sIdx=" + streamIndex;

				int lpIdx = getLPIndex(lp,serverProvision);

				ProvisioningCalculator calc = new ProvisioningCalculator(mKey,fiKey,lp,fiProvision,baseCcyIdx,varCcyIdx,
															this.rawRatePool,this.precision,lpIdx, serverProvision.isValidateValueDate());

				// de activate the calc if the LP is turned off in Liquidity rules
				if(!lp.isLrEnabled()){
					calc.deActivateForLRLPUpdate();
				}

				// de activate the calc if the stream status is turned off in either the masked lp or real lp;
				if(!lp.isStreamStatus()){
					calc.deActivateForStreamStatusUpdate();
				}

				//cache it against stream index for faster lookup on rate update.
				pcalcRateIndex.put(streamIndex, calc);

				//cache it against credit holder LE's Id for faster lookup on credit updates.
				List<ProvisioningCalculator> provisioningCalculatorList = pcalcCreditIndex.get(lp.getCreditHolderLEId());
				//Multiple LP's could have same CreditHolderLEId , like mask and its LP and other cases so using a list.

				if(null==provisioningCalculatorList){
					ArrayList<ProvisioningCalculator> calculators = new ArrayList<ProvisioningCalculator>();
					provisioningCalculatorList = pcalcCreditIndex.putIfAbsent(lp.getCreditHolderLEId(),calculators);
					if(provisioningCalculatorList == null){
						provisioningCalculatorList = calculators;
					}
				}
				provisioningCalculatorList.add(calc);
			}else{
				log.info("RateBook: unsupported ccypair for LP. fi"+fiProvision.getName()+", ccyp="+ccyPairName.get()+", lp="+getLPName(lp));
			}
		}

		this.processor = rateProcessor;
		this.tasksetPosition = rateProcessor.assignTaskId(this);
		this.pricebook.setFIIndex(this.fiOrgIndex);
		this.pricebook.setCcyPairIndex(this.ccypidx);
		String bmKey = getBookMetricsKey(baseCcyIdx, varCcyIdx,
				fiProvision.getName(),processorId, serverProvision, fiProvision.getAggregationType());
		this.bm = new BookMetrics(bmKey);
		MetricsManager.instance().register(bm);
		MAX_AGGREGATON_ATTEMPTS = serverProvision.getMaxAggregationAttempts() < pcalcRateIndex.size() ?
		serverProvision.getMaxAggregationAttempts() : pcalcRateIndex.size();
		log.info("created ratebook k="+bmKey+" , maxAA="+MAX_AGGREGATON_ATTEMPTS);
	}
	public RateBook(Integer ccypidx, FIProvision fiProvision,
					ServerProvision serverProvision, RateProcessor rateProcessor){
		this(ccypidx, fiProvision, serverProvision, rateProcessor, null, null, 0, false, 0, false);
	}

	private int getLPIndex(LPProvision lpProvision,ServerProvision serverProvision) {
		String lpName = lpProvision.getShortName();
		Optional<Integer> idx = serverProvision.getOrgIndex(lpName);
		return idx.orElse(-1);
	}

	public int processStreamStatusUpdate(int streamId, boolean enable, String lpName){
		ProvisioningCalculator calc = pcalcRateIndex.get(streamId);
		if (calc == null || !calc.getLp().equals(lpName)) {
			return -1;
		}
		// Enable/disable the calculator here
		if (enable) {
			calc.activateForStreamStatusUpdate();
		} else {
			calc.deActivateForStreamStatusUpdate();
			calc.withdrawRate();

			//reset time effective to trigger time-sliced aggregation immediately.
			//resetting this doesn't affect tick-by-tick.
			this.pricebook.setTimeEffective(0,0);

			//update this ratebook's position within rate processor.
			this.processor.getNotificationChangeSet().set(tasksetPosition);

			//return index of rate processor assigned to this rate book;
			return processor.getProcessorId();
		}
		return -1;
	}

    public int processLRLPUpdate(int streamId, boolean enable) {
        ProvisioningCalculator calc = pcalcRateIndex.get(streamId);
        if (calc == null) {
            return -1;
        }
        // Enable/disable the calculator here
        if (enable) {
            calc.activateForLRLPUpdate();
        } else {
            calc.deActivateForLRLPUpdate();
            calc.withdrawRate();

            //reset time effective to trigger time-sliced aggregation immediately.
			//resetting this doesn't affect tick-by-tick.
			this.pricebook.setTimeEffective(0,0);
            //update this ratebook's position within rate processor.
    		this.processor.getNotificationChangeSet().set(tasksetPosition);

    		//return index of rate processor assigned to this rate book;
    		return processor.getProcessorId();
        }
        return -1;
    }

	/**
	 *
	 * @param fromStreamindex
	 * @param toStreamIndex
	 * @param lp
	 * @param state
	 * @return true if swith is successful.
	 */
	public boolean switchStream(int fromStreamindex,int toStreamIndex, LPProvision lp, boolean state){
		ProvisioningCalculator calc = pcalcRateIndex.get(fromStreamindex);
		if (calc == null || !calc.getLp().equals(lp.getShortName())) {
			log.info("no calculator associated with sIdx="+fromStreamindex + ", " + rateMetricsKey );
			return false;
		}
		calc = pcalcRateIndex.remove(fromStreamindex);

		String mKey = rateMetricsKey + ", lp=" + getLPName(lp) + ", s=" + lp.getStreamName() + ", sIdx=" + toStreamIndex;
		calc.onStreamSwitch(lp, mKey, fiKey, state);
		pcalcRateIndex.put(toStreamIndex, calc);

		//reset time effective to trigger time-sliced aggregation immediately. In case of stream switch, next agggregation
		//happens on next update valid for this book.
		//resetting this doesn't affect tick-by-tick.
		this.pricebook.setTimeEffective(0,0);

		return true;
	}

	private String getLogKey(String fiName,Optional<String> bccy,Optional<String> vccy) {
		StringBuilder lKey = new StringBuilder();
		lKey.append(fiName).append('|');
		if(bccy.isPresent()){
			lKey.append(bccy.get()).append('|');
		}
		if(vccy.isPresent()){
			lKey.append(vccy.get()).append('|');
		}
		return lKey.toString();
	}

	private double getTickValue(ServerProvision serverProvision, int baseCcyIdx) {
		Optional<CurrencyProvision> ccyProvision = serverProvision.getCcyProvision(baseCcyIdx);
		if(ccyProvision.isPresent()){
			 double value = ccyProvision.get().getTickValue();
			 if(value != 0.0d){
				 return value;
			 }else{
				 log.info("Using Default tick value for the currnecy idx:"+baseCcyIdx+", actual tick value:"+value);
			 }
		}

		return  DEFAULT_TICK_VALUE;
	}

	private int getRoundingType(ServerProvision serverProvision, int baseCcyIdx) {
		Optional<CurrencyProvision> ccyProvision = serverProvision.getCcyProvision(baseCcyIdx);
		if(ccyProvision.isPresent()){
			return ccyProvision.get().getRoundingType();
		}
		return DEFAULT_ROUNDING_TYPE;
	}

	private int getPrecision(ServerProvision serverProvision,
			Optional<String> ccyPairName) {
		if (ccyPairName.isPresent()) {
			Optional<CurrencyPairProvision> ccyPairProvision = serverProvision
					.getCcyPairProvision(ccyPairName.get());
			if (ccyPairProvision.isPresent()) {
				return ccyPairProvision.get().getSpotPrecision();
			}
		}
		return DEFAULT_PRECISION;
	}

	/**
	 *
	 * @param rate
	 * @return id of the rate processor which is assigned to process this ratebook. Calling thread will
	 * notify the parked rate processor to start working on new update.
	 */
	public int handleUpdate(QuoteC rate){

		//make copy
		QuoteC local = rawRatePool.borrowObject();
		local.readFrom(rate);

		int streamid = local.getStreamIdx();
		ProvisioningCalculator calc = pcalcRateIndex.get(streamid);
		boolean res = calc.onRate(local);

        if(res){
            //clear the this ratebook's position within rate processor.
            this.processor.getNotificationChangeSet().set(tasksetPosition);
        } else{
            //update failed because ProvisioningCalculator wasn't active, return local back to pool.
            rawRatePool.returnObject(local);//
        }

		//return index of rate processor assigned to this rate book;
		//return processor.getProcessorId();
		return -1;
	}

	/**
	 * it is assumed that handleUpdate method will not be called while this
	 * is getting. Externally synchronized.
	 * @return
	 */
	public int withdrawAllLastRates() {

		for(ProvisioningCalculator pcalc : pcalcRateIndex.values()){
			pcalc.withdrawRate();
		}

		//reset time effective to trigger time-sliced aggregation immediately.
		//resetting this doesn't affect tick-by-tick.
		this.pricebook.setTimeEffective(0,0);

		//update this ratebook's position within rate processor.
		this.processor.getNotificationChangeSet().set(tasksetPosition);

		//return index of rate processor assigned to this rate book;
		return processor.getProcessorId();
	}
	
	
	/**
	 * it is assumed that handleUpdate method will not be called while this
	 * is getting. Externally synchronized.
	 * @return
	 */
	public int withdrawLastRate(int streamIndex) {
		
		//withdraw last published rate
		ProvisioningCalculator calc = pcalcRateIndex.get(streamIndex);
		calc.withdrawRate();

		//reset time effective to trigger time-sliced aggregation immediately.
		//resetting this doesn't affect tick-by-tick.
		this.pricebook.setTimeEffective(0,0);

		//update this ratebook's position within rate processor.
		this.processor.getNotificationChangeSet().set(tasksetPosition);

		//return index of rate processor assigned to this rate book;
		return processor.getProcessorId();
	}

	public boolean doesCalculatorExist(){
		return !pcalcCreditIndex.isEmpty();
	}

	public void handleCreditUpdate(CreditLimitInfo creditLimit){

	//	if( true ) {

			for(List<ProvisioningCalculator> calculators:pcalcCreditIndex.values()) {
				if(null != calculators && !calculators.isEmpty()){
					for (int i=0;i<calculators.size();i++) {
						calculators.get(i).onCredit(creditLimit);
					}
				}
			}

//		}else {
//			List<ProvisioningCalculator> calculators =  pcalcCreditIndex.get(creditLimit.getLpLe());
//			if(null != calculators && !calculators.isEmpty()){
//				for (int i=0;i<calculators.size();i++) {
//					calculators.get(i).onCredit(creditLimit);
//				}
//			}else{
//				if(log.isDebugEnabled()){
//					log.debug("Dropping the credit limit for credit holder le:"+creditLimit.getLpLe());				
//				}
//			}
//		}
	}

	public void handleCreditUpdate(short valueDate,byte status,long limit, short limitccy){
		for(List<ProvisioningCalculator> calculators:pcalcCreditIndex.values()) {
			if(null != calculators && !calculators.isEmpty()){
				for (int i=0;i<calculators.size();i++) {
					calculators.get(i).onCredit(valueDate,status,limit,limitccy);
				}
			}
		}
	}

	/**
	 * if this method is call then there was an update on price.
	 * Aggregator interface can be thrown here.
	 * @return
	 */
	public PriceBook aggregate(){
		//1. add quotes - may fail because book is inverted.
		addQuotesWithRetry();
		//2. copy prices to cached out buffer
		PostAggregationAction result = pricebook.copyPrices(precision,roundingType,tickValue);
		if(customAggregation) pricebook.setFlag(PriceBook.FLAG_CUSTOM_AGG);

		if(result == PostAggregationAction.Send){
			//Millis - for monitoring between servers - //Nanos -> for time slicing for local use
			pricebook.setTimeEffective(System.currentTimeMillis(),System.nanoTime());
			pricebook.setBookId(pricebook.getBookId()+1);
			bm.numagg++;
			return pricebook;
		}else {
			return null;
		}
	}

	/*
	Returns last aggregated book. Not Thread-safe
	 */
	public PriceBook getPricebook(){
	    return pricebook;
    }


	private void addQuotesWithRetry() {
		try {
			boolean isNotAdded = true;
			int attempt = 1;

			//This entry set creation is a garbage so better to do it once instead of one each during retry .
			//We might miss if there are any update to this DS in current aggregation only.
			Set<Entry<Long, ProvisioningCalculator>> entrySet = pcalcRateIndex.entrySet();

			while( isNotAdded && attempt <= MAX_AGGREGATON_ATTEMPTS){
				pricebook.reset();
				isNotAdded = addQuotes(entrySet);
				attempt++;
				bm.numAggAttempts++;
			}

			//If the max retry is exhausted or providers exhausted
			if(isNotAdded){
				bm.numAggFail++;
				//if rate is inverted clear the book before copying
				pricebook.reset();
			}
		} finally {
			staleProviderKeys.clear();
		}
	}

	/**
	 *
	 * @param entrySet
	 * @return isNotAggregated - true if method fails to aggregate
	 */
	protected boolean addQuotes(Set<Entry<Long, ProvisioningCalculator>> entrySet) {

		long oldestQuoteReceivedTime=Long.MAX_VALUE;
		long latestQRecvtime = 0;
		long latestQId = 0;
		long oldestQId=0;
		ProvisionedQuoteC provisionedQuoteC=null;

		for(Map.Entry<Long,ProvisioningCalculator> entry:entrySet){
			if(!providers.isEmpty()){
				if(!providers.contains(entry.getKey())) {
					if(log.isDebugEnabled()) log.debug("Skipping provider :"+entry.getKey());
					continue;
				}
			}
			//make sure channel is active and rate is not stale.
			if(! staleProviderKeys.contains(entry.getKey()) && entry.getValue().isActive() ){
				provisionedQuoteC = entry.getValue().getProvisionedRate();
				if(provisionedQuoteC.getQuoteCreatedTime() > latestQRecvtime  ){//latest
					latestQRecvtime = provisionedQuoteC.getQuoteCreatedTime();
					latestQId = provisionedQuoteC.getQuoteId();
				}
				//skip previously marked quotes.
				if(!provisionedQuoteC.isActive()){
					staleProviderKeys.add(entry.getKey());
					continue;
				}
				if(provisionedQuoteC.getReceivedTime().toEpochMilli() < oldestQuoteReceivedTime){//oldest
					oldestQId = provisionedQuoteC.getQuoteId();
					oldestQuoteReceivedTime = provisionedQuoteC.getReceivedTime().toEpochMilli();
				}
				pricebook.addPrices(provisionedQuoteC);
			}
		}

		long olderQuoteProviderKey = pricebook.isInverted();
		if(olderQuoteProviderKey!=0){
			//We take an optimistic approach of removing only one stale quote at a time and retrying
			//Oldest quote in the system is the stale quote
			log.info(">>>>>>> Removing stale quote " + olderQuoteProviderKey + " " + pricebook.getBookId());
			staleProviderKeys.add(olderQuoteProviderKey);
			//Remove the oldest received quote in MDF server.
			return true;
		}

		pricebook.setQuoteId(latestQId);
		pricebook.setQuoteTimeEffective(latestQRecvtime);
		pricebook.setOldestQId(oldestQId);
		return false;
	}

	public void activate() {
		this.isActive = true;
		this.bm.active = 1;
	}

	public void deActivate() {
		this.isActive = false;
		this.bm.active = 0;
		MetricsManager.instance().unregister(this.bm);
	}

	public boolean isActive() {
		return this.isActive;
	}

	public int getFIIndex(){
		return this.fiOrgIndex;
	}

	public Integer getCcypIdx() {
		return ccypidx;
	}

//	public RateProcessor getRateProcessor() {
//		return processor;
//	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ccypidx;
		result = prime * result + fiOrgIndex;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		RateBook other = (RateBook) obj;
		if (fiOrgIndex != other.fiOrgIndex)
			return false;
		if (ccypidx != other.ccypidx)
			return false;
		return true;
	}

	private String getRateMetricsKey(int bIdx, int vIdx,String fiName, byte processorIndex,
									 ServerProvision serverProvision, MDFAggregationType at,
									 double requestedSize, List<String> providers, boolean isTermCcyAgg) {
		StringBuilder mKey = new StringBuilder();
		mKey.append(RM)
		.append(", rp=").append(processorIndex)
		.append(", fi=").append(fiName)
		.append(", b=").append(serverProvision.getCcyName(bIdx).get())
		.append(", v=").append(serverProvision.getCcyName(vIdx).get())
		.append(", at=").append(Util.getStringKey(at));
		if(requestedSize>0) mKey.append(", rs=").append(requestedSize);
		if(providers!=null && !providers.isEmpty()) mKey.append(", pr=").append(providers);
		if(isTermCcyAgg) mKey.append(", tca=true");
		return mKey.toString();
	}

	protected String getLPName(LPProvision lpProvision) {
		String lpName = lpProvision.getShortName();
		//if there is a real LP and its different from the LP name include the same
		if(lpProvision.getRealLP()!=null && !lpProvision.getRealLP().equals(lpName)){
			lpName = lpName +"("+lpProvision.getRealLP()+")";
		}
		return lpName;
	}

	private String getFIKey(FIProvision fiProvision, int baseccyIdx,
			int varccyIndex, ServerProvision serverProvision) {
		StringBuilder fiKey = new StringBuilder();
		Optional<String> ccyName = serverProvision.getCcyName(baseccyIdx);
		fiKey.append(fiProvision.getName()).append('|');
		if(ccyName.isPresent()) {
			fiKey.append(ccyName.get()).append('|');
		}
		ccyName = serverProvision.getCcyName(varccyIndex);
		if(ccyName.isPresent()){
			fiKey.append(ccyName.get()).append('|');
		}
		fiKey.append(Util.getStringKey(fiProvision.getAggregationType()));
		return fiKey.toString();
	}

	private String getBookMetricsKey(int bIdx, int vIdx, String fiName,
									 byte processorIndex, ServerProvision serverProvision,
									 MDFAggregationType aggregationType) {
		StringBuilder mKey = new StringBuilder();
		mKey.append(BM)
		.append(", rp=").append(processorIndex)
		.append(", fi=").append(fiName)
		.append(", b=").append(serverProvision.getCcyName(bIdx).get())
		.append(", v=").append(serverProvision.getCcyName(vIdx).get())
		.append(", at=").append(Util.getStringKey(aggregationType));
		return mKey.toString();
	}

	public class BookMetrics implements Metrics {
		public int numagg;
		public int inactDrp;
		public int numAggAttempts;
		public int numAggFail;
		private String mKey;
		public byte active = 1;
		StringBuilder message = new StringBuilder(64);

		public BookMetrics(String mKey) {
			this.mKey = mKey;
		}

		@Override
		public StringBuilder report() {
			message.setLength(0);
			message.append(mKey);
			message.append(", act=").append(active);
			message.append(", agg=").append(numagg);
			message.append(", inadrp=").append(inactDrp);
			message.append(", agga=").append(numAggAttempts);
			message.append(", aggf=").append(numAggFail);
			this.numagg = 0;
			this.inactDrp = 0;
			this.numAggAttempts = 0;
			this.numAggFail = 0;
			return message;
		}
	}
	
	public String getBookKey(){
		//same as fiKey; FI|Base|Var
		return fiKey;
	}

	public void unregisterMetrics(){
		MetricsManager.instance().unregister(bm);
	}

}
