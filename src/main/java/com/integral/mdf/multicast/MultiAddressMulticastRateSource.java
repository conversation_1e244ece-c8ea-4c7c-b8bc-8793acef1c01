package com.integral.mdf.multicast;


import com.integral.mdf.data.ServerProvision;
import com.integral.mdf.rate.RateDistributionManager;

import java.io.IOException;
import java.net.InetAddress;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

public class MultiAddressMulticastRateSource extends MulticastRateSource {

    ConcurrentHashMap<String,Boolean> subscribedCcyps = new ConcurrentHashMap<>(70);

    public MultiAddressMulticastRateSource(ServerProvision provision, RateDistributionManager dm) throws IOException {
        super(provision, dm);
    }

    @Override
    public void start(String ccyp) {
        if (ccyp == null) {
            log.info("MAMRS ccyp is null.");
            return;
        }

        Boolean currentState = subscribedCcyps.get(ccyp);

        if(currentState==null){
            Boolean newState = Boolean.TRUE;
            currentState = subscribedCcyps.putIfAbsent(ccyp,newState);
            if(currentState==null){
                currentState = newState;
            }
        }else{
            log.info("MAMRS - ccyp already subscribed. ccyp="+ccyp);
            return;
        }


        // 1. start listening join group....
        Optional<InetAddress> multicastgroup = provision.getRateMulticastgroup(ccyp);
        if (!multicastgroup.isPresent()) {
            log.info("MAMRS .Not joining the group since there's no multicast group defined for ccyp="+ccyp);
            return;
        }

        InetAddress mcastaddress = multicastgroup.get();
        try {
            socket.joinGroup(mcastaddress);
        } catch (IOException e) {
            log.warn("MAMRS. already joined multicast group:"+mcastaddress.toString());
            return;
        }
        log.info("MAMRS.start rate listener port="+this.port+", address="+multicastgroup.get() + ", ccyp="+ccyp);
    }
}
