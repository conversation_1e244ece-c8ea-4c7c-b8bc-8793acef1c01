package com.integral.mdf.multicast;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.util.Optional;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.PriceBookSink;
import com.integral.mdf.data.PriceBook;
import com.integral.mdf.data.ServerProvision;

public class MulticastPriceBookSink implements PriceBookSink {

	final int port;
	final MulticastSocket socket;
	ServerProvision provision;
	boolean isActive = true;
	final byte[] buffer;
	final UnSafeBuffer safeBuf = new UnSafeBuffer();
	Log log = LogFactory.getLog(this.getClass());

	public MulticastPriceBookSink(ServerProvision provision) throws IOException {
		this.provision = provision;
		this.port = provision.getPriceBookMulticastPort();
		buffer = new byte[PriceBook.getEstimatedSize(provision.getMaxPriceBookDepth())];
		this.socket = new MulticastSocket();
		this.socket.setTimeToLive(provision.getMulticastTTL());
		safeBuf.init(buffer);
	}

	@Override
	public void accept(PriceBook book) {
		Optional<InetAddress> address = provision.getPriceBookMulticastgroup(book.getFIIndex());
		if (address.isPresent()) {
			book.writeTo(safeBuf);
			try {
				this.socket.send(new DatagramPacket(buffer, buffer.length, address.get(), port));
			} catch (Exception e) {
				if(log.isDebugEnabled()){
					log.debug("failed to send message. m=" + book.toString());
				}
			} finally {
				safeBuf.resetPosition();
			}
		}else{
			log.warn("destination mcast address is not present in cache. fi="+book.getFIIndex());
		}
	}

	@Override
	public void begin() {
		this.isActive = true;
	}

	@Override
	public void end() {
		this.isActive = false;
	}

	@Override
	public boolean isActive() {
		return this.isActive;
	}

}
