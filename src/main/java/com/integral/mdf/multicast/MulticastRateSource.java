package com.integral.mdf.multicast;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.RateSource;
import com.integral.mdf.data.QuoteC;
import com.integral.mdf.data.ServerProvision;
import com.integral.mdf.rate.RateDistributionManager;
import com.integral.pipeline.metrics.Metric;
import com.integral.pipeline.metrics.Metrics;
import com.integral.pipeline.metrics.MetricsManager;
import org.HdrHistogram.Histogram;

public class MulticastRateSource implements RateSource {

	final int port;
	final MulticastSocket socket;
	ServerProvision provision;
	volatile boolean shutdown = false;
	final RateDistributionManager dm;
	Log log = LogFactory.getLog(MulticastRateSource.class);
	AtomicBoolean isstarted = new AtomicBoolean(false);
	Histogram iot = new Histogram(3);
	Histogram prt = new Histogram(3);

	public MulticastRateSource(ServerProvision provision, RateDistributionManager dm) throws IOException {
		
		this.port = provision.getRateMulticastPort();
		socket = new MulticastSocket(port);
		socket.setReuseAddress(true);
		this.provision = provision;
		this.dm = dm;
		this.dm.setRateSource(this);
		MetricsManager.instance().register(new ProcessTimeMetrics());
		new Thread(new Worker(), "mcast-reader").start();
	}

	@Override
	public void start(String ccyp) {

		if(!isstarted.compareAndSet(false,true)){
			log.info("Already running. ccyp="+ccyp);
			return;
		}

		// 1. start listening join group....
		Optional<InetAddress> multicastgroup = provision.getRateMulticastgroup();
		if (!multicastgroup.isPresent()) {
			log.info("Not joining the group since there's no multicast group defined");
			return;
		}

		InetAddress mcastaddress = multicastgroup.get();
		try {
			socket.joinGroup(mcastaddress);
		} catch (IOException e) {
			log.warn("Error joining multicast group:"+mcastaddress.toString(),e);
			return;
		}
		log.info("MRS.start rate listener port="+this.port+", address="+multicastgroup.get());
	}

	class Worker implements Runnable {

		QuoteC rate = new QuoteC();
		byte[] buffer;

		public Worker() {
			buffer = rate.buffer().byteArray();
		}

		@Override
		public void run() {
			DatagramPacket dp = new DatagramPacket(buffer, buffer.length);
			do {
				try {
					long st = System.nanoTime();
					socket.receive(dp);
					long mt = System.nanoTime();
					dm.handleRate(rate);
					long et = System.nanoTime();
					iot.recordValue(mt-st);
					prt.recordValue(et-mt);
				} catch (Exception e) {
					log.warn("MRS.worker.run():Unable to handle multicast datagram packet.",e);
				} 
			} while (!shutdown);
		}
	}

	@Override
	public void stop(String ccyp) {

		// 1. stop listening leave group....
		Optional<InetAddress> multicastgroup = provision.getRateMulticastgroup();
		if (!multicastgroup.isPresent()) {
			log.info("Not leaving the group since there's no multicast group defined");
			return;
		}

		InetAddress mcastaddress = multicastgroup.get();
		try {
			socket.leaveGroup(mcastaddress);
		} catch (IOException e) {
			log.warn("Error leaving multicast group:"+mcastaddress.toString(),e);
		}
		log.info("MRS.stop rate listener port="+this.port+", address="+multicastgroup.get());
	}

	class ProcessTimeMetrics implements Metrics{

		StringBuilder message = new StringBuilder(400);

		@Override
		public StringBuilder report() {
			message.setLength(0);
			message.append("k=mio ");
			message.append(", ").append(getString(iot,"io",1.0d));
			message.append(", ").append(getString(prt,"prt",1.0d));
			iot.reset();
			prt.reset();
			return message;
		}

		public StringBuilder getString(Histogram histogram,String key, double divisor){
			StringBuilder report = new StringBuilder(key);
			report.append("[ c=").append(histogram.getTotalCount());
			report.append(" m=").append(histogram.getMinValue()/divisor);
			report.append(",").append( histogram.getValueAtPercentile(50.00)  / divisor );
			report.append(",").append( histogram.getValueAtPercentile(90.00)  / divisor );
			report.append(",").append( histogram.getValueAtPercentile(99.00)  / divisor );
			report.append(",").append( histogram.getValueAtPercentile(99.90)  / divisor );
			report.append(",").append( histogram.getValueAtPercentile(99.99)  / divisor );
			report.append(",").append( histogram.getValueAtPercentile(100.00)  / divisor );
			report.append("]");
			return report;
		}
	}
}
