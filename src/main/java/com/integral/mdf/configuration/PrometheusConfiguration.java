package com.integral.mdf.configuration;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.sun.net.httpserver.HttpServer;
import io.micrometer.core.instrument.binder.jvm.ClassLoaderMetrics;
import io.micrometer.core.instrument.binder.jvm.JvmGcMetrics;
import io.micrometer.core.instrument.binder.jvm.JvmMemoryMetrics;
import io.micrometer.core.instrument.binder.jvm.JvmThreadMetrics;
import io.micrometer.core.instrument.binder.system.ProcessorMetrics;
import io.micrometer.core.instrument.binder.system.UptimeMetrics;
import io.micrometer.prometheus.PrometheusConfig;
import io.micrometer.prometheus.PrometheusMeterRegistry;
import java.io.OutputStream;
import java.net.InetSocketAddress;

public class PrometheusConfiguration {

    private static final Log LOG = LogFactory.getLog(PrometheusConfiguration.class);

    private static final PrometheusConfiguration INSTANCE = new PrometheusConfiguration();

    public static PrometheusConfiguration getInstance() {
        return INSTANCE;
    }

    public PrometheusMeterRegistry getPrometheusRegistry() {
        return prometheusRegistry;
    }

    private PrometheusConfiguration() {
    }

    private PrometheusMeterRegistry prometheusRegistry = new PrometheusMeterRegistry(PrometheusConfig.DEFAULT);

    private boolean serverStarted = false;
    private Thread serverTread;
    private HttpServer server;

    public synchronized void startServer(String serverName) {

        if (!serverStarted) {
            prometheusRegistry.config().commonTags("server.name", serverName);
            prometheusRegistry.config().commonTags("application", serverName);
            new ClassLoaderMetrics().bindTo(prometheusRegistry);
            new JvmMemoryMetrics().bindTo(prometheusRegistry);
            new JvmGcMetrics().bindTo(prometheusRegistry);
            new ProcessorMetrics().bindTo(prometheusRegistry);
            new JvmThreadMetrics().bindTo(prometheusRegistry);
            new UptimeMetrics().bindTo(prometheusRegistry);

            try {
                server = HttpServer.create(new InetSocketAddress(0), 0);
                server.createContext("/metrics", httpExchange -> {
                    String response = prometheusRegistry.scrape();
                    httpExchange.sendResponseHeaders(200, response.getBytes().length);
                    try (OutputStream os = httpExchange.getResponseBody()) {
                        os.write(response.getBytes());
                    }
                });

                server.createContext("/check", httpExchange -> {
                    String response = "healthy";
                    httpExchange.sendResponseHeaders(200, response.getBytes().length);
                    try (OutputStream os = httpExchange.getResponseBody()) {
                        os.write(response.getBytes());
                    }
                });

                serverStarted = true;
                serverTread = new Thread(server::start);
                serverTread.start();
                LOG.info("HTTP address: " + server.getAddress());
            } catch (Exception e) {
                LOG.error("Could not start server", e);
            }
        }
    }

    public synchronized void stopServer() {
        if (serverStarted) {
            server.stop(5);
        }
    }

    public int getPort() {
        if (serverStarted) {
            return server.getAddress().getPort();
        }
        return 0;
    }

}
