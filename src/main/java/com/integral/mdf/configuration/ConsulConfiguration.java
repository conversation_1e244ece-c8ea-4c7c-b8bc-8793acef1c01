package com.integral.mdf.configuration;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.orbitz.consul.AgentClient;
import com.orbitz.consul.Consul;
import com.orbitz.consul.model.agent.ImmutableRegistration;
import com.orbitz.consul.model.agent.Registration;
import java.net.InetAddress;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ConsulConfiguration {

    private static final Log LOG = LogFactory.getLog(ConsulConfiguration.class);

    private Consul client;
    private AgentClient agentClient;
    private List<Registration> services = new ArrayList<>();


    public ConsulConfiguration(String sdAddress) {
        client = Consul.builder().withUrl(sdAddress).build();
        agentClient = client.agentClient();
    }

    public void registerService(String serverName, String serviceName, int httpPort) {
        try {
            String serviceId = serverName;
            String httpCheckUrl = "http://" + InetAddress.getLocalHost().getHostAddress() + ":" + httpPort + "/check";
            Registration service = ImmutableRegistration.builder()
                    .id(serviceId)
                    .name(serviceName)
                    .port(httpPort)
                    .address(InetAddress.getLocalHost().getHostAddress())
                    .check(Registration.RegCheck.http(httpCheckUrl, 10, 10))
                    .tags(Collections.singletonList(serverName))
                    .meta(Collections.singletonMap("version", "1.0"))
                    .build();

            agentClient.register(service);

            services.add(service);
            LOG.info("service registered");
        } catch (Exception e) {
            LOG.error("error while registering consul service", e);
        }
    }

    public void unregisterServices(){
        try {
            services.forEach(s -> agentClient.deregister(s.getId()));
        } catch (Exception e) {
            LOG.error("error while unregistering consul service", e);
        }
    }
}
