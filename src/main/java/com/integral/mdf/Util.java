package com.integral.mdf;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.data.ServerProvision;
import com.integral.notifications.mdf.SubscriptionRequest;
import com.integral.notifications.mdf.SubscriptionResponse;
import com.integral.provision.LPProvision;
import com.integral.provision.MDFAggregationType;

/**
 * its util. This class shouldn't use com.integral.Log
 * <AUTHOR>
 *
 */
public class Util {
	
	public static final String CURRENCY_SEPARATOR = "/";
	
	static Log log = LogFactory.getLog(Util.class);

	public static ExecutorService housekeeper = Executors.newSingleThreadExecutor(new ThreadFactory() {
		
		public Thread newThread(Runnable r) {
			return new Thread(r,"housekeeper");
		}
	});

	public static ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor(new ThreadFactory() {
		
		@Override
		public Thread newThread(Runnable r) {
			return new Thread(r,"scheduler");
		}
	});

	
	/**
	 * Returns index of currencypair
	 * @param ccyPairName
	 * @param ccyNamesVsIndex
	 * @return
	 */
	static public Optional<Integer> getCurrencyPairIndex(String ccyPairName,Map<String, Integer> ccyNamesVsIndex )
    {
		String[] split = ccyPairName.split(CURRENCY_SEPARATOR);

		if (split.length != 2) {
			log.warn("Util.getCurrencyPairIndex():Unable to load currency pair, ignoring value.Invalid currency pair format:"
					+ ccyPairName);
			return Optional.empty();
		}

		try {
			int baseccyIdx = ccyNamesVsIndex.get(split[0]);
			int varccyIndex = ccyNamesVsIndex.get(split[1]);
			return Optional.of(getCurrencyPairIndex(baseccyIdx,varccyIndex));
		} catch (Exception iae) {
			log.warn("Util.getCurrencyPairIndex():Unable to lookup currency index for currency pair:" + ccyPairName,iae);
		}
		return Optional.empty();
    }

	
	
	/**
	 * Returns index of currencypair
	 * @param baseccyIdx
	 * @param varccyIndex
	 * @return
	 */
	static public int getCurrencyPairIndex(int baseccyIdx, int varccyIndex)
    {
        int index = 0;
        index = baseccyIdx;
        index <<= 16;

        index |= ( varccyIndex & 0xFFFF );
        return index;
    }

	
	/**
	 * Returns index of base currency from composite currency pair index.
	 * @param ccypIndex
	 * @return
	 */
	static public int getBaseCurrencyIndex(int ccypIndex)
    {
        int index = 0;
        index = (ccypIndex >> 16) & 0xFFFF;
        return index;
    }
	
	/**
	 * Returns index of variable currency from composite currency pair index.
	 * @param ccypIndex
	 * @return
	 */
	static public int getVarCurrencyIndex(int ccypIndex)
    {
        return ccypIndex & 0xFFFF;
    }
	
	
	static public long getRateChannelKey(int streamIndex, int baseCcyIndex,int varCcyIndex){
		long index = 0;
        index = streamIndex;
        
        index <<= 16;
        index |= baseCcyIndex & 0xFFFF;

        index <<= 16;
        index |= varCcyIndex & 0xFFFF;

        return index;
	}
	
	static public long getRateBookKey(int fiIndex, int baseCcyIndex,int varCcyIndex, int aggrType){
		long index = 0;

		index = aggrType;

		index <<= 30;
		index |= fiIndex & 0xFFFF;

        index <<= 15;
        index |= baseCcyIndex & 0xFFFF;

        index <<= 15;
        index |= varCcyIndex & 0xFFFF;

        return index;
	}
	
	public static int getStreamIndex (ServerProvision provision,LPProvision lpProvision ){
		return provision.isUseSuperLPStreamIndex() ? lpProvision.getStreamSuperLPIndex() : (int) lpProvision.getStreamIndex();
	}

	public static String getStringKey(MDFAggregationType mdfAggregationType){
		switch (mdfAggregationType){
			case FULL_BOOK: return "FBA";
			case RAW_BOOK:  return "RBA";
			case WEIGHTED_AVERAGE: return "VWAP";
			case BEST_PRICE: return "BPA";
			case MULTI_TIER_FOK: return "MTFOK";
			case FA_MULTI_TIER: return "MTFA";
			case RAW_DIRECT: return "RDA";
			default: return "NA";
		}
	}

	public static String stringify(SubscriptionRequest sr) {
		StringBuilder sb = new StringBuilder("[SReq");
		sb.append(",o=").append(sr.getOrgIndex());
		sb.append(",cp=").append(sr.getCurrencyPairIndex());
		sb.append(",at=").append(sr.getAggregationType());
		sb.append(",id=").append(sr.getId());
		sb.append(",typ=").append(sr.getType());
		sb.append(",sb=").append(sr.getSentBy());
		sb.append(",t=").append(Arrays.toString(sr.getTiers()));
		sb.append(", tv=").append(Arrays.toString(sr.getTierValues()));
		sb.append(",cus=").append(sr.isCustomAggregation());
		sb.append(",dc=").append(sr.getDealtCurrencyIndex());
		sb.append(", rqS=").append(sr.getRequestedSize());
		sb.append(", prL=").append(sr.getProviders());
		sb.append("]");
		return sb.toString();
	}

	public static String stringify(SubscriptionResponse sr) {
		StringBuilder sb = new StringBuilder("[SRes");
		sb.append(",id=").append(sr.getRequestId());
		sb.append(",s=").append(sr.getStatus());
		sb.append(",err=").append(sr.getError());
		sb.append(",msg=").append(sr.getMessage());
		sb.append("]");
		return sb.toString();
	}
	
	public static void main(String[] args) {
		System.out.println(getCurrencyPairIndex(1, 3));
	}
}
