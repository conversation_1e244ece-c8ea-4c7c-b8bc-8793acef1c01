package com.integral.mdf.cache.subscription;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.notifications.mdf.SubscriptionRequest;

import java.util.*;

public class SubscriptionCacheManagerC implements SubscriptionCacheManager {

    public static final String MDF_SUBSCRIPTION_CACHE = "mdf.subscription.cache";
    public static final String SEPARATOR = "_";

    private final IMap<String, Map<String, SubscriptionCache>> cache;
    private static SubscriptionCacheManager INSTANCE;
    private static boolean initialized;

    private static final Log LOG = LogFactory.getLog(SubscriptionCacheManagerC.class);

    public SubscriptionCacheManagerC(HazelcastInstance cacheInstance) {
        cache = cacheInstance.getMap(MDF_SUBSCRIPTION_CACHE);
    }

    public static void init(HazelcastInstance cacheInstance) {
        if (!initialized) {
            INSTANCE = new SubscriptionCacheManagerC(cacheInstance);
            initialized = true;
        }
    }

    public static SubscriptionCacheManager getInstance() {
        return INSTANCE;
    }

    @Override
    public void addSubscription(String key, SubscriptionRequest request, String sentBy) {
        Map<String, SubscriptionCache> map = cache.get(key);

        if (map == null) {
            map = new HashMap<>();
        }

        if(!request.isCustomAggregation()){
            for (long tier : request.getTiers()) {
                String requestKey = getRequestKey(request, tier);
                SubscriptionCache sc;
                if (map.containsKey(requestKey)) {
                    sc = map.get(requestKey);
                } else {
                    sc = getNewRequest(request, tier);
                    map.put(requestKey, sc);
                }
                sc.addSentBy(sentBy);
            }
        } else {
            String requestKey = getRequestKey(request, 0);
            SubscriptionCache sc;
            if (map.containsKey(requestKey)) {
                sc = map.get(requestKey);
            } else {
                sc = getNewRequest(request, 0);
                map.put(requestKey, sc);
            }
            sc.addSentBy(sentBy);
        }
        LOG.debugAsFormat("Added cache : ", map);
        cache.set(key, map);
    }

    private SubscriptionCache getNewRequest(SubscriptionRequest request, long tier) {
        SubscriptionCache newRequest = new SubscriptionCache();
        newRequest.setId(request.getId());
        newRequest.setOrgIndex(request.getOrgIndex());
        newRequest.setCurrencyPairIndex(request.getCurrencyPairIndex());
        newRequest.setAggregationType(request.getAggregationType());
        newRequest.setType(request.getType());
        if(!request.isCustomAggregation()) {
            newRequest.setTiers(new long[]{tier});
        } else {
            newRequest.setCustomAggregation(true);
            newRequest.setTiers(request.getTiers());
            newRequest.setTierValues(request.getTierValues());
            newRequest.setRequestedSize(request.getRequestedSize());
            newRequest.setDealtCurrencyIndex(request.getDealtCurrencyIndex());
            newRequest.setProviders(request.getProviders());
        }
        return newRequest;
    }

    private String getRequestKey(SubscriptionRequest request, long tier) {
        String key = request.getAggregationType().toString()
                + SEPARATOR + request.getOrgIndex()
                + SEPARATOR + request.getCurrencyPairIndex();
        if(!request.isCustomAggregation()){
            key += SEPARATOR + tier;
        }else {
            key += SEPARATOR + "custom";
            if(request.getTiers() != null) key += SEPARATOR + Arrays.toString(request.getTiers());
            if(request.getTierValues() != null) key += SEPARATOR + Arrays.toString(request.getTierValues());
            if(request.getRequestedSize() > 0) key += SEPARATOR + request.getRequestedSize();
            if(request.getProviders() != null && !request.getProviders().isEmpty()) key += SEPARATOR + request.getProviders();
            if(request.getDealtCurrencyIndex() > 0) key += SEPARATOR + request.getDealtCurrencyIndex();
        }
        return key;
    }

    @Override
    public void removeSubscription(String key, SubscriptionRequest request, String sentBy) {
        if (cache.containsKey(key)) {
            Map<String, SubscriptionCache> map = cache.get(key);
            if(!request.isCustomAggregation()){
                for (long tier : request.getTiers()) {
                    String requestKey = getRequestKey(request, tier);
                    if (map.containsKey(requestKey)) {
                        SubscriptionCache sc = map.get(requestKey);
                        sc.removeSentBy(sentBy);

                        if (sc.isSentByEmpty()) {
                            map.remove(requestKey);
                        }
                    }
                }
            } else {
                String requestKey = getRequestKey(request, 0);
                if (map.containsKey(requestKey)) {
                    SubscriptionCache sc = map.get(requestKey);
                    sc.removeSentBy(sentBy);
                    if (sc.isSentByEmpty()) {
                        map.remove(requestKey);
                    }
                }
            }
            LOG.debugAsFormat("Removed cache - New Cache : %s", map);
            cache.set(key, map);
        }
    }

    @Override
    public List<SubscriptionCache> getSubscriptions(String key) {
        if (initialized && cache.containsKey(key)) {
            return new ArrayList<>(cache.get(key).values());
        }
        return new ArrayList<>();
    }
}
