package com.integral.mdf.cache.subscription;

import com.integral.notifications.mdf.SubscriptionRequest;

import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class SubscriptionCache extends SubscriptionRequest implements Serializable {
    private Set<String> sentBySet;

    public SubscriptionCache() {
        sentBySet = new HashSet<>();
    }

    public void addSentBy(String sentBy) {
        sentBySet.add(sentBy);
    }

    public void removeSentBy(String sentBy) {
        sentBySet.remove(sentBy);
    }

    public boolean isSentByEmpty() {
        return sentBySet.isEmpty();
    }

    public Set<String> getAllSentBy() {
        return new HashSet<>(sentBySet);
    }

    @Override
    public String toString() {
        return "SubscriptionCache{" +
                "id='" + getId() + '\'' +
                ", orgIndex=" + getOrgIndex() +
                ", currencyPairIndex=" + getCurrencyPairIndex() +
                ", aggregationType=" + getAggregationType() +
                ", tiers=" + Arrays.toString(getTiers()) +
                ", tierValues=" + Arrays.toString(getTierValues()) +
                ", type=" + getType() +
                ", sentBySet=" + sentBySet +
                '}';
    }
}
