package com.integral.mdf.cache;

import com.hazelcast.config.Config;
import com.hazelcast.config.JoinConfig;
import com.hazelcast.config.MapConfig;
import com.hazelcast.config.NetworkConfig;
import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.ShutdownTask;

public class CacheClientC implements CacheClient, ShutdownTask {

    public static final String MDF_CACHE = "mdf.cache";
    public static final String MULTICAST_HEARTBEAT = "multicast.heartbeat";

    private final HazelcastInstance cacheInstance;// = Hazelcast.newHazelcastInstance(createConfig());
    private static CacheClientC INSTANCE = null;
    private static final Log LOG = LogFactory.getLog(CacheClientC.class);
    private static boolean initialized;

    private CacheClientC(String multicastAddress) throws Exception {
        cacheInstance = Hazelcast.newHazelcastInstance(createConfig(multicastAddress));
    }

    public static void init(String multicastAddress) {
        if (!initialized) {
            try {
                INSTANCE = new CacheClientC(multicastAddress);
                LOG.info("Distributed cache initialized.");
            } catch (Exception e) {
                LOG.error("Exception while initializing", e);
            }
            initialized = true;
        }
    }

    @Override
    public boolean shutdown() {
        if (initialized) {
            initialized = false;
            cacheInstance.shutdown();
            LOG.info("Distributed cache shutdown.");
        }
        return true;
    }

    public static CacheClientC getInstance() {
        return INSTANCE;
    }

    private Config createConfig(String multicastAddress) throws Exception {
        Config config;
        try {
            config = new Config();
            config.addMapConfig(mapConfig());

            NetworkConfig networkConfig = config.getNetworkConfig();

            /* Port for cache communication */
            networkConfig.setPort(0);

            JoinConfig joinConfig = networkConfig.getJoin();
            joinConfig.getMulticastConfig().setEnabled(true);

            joinConfig.getMulticastConfig().setMulticastGroup(multicastAddress);
        } catch (Exception e) {
            LOG.error("Error while fetching multicast address", e);
            throw e;
        }

        return config;
    }

    private MapConfig mapConfig() {
        return new MapConfig(MDF_CACHE);
    }

    @Override
    public void put(String key, Object value) {
        IMap<String, Object> map = cacheInstance.getMap(MDF_CACHE);
        LOG.debugAsFormat("key : %s, value : %s", key, value);
        map.put(key, value);
    }

    @Override
    public Object get(String key) {
        IMap<String, Object> map = cacheInstance.getMap(MDF_CACHE);
        Object result = map.get(key);
        LOG.debugAsFormat("result : %s", result);
        return result;
    }

    @Override
    public HazelcastInstance getCacheInstance() {
        return cacheInstance;
    }
}
