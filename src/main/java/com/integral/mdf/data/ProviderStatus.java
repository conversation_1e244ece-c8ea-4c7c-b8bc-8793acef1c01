package com.integral.mdf.data;

import com.integral.commons.buffers.UnSafeBuffer;

/**
 * Copied from com.integral.is.message.ProviderStatusC
 * <AUTHOR>
 *
 */
public class ProviderStatus {
	
	private String providerShortName = null;
    private int status = 0;
    private String statusReason = null;
    private long startTime = 0;
    private String virtualServer;
    private String hostName;
    private int imtpPort = -1;
    private String imtpVersionId;
    private String providerType;

    public String getProviderShortName() {
        return providerShortName;
    }

    public void setProviderShortName(String shortName) {
        this.providerShortName = shortName;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getStatusReason() {
        return statusReason;
    }

    public void setStatusReason(String statusReason) {
        this.statusReason = statusReason;
    }

    public long getAdaptorStartTime() {
        return startTime;
    }

    public void setAdaptorStartTime(long startTime) {
        this.startTime = startTime;
    }

    public String getVirtualServer() {
        return virtualServer;
    }

    public void setVirtualServer(String virtualServer) {
        this.virtualServer = virtualServer;
    }

    public String getIMTPVersionId() {
        return imtpVersionId;
    }

    public void setIMTPVersionId(String versionId) {
        imtpVersionId = versionId;
    }

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public int getImtpPort() {
        return imtpPort;
    }

    public void setImtpPort(int imtpPort) {
        this.imtpPort = imtpPort;
    }

    public String getProviderType() {
        return providerType;
    }

    public void setProviderType(String providerType) {
        this.providerType = providerType;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder();
        sb.append("ProviderStatusC");
        sb.append("{providerShortName='").append(providerShortName).append('\'');
        sb.append(", status=").append(status);
        sb.append(", statusReason='").append(statusReason).append('\'');
        sb.append(", startTime=").append(startTime);
        sb.append(", virtualServer='").append(virtualServer).append('\'');
        sb.append(", hostName='").append(hostName).append('\'');
        sb.append(", imtpPort=").append(imtpPort);
        sb.append(", imtpVersionId='").append(imtpVersionId).append('\'');
        sb.append(", providerType='").append(providerType).append('\'');
        sb.append('}');
        return sb.toString();
    }

	public void writeTo(UnSafeBuffer safeBuf) {
		safeBuf.putString(this.getVirtualServer());
		safeBuf.put((byte) this.getStatus());
		safeBuf.putString(this.getProviderShortName());
		safeBuf.putString((this.getHostName()));
		safeBuf.putString(this.getIMTPVersionId());
		safeBuf.putInt(this.getImtpPort());
		safeBuf.putLong(this.getAdaptorStartTime());
		safeBuf.putString(this.getProviderType());
	}

	public void readFrom(UnSafeBuffer safeBuf) {
		this.setVirtualServer(safeBuf.getString());
		this.setStatus((int) safeBuf.get());
		this.setProviderShortName(safeBuf.getString());
		this.setHostName(safeBuf.getString());
		this.setIMTPVersionId(safeBuf.getString());
		this.setImtpPort(safeBuf.getInt());
		this.setAdaptorStartTime(safeBuf.getLong());
		this.setProviderType(safeBuf.getString());
	}

	public int getEstimatedSize() {
		return 256;
	}
}