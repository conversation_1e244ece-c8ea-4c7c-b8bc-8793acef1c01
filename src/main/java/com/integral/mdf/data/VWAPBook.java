package com.integral.mdf.data;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.util.MathUtil;

import java.math.BigDecimal;
import java.util.Arrays;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2/1/2019.
 * <AUTHOR>
 */
public class VWAPBook extends PriceBookV2 {

    PriceTier[] bids = new PriceTier[1000];
    int bidCount = 0;
    PriceTier maxbid = new PriceTier(0.0d,0.0d,-1,0,0);
    PriceTier[] offers = new PriceTier[1000];
    int offerCount = 0;
    PriceTier lowestoffer = new PriceTier(Double.MAX_VALUE,0.0d,-1,0,0);

    static int MAX_TIER = 50;
    Log log = LogFactory.getLog(this.getClass());

    private StringBuilder builder = new StringBuilder();

    final double EPSILON = Double.valueOf("0.0000001");//6 digit precision.

    public VWAPBook(int maxDepth, double[] tiers, boolean isTermCcyAgg, long requestId) {
        super(maxDepth,FLAG_VWAP_BOOK, tiers, isTermCcyAgg, requestId);
        if( maxDepth > MAX_TIER ){
            throw new IllegalArgumentException("Invalid maxDepth. It can't be more then " + MAX_TIER);
        }
        log.info("VWAP Book created. maxDepth="+maxDepth+", isTermCcyAgg="+isTermCcyAgg+", requestId="+requestId + ", tiers="+Arrays.toString(tiers));
    }


    /**
     *
     */
    public void reset() {
        bidCount = 0;
        offerCount = 0;
        maxbid.prc = 0.0d;maxbid.streamidx = 0;maxbid.effectiveTime = 0;
        lowestoffer.prc = Double.MAX_VALUE;lowestoffer.streamidx = 0;lowestoffer.effectiveTime = 0;
        if(log.isDebugEnabled()){
            builder.setLength(0);
        }
    }

    /**
     * @return isInverted
     */
    public boolean addPrices(final ProvisionedQuoteC pq) {
        int tier = 0; PriceTier prev=null;
        while(tier < pq.getPBidTiersNum() ){
            int offset = pq.pTierOffset(ProvisionedQuoteC.BUY, tier);
            double prc = pq.getPPrice(offset), d=0.0d;
            if( (d= pq.getPShowQty(offset, isTermCcyAgg) ) > 0.0d ) {
                PriceTier ptier = new PriceTier(prc, pq.getPShowQty(offset, isTermCcyAgg), pq.getLPIndex(),
                        pq.getStreamIdx(),pq.getReceivedTime().toEpochMilli());
                ptier.prev = pq.isMultiTier() ? prev:null;
                bids[bidCount++] = ptier;
                if (prc > maxbid.prc) {
                    maxbid.prc = prc;
                    maxbid.streamidx = pq.getStreamIdx();
                    maxbid.effectiveTime = pq.getReceivedTime().toEpochMilli();
                }
                prev = ptier;
            }
            tier++;
        }

        tier = 0; prev=null;
        while(tier < pq.getPOfferTiersNum() ){
            int offset = pq.pTierOffset(ProvisionedQuoteC.SELL, tier);
            double prc = pq.getPPrice(offset), d=0.0d;
            if( (d= pq.getPShowQty(offset, isTermCcyAgg) ) > 0.0d ) {
                PriceTier ptier = new PriceTier(prc, pq.getPShowQty(offset, isTermCcyAgg), pq.getLPIndex(),
                        pq.getStreamIdx(),pq.getReceivedTime().toEpochMilli());
                ptier.prev = pq.isMultiTier() ? prev:null;
                offers[offerCount++] = ptier;
                if (prc < lowestoffer.prc) { //better offer
                    lowestoffer.prc = prc;
                    lowestoffer.streamidx=pq.getStreamIdx();
                    lowestoffer.effectiveTime=pq.getReceivedTime().toEpochMilli();
                }
                prev=ptier;
            }
            tier++;
        }

//        if(bidCount > 0 && offerCount > 0 ){
//            if(maxbid > lowestoffer ){
//                return true;
//            }
//        }

        //we added some prices successfully. send latest
        if( getValueDate() < pq.getValueDate()){
            setValueDate(pq.getValueDate());
        }

        if(log.isDebugEnabled()){
            builder.append(pq.getProvisionedQuoteString());
            builder.append(" ");
        }

        return false;
    }


    public PostAggregationAction copyPrices(int precision,int roundingType , double tickValue) {

        if(bidCount <= 0 && offerCount <= 0 ){
            //send zero price book. MDG treat it is as inactive quote.
            setNumBids(1);
            this.getBidPrices()[0]=0.0d;
            this.getBidQtys()[0]=0.0d;
            this.getBidNoLP()[0]=0;

            setNumOffers(1);
            this.getOfferPrices()[0]=0.0d;
            this.getOfferQtys()[0]=0.0d;
            this.getOfferNoLP()[0]=0;

            if(log.isDebugEnabled()){
                builder.append(logRate());
                log.info(builder.toString());
            }

            return PostAggregationAction.Send;
        }

        Arrays.sort(bids,0,bidCount, (o1,o2) -> {
            int a = Double.compare(o2.prc, o1.prc);
            if(a != 0) return a;
            a = (int)(o1.effectiveTime - o2.effectiveTime);
            if(a != 0) return a;
            return Double.compare(o2.qty,o1.qty);
        });


        Arrays.sort(offers,0,offerCount,(o1,o2)->{
            int a = Double.compare(o1.prc, o2.prc);
            if(a != 0) return a;
            a = (int)(o1.effectiveTime - o2.effectiveTime);
            if(a != 0) return a;
            return Double.compare(o2.qty,o1.qty);
        });


        int maxDepth = getMaxDepth();

        int c=0;
        int t=0;
        int k=0;
        double lastvwap = 0.0d;
        double lasttierlmt = 0.0d;
        double cqty=0.0d;
        double vol = 0.0d;
        final double[] tiers = getTiers();
        while(t<tiers.length && c < (maxDepth-1) && k < bidCount) {
            log.debug("loop start c="+c+", t="+t+", k="+k+", cqty="+cqty+", vol="+vol );

            double tierlmt = tiers[t];
            PriceTier entry = bids[k];
            double limit = entry.qty;
            double prc = entry.prc;

            if(entry.prev!=null){//assumption is prev always has better rate then this since it was multitier and MT prices are naturally sorted.
                log.debug("rolling back last MT rate");
                cqty = cqty - entry.prev.qty;
                vol = vol - entry.prev.qty * entry.prev.prc;
            }

            if (limit + cqty >= tierlmt) { //for first price it will be just qty.
                //split limit
                double diff = Math.abs(tierlmt - cqty);
                double vwap = (vol + diff * prc)/(cqty+diff);
                vwap = MathUtil.round(vwap, precision, BigDecimal.ROUND_FLOOR);

                log.debug("moving to next tier ");
                t++; //go to next tier

                //newly calculated vwap may be good for next few tiers as well
                if(lastvwap != 0.0d ){
                    if(lastvwap-vwap>EPSILON) {
                        this.getBidPrices()[c] = lastvwap;
                        this.getBidQtys()[c] = MathUtil.round(lasttierlmt, tickValue, roundingType);
                        c++;
                        //reset vwap
                        lastvwap = vwap;
                        lasttierlmt = tierlmt;
                    }else{
                        lasttierlmt = tierlmt;
                    }
                }else{
                    lastvwap = vwap;
                    lasttierlmt = tierlmt;
                }


                if(entry.prev!=null){
                    //fix cqty before going to next tier
                    log.debug("put back current MT rate");
                    cqty += entry.prev.qty;
                    vol += entry.prev.qty * entry.prev.prc;
                }
            } else {
                //need more prices
                log.debug("not enough liquidity ");
                cqty += limit;
                vol += limit * prc;
                k++;

                //special cases
                if(k>=bidCount){//ran out of liquidity

                    //clear current tier
                    if(lastvwap != 0.0d ) {
                        this.getBidPrices()[c] = MathUtil.round(lastvwap, precision, BigDecimal.ROUND_FLOOR);
                        this.getBidQtys()[c] = MathUtil.round(lasttierlmt, tickValue, roundingType);
                        c++;
                    }

                    if(tierlmt>cqty && cqty > lasttierlmt ) {//ran out of liquidity
                        //recalculate for remaining limit
                        lasttierlmt = cqty;
                        lastvwap = vol / cqty;
                        this.getBidPrices()[c] = MathUtil.round(lastvwap, precision, BigDecimal.ROUND_FLOOR);
                        this.getBidQtys()[c] = MathUtil.round(lasttierlmt, tickValue, roundingType);
                        c++;
                    }
                    lastvwap=0.0d;//done with last tier.
                }
            }
            log.debug("loop end c="+c+", t="+t+", k="+k+", cqty="+cqty+", vol="+vol +", tierlmt="+tierlmt);
        }

        if(lastvwap != 0.0d ) {
            this.getBidPrices()[c] = MathUtil.round(lastvwap, precision, BigDecimal.ROUND_FLOOR);
            this.getBidQtys()[c] = MathUtil.round(lasttierlmt, tickValue, roundingType);
            c++;
        }

        setNumBids(c);

        if( c < maxDepth ){
            Arrays.fill(getBidPrices(), c, maxDepth-1, 0.0d);
            Arrays.fill(getBidQtys(), c, maxDepth-1, 0.0d);
        }


        //Do offers now.....
        c=0;
        t=0;
        k=0;
        lastvwap = 0.0d;
        lasttierlmt = 0.0d;
        cqty=0.0d;
        vol=0.0d;
        while(t<tiers.length && c < (maxDepth-1) && k < offerCount) {
            double tierlmt = tiers[t];
            PriceTier entry = offers[k];
            double limit = entry.qty;
            double prc = entry.prc;

            if(entry.prev!=null){//assumption is prev always has better rate then this since it was multitier and MT prices are naturally sorted.
                cqty = cqty - entry.prev.qty;
                vol = vol - entry.prev.qty * entry.prev.prc;
            }

            if (limit + cqty >= tierlmt) { //for first price it will be just qty.
                //split limit
                double diff = Math.abs(tierlmt - cqty);
                double vwap = (vol + diff * prc)/(cqty+diff);
                vwap = MathUtil.round(vwap, precision, BigDecimal.ROUND_CEILING);

                t++; //go to next tier

                //newly calculated vwap may be good for next few tiers as well
                if(lastvwap != 0.0d ){
                    if(Math.abs(lastvwap-vwap)>EPSILON) {
                        this.getOfferPrices()[c] = lastvwap;
                        this.getOfferQtys()[c] = MathUtil.round(lasttierlmt, tickValue, roundingType);
                        c++;
                        //reset vwap
                        lastvwap = vwap;
                        lasttierlmt = tierlmt;
                    }else{
                        lasttierlmt = tierlmt;
                    }
                }else{
                    lastvwap = vwap;
                    lasttierlmt = tierlmt;
                }

                if(entry.prev!=null){
                    //fix cqty before going to next
                   // log.info("put back current MT rate");
                    cqty += entry.prev.qty;
                    vol += entry.prev.qty * entry.prev.prc;
                }
            } else {
                //need more prices
                cqty += limit;
                vol += limit * prc;
                k++;//to break loop

                //special case
                if(k>=offerCount){//ran out of liquidity

                    //clear current tier
                    if(lastvwap != 0.0d ) {
                        this.getOfferPrices()[c] = MathUtil.round(lastvwap, precision, BigDecimal.ROUND_CEILING);
                        this.getOfferQtys()[c] = MathUtil.round(lasttierlmt, tickValue, roundingType);
                        c++;
                    }

                    if(tierlmt>cqty && cqty > lasttierlmt ) {//ran out of liquidity
                        //recalculate for remaining limit
                        lasttierlmt = cqty;
                        lastvwap = vol / cqty;
                        this.getOfferPrices()[c] = MathUtil.round(lastvwap, precision, BigDecimal.ROUND_CEILING);
                        this.getOfferQtys()[c] = MathUtil.round(lasttierlmt, tickValue, roundingType);
                        c++;
                    }
                    lastvwap=0.0d;//done with last tier.
                }
            }
        }


        if(lastvwap != 0.0d ) {
            this.getOfferPrices()[c] = MathUtil.round(lastvwap, precision, BigDecimal.ROUND_CEILING);
            this.getOfferQtys()[c] = MathUtil.round(lasttierlmt, tickValue, roundingType);
            c++;
        }

        setNumOffers(c);
        if( c < maxDepth ){
            Arrays.fill(getOfferPrices(), c, maxDepth-1, 0.0d);
            Arrays.fill(getOfferQtys(), c, maxDepth-1, 0.0d);
        }

        if(log.isDebugEnabled()){
            builder.append(logRate());
            log.info(builder.toString());
        }

        return PostAggregationAction.Send;
    }

    @Override
    public long isInverted() {
        if(bidCount > 0 && offerCount > 0 ){
            if(maxbid.prc > lowestoffer.prc){
                return maxbid.effectiveTime < lowestoffer.effectiveTime ? maxbid.streamidx : lowestoffer.streamidx;
            }
        }
        return 0;
    }

    public String logRate() {
        StringBuilder msg = new StringBuilder("VWAP  ");
        int numBids = getNumBids();
        msg.append(getLogKey()).append(getTimeEffective()).append('|').append(numBids);

        for (int i = 0; i < numBids; i++) {
            msg.append('|').append(this.getBidPrices()[i])
                    .append('|').append(this.getBidQtys()[i])
                    .append('|').append(this.getBidNoLP()[i]);
        }

        int numOffers = getNumOffers();
        msg.append("|").append(numOffers);

        for (int i = 0; i < numOffers; i++) {
            msg.append('|').append(this.getOfferPrices()[i])
                    .append('|').append(this.getOfferQtys()[i])
                    .append('|').append(this.getOfferNoLP()[i]);
        }

        msg.append('#');
        msg.append(this.getBookId());
        msg.append('|').append(getQuoteId());
        msg.append('|').append(getQuoteTimeEffective());
        msg.append('|').append(getOldestQId());
        msg.append("|").append(hashCode());

        return msg.toString();
    }

}
