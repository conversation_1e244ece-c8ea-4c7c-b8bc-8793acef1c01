package com.integral.mdf.data;

public class PriceElementsHolder {
    public PriceTier[] bids = new PriceTier[1000];
    public int bidCount = 0;
    public PriceTier maxbid = new PriceTier(0.0d,0.0d,-1,0,0);
    public PriceTier[] offers = new PriceTier[1000];
    public int offerCount = 0;
    public PriceTier lowestoffer = new PriceTier(Double.MAX_VALUE,0.0d,-1,0,0);
    public int bidPosition;
    public int offerPosition;
    public double cumBidQty;
    public double cumOfferQty;
    public double cumBidVol;
    public double cumOfferVol;
    public void reset() {
        bidCount = 0;
        offerCount = 0;
        maxbid.prc = 0.0d;maxbid.streamidx = 0;maxbid.effectiveTime = 0;
        lowestoffer.prc = Double.MAX_VALUE;lowestoffer.streamidx = 0;lowestoffer.effectiveTime = 0;
        bidPosition = 0;
        offerPosition = 0;
        cumBidQty = 0.0d;
        cumOfferQty = 0.0d;
        cumBidVol = 0.0d;
        cumOfferVol = 0.0d;
    }
}
