package com.integral.mdf.data;

import java.util.Arrays;

import com.integral.commons.buffers.UnSafeBuffer;

public abstract class PriceBookV2 extends PriceBook {
	
	public static int MTU = 250;

	private byte version=(byte)5;
	private int[] bidNoLP;
	private int[] offerNoLP;
	private short valueDate;
	private short flags;

	//version 5
	private long oldestQId;

	/**
	 * Synthetic tiers for aggregation
	 */
	private transient double[] tiers;
	protected double requestedSize;
	protected boolean isTermCcyAgg;
	protected long requestId;

	public PriceBookV2(int maxDepth,int bookType) {
		super(maxDepth);
		this.bidNoLP = new int[maxDepth];
		this.offerNoLP = new int[maxDepth];
		flags |= (1 << bookType);
		tiers = new double[0];
	}

	public PriceBookV2(int maxDepth,int bookType, double[] tiers) {
		this(maxDepth,bookType);

		if(tiers==null){
			throw new IllegalArgumentException("tiers can't be null.");
		}

		for(double tier:tiers){
			if(tier<=0.0d){
				throw new IllegalArgumentException("tier amount can't be zero or negetive. tiers="+Arrays.toString(tiers));
			}
		}
		Arrays.sort(tiers);
		this.tiers = tiers;
	}
	public PriceBookV2(int maxDepth, int bookType, double[] tiers, boolean isTermCcyAgg, long requestId) {
		this(maxDepth,bookType,tiers);
		this.isTermCcyAgg = isTermCcyAgg;
		this.requestId = requestId;
	}
	public PriceBookV2(int maxDepth, int bookType, double requestedSize, boolean isTermCcyAgg, long requestId) {
		this(maxDepth,bookType);
		this.requestedSize = requestedSize;
		this.isTermCcyAgg = isTermCcyAgg;
		this.requestId = requestId;
	}
	
	public byte getVersion() {
		return version;
	}

	public void ensure(int marketDepth) {
		super.ensure(marketDepth);
		if (marketDepth > getMaxDepth()) {
			this.bidNoLP = new int[getMaxDepth()];
			this.offerNoLP = new int[getMaxDepth()];
		}
	}

	/**
	 * 
	 */
	public void reset() {
		super.reset();
		Arrays.fill(this.bidNoLP, 0);
		Arrays.fill(this.offerNoLP, 0);
	}

	public void writeTo(UnSafeBuffer safeBuf) {
		//first write super's fields then append new fields
		super.writeTo(safeBuf);
		//Version 2 fields
		safeBuf.putIntArray(this.bidNoLP);
		safeBuf.putIntArray(this.offerNoLP);
		safeBuf.put((byte) getNumBids());
		safeBuf.put((byte) getNumOffers());
		//version 3
		safeBuf.putShort(valueDate);
		safeBuf.putLong(getQuoteId());
		safeBuf.putLong(getQuoteTimeEffective());
		//version 4
		safeBuf.putShort(flags);
		//version 5
		safeBuf.putLong(requestId); //correlation id
		safeBuf.putLong(getOldestQId());
	}

	public void readFrom(UnSafeBuffer safeBuf) {
		//first read super's fields then read fields for this version.
		super.readFrom(safeBuf);
		//Version 2 fields
		this.bidNoLP = safeBuf.getIntArray();
		this.offerNoLP = safeBuf.getIntArray();
		setNumBids( safeBuf.get() );
		setNumOffers( safeBuf.get() );
		this.valueDate = safeBuf.getShort();
		setQuoteId(safeBuf.getLong());
		setQuoteTimeEffective(safeBuf.getLong());
		this.flags = safeBuf.getShort();
		if(getVersion()>=5) this.requestId = safeBuf.getLong();
		if(getVersion()>=5) this.oldestQId = safeBuf.getLong();
	}

	@Override
	public String toString() {
		return "PriceBook [version=" + version + ", fiIndex=" + getFIIndex() + ", ccyPairIndex=" + getCcyPairIndex() + ", bookId="
				+ getBookId() + ", bidPrices=" + Arrays.toString(getBidPrices()) + ", bidQtys=" + Arrays.toString(getBidQtys())
				+ ", bidNoLPs" + Arrays.toString(getBidNoLP())
				+ ", offerPrices=" + Arrays.toString(getOfferPrices()) + ", offerQtys=" + Arrays.toString(getOfferQtys())
				+ ", offerNoLPs" + Arrays.toString(getOfferNoLP())
				+ ", timeEffective=" + getTimeEffective() + ", numBids=" + getNumBids() + ", numOffers=" + getNumOffers()
				+ ", maxDepth=" + getMaxDepth() 
				+ ", valueDate=" + getValueDate()
				+ ", qid=" + getQuoteId() 
				+", qfime=" + getQuoteTimeEffective() 
				+", flags=" + getFlags()
				+", oQId=" + getOldestQId()
				+"]";
	}

	public int[] getOfferNoLP() {
		return offerNoLP;
	}
	
	public int[] getBidNoLP() {
		return bidNoLP;
	}
	
	public short getValueDate(){
		return valueDate;
	}

	public void setValueDate(short value){
		this.valueDate = value;
	}
	
	public boolean isRawBook(){
		return ( flags & ( 1 << FLAG_RAW_BOOK) ) > 0;
	}
	
	public boolean isFullBook(){
		return ( flags & ( 1 << FLAG_FULL_BOOK) ) > 0;
	}
	public boolean isMTFABook(){
		return ( flags & ( 1 << FLAG_MT_BOOK_FA) ) > 0;
	}
	
	public boolean addTier(double tier){
		return true;
	}

	public double[] getTiers(){
		return tiers;
	}

	public boolean removeTier(double tier){
		return true;
	}

	public short getFlags() {
		return flags;
	}
	@Override
	public void setFlag(int flag){
		this.flags |= (1 << flag);
	}
	@Override
	public boolean isCustomAggregation(){
		return ( flags & ( 1 << FLAG_CUSTOM_AGG) ) > 0;
	}

	public long getOldestQId() {
		return oldestQId;
	}

	public void setOldestQId(long id){
		this.oldestQId = id;
	}

	public void logRate(StringBuilder msg) {
		msg.append(getName()).append("|");
		int numBids = getNumBids();
		msg.append(getLogKey()).append(getTimeEffective()).append('|').append(numBids);

		for (int i = 0; i < numBids; i++) {
			msg.append('|').append(this.getBidPrices()[i])
					.append('|').append(this.getBidQtys()[i])
					.append('|').append(this.getBidNoLP()[i]);
		}

		int numOffers = getNumOffers();
		msg.append("|").append(numOffers);

		for (int i = 0; i < numOffers; i++) {
			msg.append('|').append(this.getOfferPrices()[i])
					.append('|').append(this.getOfferQtys()[i])
					.append('|').append(this.getOfferNoLP()[i]);
		}

		msg.append('#');
		msg.append(this.getBookId());
		msg.append('|').append(getQuoteId());
		msg.append('|').append(getQuoteTimeEffective());
		msg.append('|').append(getOldestQId());
		msg.append("|").append(hashCode());
	}
	protected String getName(){
		return "UNKNOWN BOOK";
	}
}
