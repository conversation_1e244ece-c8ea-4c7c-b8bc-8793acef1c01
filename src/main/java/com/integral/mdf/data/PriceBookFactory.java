package com.integral.mdf.data;

import com.integral.provision.MDFAggregationType;

public class PriceBookFactory {

    public static PriceBook create(MDFAggregationType type, int precision, int depth, double[] aggregationTiers,
                                   boolean isModifiable, double requestedSize, boolean isTermCcyAgg, long requestId) {
        switch (type) {
            case FULL_BOOK:
                return new FullBookPriceBookV2(depth, requestedSize, isTermCcyAgg, requestId);
            case RAW_BOOK:
                return new ArrayRawPriceBook(depth, isTermCcyAgg, requestId);
            case BEST_PRICE:
                return new BestPriceBook(isTermCcyAgg, requestId);
            case MULTI_TIER_FOK:
                return isModifiable ? new ModifiableMTFOKBook(depth, isTermCcyAgg, requestId) : new MultiTierFOKBook(depth, aggregationTiers, isTermCcyAgg, requestId);
            case WEIGHTED_AVERAGE:
                return isModifiable ? new ModifiableVWAPBook(depth, isTermCcyAgg, requestId) : new VWAPBook(depth,aggregationTiers, isTermCcyAgg, requestId);
            case FA_MULTI_TIER:
                return isModifiable ? new ModifiableMTFABook(depth, isTermCcyAgg, requestId) : new MTFABook(depth, aggregationTiers, isTermCcyAgg, requestId);
            case RAW_DIRECT:
                return new RawDirectBook(depth, requestId);
            default:
                return new FullBookPriceBookV2(depth, requestedSize, isTermCcyAgg, requestId);
        }
    }

}
