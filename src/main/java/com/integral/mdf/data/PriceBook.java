package com.integral.mdf.data;

import java.util.Arrays;

import com.integral.commons.buffers.UnSafeBuffer;

/**
 * Flyweight object contains serialization scheme of price book.
 * <AUTHOR>
 *
 */
public abstract class PriceBook {
	
	public static int MTU = 250;
	public final static int FLAG_RAW_BOOK = 1; 
	public final static int FLAG_FULL_BOOK = 2;
	public final static int FLAG_TOB_BOOK = 3;
	public final static int FLAG_MTFOK_BOOK = 4;
	public final static int FLAG_VWAP_BOOK = 5;
	public final static int FLAG_CUSTOM_AGG = 6;
	//public final static int FLAG_MTFOK_BOOK_FA = 7;
	public final static int FLAG_MT_BOOK_FA = 8;
	//public final static int FLAG_SYN_MQ_BOOK_FA = 9;
	public final static int FLAG_RAW_DIRECT_BOOK = 10;
	public static final int TIER_SIZE = 4 * Double.BYTES + 2 * Integer.BYTES;
	public static final int FIXED_SIZE = Byte.BYTES + 2 * Short.BYTES + 5 * Integer.BYTES + 4 * Long.BYTES + 100;

	private byte version=(byte)1;
	private int fiIndex;
	private int ccyPairIndex;
	private long bookId;
	private double[] bidPrices;
	private double[] bidQtys;

	private double[] offerPrices;
	private double[] offerQtys;

	private long timeEffective;
	private int numBids;
	private int numOffers;
	private int maxDepth;
	
	private String logKey;

	private long quoteId;

	private long quoteTimeEffective;
	private long timeEffectiveNano;

	public PriceBook(int maxDepth) {
		this.maxDepth = maxDepth;
		this.bidPrices = new double[maxDepth];
		this.bidQtys = new double[maxDepth];
		this.offerPrices = new double[maxDepth];
		this.offerQtys = new double[maxDepth];
	}

	public void ensure(int marketDepth) {
		if (marketDepth > getMaxDepth()) {
			this.maxDepth = marketDepth;
			this.bidPrices = new double[getMaxDepth()];
			this.bidQtys = new double[getMaxDepth()];
			this.offerPrices = new double[getMaxDepth()];
			this.offerQtys = new double[getMaxDepth()];
		}
	}

	/**
	 * 
	 */
	public void reset() {
		Arrays.fill(this.bidPrices, 0);
		Arrays.fill(this.bidQtys, 0);
		Arrays.fill(this.offerPrices, 0);
		Arrays.fill(this.offerQtys, 0);
	}

	public int getNumBids() {
		return numBids;
	}

	public void setNumBids(int numBids) {
		this.numBids = numBids;
	}

	public int getNumOffers() {
		return numOffers;
	}

	public void setNumOffers(int numOffers) {
		this.numOffers = numOffers;
	}

	public int getFIIndex() {
		return fiIndex;
	}

	public void setFIIndex(int fiIndex) {
		this.fiIndex = fiIndex;
	}

	public int getCcyPairIndex() {
		return ccyPairIndex;
	}

	public void setCcyPairIndex(int ccyPairIndex) {
		this.ccyPairIndex = ccyPairIndex;
	}

	public long getBookId() {
		return bookId;
	}

	public void setBookId(long quoteId) {
		this.bookId = quoteId;
	}

	public double[] getBidPrices() {
		return bidPrices;
	}

	public void setBidPrices(double[] bidPrices) {
		this.bidPrices = bidPrices;
	}

	public double[] getBidQtys() {
		return bidQtys;
	}

	public void setBidQtys(double[] bidQtys) {
		this.bidQtys = bidQtys;
	}

	public double[] getOfferPrices() {
		return offerPrices;
	}

	public void setOfferPrices(double[] offerPrices) {
		this.offerPrices = offerPrices;
	}

	public double[] getOfferQtys() {
		return offerQtys;
	}

	public void setOfferQtys(double[] offerQtys) {
		this.offerQtys = offerQtys;
	}

	public long getTimeEffective() {
		return timeEffective;
	}

	public void setTimeEffective(long millisTime, long nanoTime) {
		this.timeEffective = millisTime;
		this.timeEffectiveNano = nanoTime;
	}

	public static int getEstimatedSize(int tiers) {
		return tiers * TIER_SIZE + FIXED_SIZE;
	}
	
	public void writeTo(UnSafeBuffer safeBuf) {
		safeBuf.put((byte)getVersion());
		safeBuf.putInt(this.getFIIndex());
		safeBuf.putInt(this.getCcyPairIndex());
		safeBuf.putLong(this.getBookId());
		safeBuf.putDoubleArray(this.getBidPrices());
		safeBuf.putDoubleArray(this.getBidQtys());
		safeBuf.putDoubleArray(this.getOfferPrices());
		safeBuf.putDoubleArray(this.getOfferQtys());
		safeBuf.putLong(getTimeEffective());
	}

	public void readFrom(UnSafeBuffer safeBuf) {
		this.version = safeBuf.get();
		this.fiIndex = safeBuf.getInt();
		this.ccyPairIndex = safeBuf.getInt();
		this.bookId = safeBuf.getLong();
		
		this.bidPrices = safeBuf.getDoubleArray();
		this.bidQtys = safeBuf.getDoubleArray();
		this.offerPrices = safeBuf.getDoubleArray();
		this.offerQtys = safeBuf.getDoubleArray();
		
		this.timeEffective = safeBuf.getLong();
	}

	@Override
	public String toString() {
		return "PriceBook [version=" + version + ", fiIndex=" + fiIndex + ", ccyPairIndex=" + ccyPairIndex + ", bookId="
				+ bookId + ", bidPrices=" + Arrays.toString(bidPrices) + ", bidQtys=" + Arrays.toString(bidQtys)
				+ ", offerPrices=" + Arrays.toString(offerPrices) + ", offerQtys=" + Arrays.toString(offerQtys)
				+ ", timeEffective=" + timeEffective + ", numBids=" + numBids + ", numOffers=" + numOffers
				+ ", maxDepth=" + getMaxDepth() +  ", qid=" + getQuoteId() +", qfime=" + getQuoteTimeEffective()
				+ ", oqid=" + getOldestQId()
				+ "]";
	}

	public byte getVersion() {
		return version;
	}

	public int getMaxDepth() {
		return maxDepth;
	}
	
	/**
	 * Returns true if after adding all the required prices from 'ProvisionedQuote' book becomes 
	 * inverted.
	 * @param pq
	 * @return true if book is inverted post operation.
	 */
	public abstract boolean addPrices(final ProvisionedQuoteC pq);

	public abstract PostAggregationAction copyPrices(int precision,int roundingType , double tickValue);
	
	public String getLogKey() {
		return logKey;
	}

	public void setLogKey(String logKey) {
		this.logKey = logKey;
	}

	public void setQuoteId(long latestQId) {
		this.quoteId = latestQId;
	}
	
	public long getQuoteId(){
		return this.quoteId;
	}

	public void setQuoteTimeEffective(long latestQRecvtime) {
		this.quoteTimeEffective = latestQRecvtime;
	}
	
	public long getQuoteTimeEffective(){
		return this.quoteTimeEffective;
	}

	/**
	 * Returns true if tier is added successfully, if tier is already present then returns false
	 * @param tier
	 * @return
	 */
	public abstract boolean addTier(double tier);

	public abstract double[] getTiers();

	/**
	 * Returns true if the tier is removed successfully, is tier is not present then returns false
	 * @param tier
	 * @return
	 */
	public abstract boolean removeTier(double tier);

	/**
	 * Checks and returns streamId of older quote between comparison.
	 * @return 0 - if book is not inverted else return streamIdx of older quote.
	 */
	public abstract long isInverted();

	public abstract void setOldestQId(long id);

	public abstract long getOldestQId();

	public long getTimeEffectiveNano(){
		return this.timeEffectiveNano;
	}

	public void setTimeEffectiveNano(long value){
		this.timeEffectiveNano = value;
	}
	public abstract void setFlag(int flag);
	public abstract boolean isCustomAggregation();
}
