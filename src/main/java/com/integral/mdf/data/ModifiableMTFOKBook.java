package com.integral.mdf.data;

import java.util.Arrays;
import java.util.concurrent.atomic.AtomicReference;

public class ModifiableMT<PERSON><PERSON><PERSON>ook extends MultiTierFOKBook {

    final AtomicReference<double[]> tiers = new AtomicReference<>(new double[0]);

    public ModifiableMTFOKBook(int maxDepth, boolean isTermCcyAgg, long requestId) {
        super(maxDepth, new double[0], isTermCcyAgg, requestId);
    }

    /**
     * Not a thread safe method. Might add same tier twice.
     * @param tier
     * @return
     */
    public boolean addTier(double tier){

        if(tier<=0.0d){
            return false;
        }

        for(double otier:tiers.get()){
            if(Math.abs(otier-tier) < 0.001){
                return false;
            }
        }

        double[] newTiers = new double[tiers.get().length+1];
        System.arraycopy(tiers.get(),0,newTiers,0,tiers.get().length);
        newTiers[newTiers.length-1] = tier;
        Arrays.sort(newTiers);
        tiers.set(newTiers);
        log.info("added tier - book="+getLogKey()+" tier="+tier +", current tiers="+Arrays.toString(tiers.get()));
        return true;
    }

    public boolean removeTier(double tier){
        double[] cTiers = tiers.get();
        int i=0;
        for(;i< cTiers.length;i++){
            double otier = cTiers[i];
            if(Math.abs(otier-tier) < 0.001){
                break;
            }
        }
        if(i<cTiers.length){
            double[] newTiers = new double[tiers.get().length-1];

            //remove upto i
            if(i>0)
                System.arraycopy(cTiers,0,newTiers,0,i);

            if(i<cTiers.length-1)
                System.arraycopy(cTiers,i+1,newTiers,i,newTiers.length-i);
            Arrays.sort(newTiers);
            tiers.set(newTiers);
            log.info("removed tier - book="+getLogKey()+" tier="+ tier +", current tiers="+Arrays.toString(tiers.get()));
            return true;
        }else{
            return false;
        }
    }

    public double[] getTiers(){
        return tiers.get();
    }
}
