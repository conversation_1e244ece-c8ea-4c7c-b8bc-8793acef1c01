package com.integral.mdf.data;

import org.agrona.concurrent.UnsafeBuffer;

import java.time.Instant;

public class QuoteC {

	public static final  int FULL_AMOUNT_FLAG =1;
	private static final int SIZE_OF_BYTE = 1;
	private static final int SIZE_OF_INT = 4;
	private static final int SIZE_OF_LONG = 8;
	private static final int SIZE_OF_SHORT = 2;
	private static final int SIZE_OF_DOUBLE = 8;
	public static final byte BUY = 0;
	public static final byte SELL = 1;
	public static final byte MULTI_QUOTE = 0;
	public static final byte MULTI_TIER = 1;
	public static final int MAX_DEPTH = 5;

	private static final int MSG_TYPE = 0;
	private static final int VERSION = SIZE_OF_BYTE;
	private static final int VENUE_IDX = VERSION + SIZE_OF_BYTE;
	private static final int STREAM_IDX = VENUE_IDX + SIZE_OF_INT;
	private static final int CCY_PAIR_IDX = STREAM_IDX + SIZE_OF_INT;
	private static final int QUOTE_ID = CCY_PAIR_IDX + SIZE_OF_INT;
	private static final int SESSION_ID = QUOTE_ID + SIZE_OF_LONG; 
	private static final int PROVIDER_IDX = SESSION_ID + SIZE_OF_INT;
	private static final int QUOTE_TYPE = PROVIDER_IDX + SIZE_OF_INT;
	private static final int QUOTE_MIN_QTY = QUOTE_TYPE + SIZE_OF_BYTE;
	private static final int QUOTE_CATEGORY = QUOTE_MIN_QTY + SIZE_OF_DOUBLE;
	private static final int VALUE_DATE = QUOTE_CATEGORY + SIZE_OF_INT;
	private static final int LEGAL_ENTITYID = VALUE_DATE + SIZE_OF_SHORT;
	private static final int BID_TIERS_NUM = LEGAL_ENTITYID + SIZE_OF_INT;
	private static final int OFFER_TIERS_NUM = BID_TIERS_NUM + SIZE_OF_INT;
	private static final int BID_TIERS = OFFER_TIERS_NUM + SIZE_OF_INT; //length of fixed structure

	private static final int TIER_PRICE_OFFSET = 0;
	private static final int TIER_SHOW_QTY_OFFSET = TIER_PRICE_OFFSET
			+ SIZE_OF_DOUBLE;
	private static final int TIER_TOTAL_QTY_OFFSET = TIER_SHOW_QTY_OFFSET
			+ SIZE_OF_DOUBLE;
	private static final int TIER_CNT_OFFSET = TIER_TOTAL_QTY_OFFSET
			+ SIZE_OF_DOUBLE;
	private static final int TIER_SIZE = TIER_CNT_OFFSET + SIZE_OF_INT;

	private static int QUOTE_CREATED_TIME = getQuoteCreatedTimeOffset(7) ;
	private static final int FLAGS = QUOTE_CREATED_TIME + SIZE_OF_LONG;
	private static int LENGTH = FLAGS + SIZE_OF_INT;

	private final UnsafeBuffer buffer;
	
	public static final int MAX_LENGTH = 512;

	private int index;
	
	private Instant receivedTime;
	
	public QuoteC() {
		this.buffer = new UnsafeBuffer(new byte[MAX_LENGTH]);
		//this.buffer.putByte(MSG_TYPE,MsgType.QUOTE);
		this.buffer.putByte(MSG_TYPE,(byte)24);
		this.buffer.putDouble(QUOTE_MIN_QTY, 0.0);
	}

//	public static int size(final int depth) {
//		return BID_TIERS + (TIER_SIZE * depth * 2) + ;
//	}
	
	public int getVersion() {
		return this.buffer.getByte(VERSION);
	}

	public void setVersion(final byte version) {
		this.buffer.putByte(VERSION, version);
	}
	
	public int getMessageType() {
		return this.buffer.getByte(MSG_TYPE);
	}

	public int getVenueIdx() {
		return this.buffer.getInt(VENUE_IDX);
	}

	public void setVenueIdx(final int venueIdx) {
		this.buffer.putInt(VENUE_IDX, venueIdx);
	}
	
	public short getValueDate() {
		return this.buffer.getShort(VALUE_DATE);
	}

	public void setValueDate(final short valueDate) {
		this.buffer.putShort(VALUE_DATE, valueDate);
	}
	
	public int getProviderIdx() {
		return this.buffer.getInt(PROVIDER_IDX);
	}

	public void setProviderIdx(final int providerIdx) {
		this.buffer.putInt(PROVIDER_IDX, providerIdx);
	}
	
	public int getLegalEntityID() {
		return this.buffer.getInt(LEGAL_ENTITYID);
	}

	public void setLegalEntityID(final int legalEntityID) {
		this.buffer.putInt(LEGAL_ENTITYID, legalEntityID);
	}

	public void setStreamIdx(final int streamIdx) {
		this.buffer.putInt(STREAM_IDX, streamIdx);
	}

	public int getStreamIdx() {
		return this.buffer.getInt(STREAM_IDX);
	}

	public byte getQuoteType() {
		return this.buffer.getByte(QUOTE_TYPE);
	}

	public void setQuoteType(byte aType) {
		this.buffer.putByte(QUOTE_TYPE, aType);
	}

	public int getCategory() {
		return this.buffer.getInt(QUOTE_CATEGORY);
	}

	public void setCategory(final int category) {
		this.buffer.putInt(QUOTE_CATEGORY, category);
	}

	public int getCcyPairIdx() {
		return this.buffer.getInt(CCY_PAIR_IDX);
	}

	public void setCcyPairIdx(final int ccyPairIdx) {
		this.buffer.putInt(CCY_PAIR_IDX, ccyPairIdx);
	}

	public long getQuoteId() {
		return this.buffer.getLong(QUOTE_ID);
	}

	public void setQuoteId(final long quoteId) {
		this.buffer.putLong(QUOTE_ID, quoteId);
	}
	
	public int getSessionId() {
		return this.buffer.getInt(SESSION_ID);
	}

	public void setSessionId(final int sessionId) {
		this.buffer.putInt(SESSION_ID, sessionId);
	}

	public final int bidTiersOffset() {
		return BID_TIERS;
	}

	public final int offerTiersOffset() {
		return bidTiersOffset() + getBidTiersNum() * TIER_SIZE;
	}

	public void setBidTiersNum(int bidNum) {
		this.buffer.putInt(BID_TIERS_NUM, bidNum);
	}

	public void setOfferTiersNum(int offerNum) {
		this.buffer.putInt(OFFER_TIERS_NUM, offerNum);
	}

	public int getBidTiersNum() {
		return this.buffer.getInt(BID_TIERS_NUM);
	}

	public int getOfferTiersNum() {
		return this.buffer.getInt(OFFER_TIERS_NUM);
	}

	public int tierOffset(final byte side, final int tierNo) {
		return (side == BUY ? bidTiersOffset() : offerTiersOffset()) + tierNo
				* TIER_SIZE;
	}

	public double getPrice(final int tierOffset) {
		return this.buffer.getDouble(tierOffset + TIER_PRICE_OFFSET);
	}

	public void setPrice(final int tierOffset, final double price) {
		this.buffer.putDouble(tierOffset + TIER_PRICE_OFFSET, price);
	}

	public void setShowQty(final int tierOffset, final double showQty) {
		this.buffer.putDouble(tierOffset + TIER_SHOW_QTY_OFFSET, showQty);
	}

	public double getShowQty(final int tierOffset) {
		return this.buffer.getDouble(tierOffset + TIER_SHOW_QTY_OFFSET);
	}

	public void setTotalQty(final int tierOffset, final double totalQty) {
		this.buffer.putDouble(tierOffset + TIER_TOTAL_QTY_OFFSET, totalQty);
	}

	public double getTotalQty(final int tierOffset) {
		return this.buffer.getDouble(tierOffset + TIER_TOTAL_QTY_OFFSET);
	}

	public void setCnt(final int tierOffset, final int cnt) {
		this.buffer.putInt(tierOffset + TIER_CNT_OFFSET, cnt);
	}

	public int getCnt(final int tierOffset) {
		return this.buffer.getInt(tierOffset + TIER_CNT_OFFSET);
	}

	public int index() {
		return index;
	}

	public void index(int index) {
		this.index = index;
	}

	public UnsafeBuffer buffer() {
		return buffer;
	}
	
	@Override
	public String toString() {

		int bidNum = getBidTiersNum();
		int offerNum = getOfferTiersNum();

		String marketData = "QuoteC [getVersion()=" + getVersion() + ", getMessageType()="
				+ getMessageType()+", receivedTime=" + receivedTime 
				+ ", getVenueIdx()=" + getVenueIdx()
				+ ", getProviderIdx()=" + getProviderIdx()
				+ ", getStreamIdx()=" + getStreamIdx() + ", getQuoteType()="
				+ getQuoteType() + ", getCategory()=" + getCategory()
				+ ", getCcyPairIdx()=" + getCcyPairIdx() + ", getQuoteId()="
				+ getQuoteId() + ", getSessionId()=" + getSessionId() +",valuedate=" + getValueDate()
				+ ", bidTiersOffset()=" + bidTiersOffset()
				+ ", offerTiersOffset()=" + offerTiersOffset()
				+ ", getBidTiersNum()=" + getBidTiersNum()
				+ ", getOfferTiersNum()=" + getOfferTiersNum()
                + ", QuoteCreatedTime=" + getQuoteCreatedTime()
				+ ", isFAStream=" + isFullAmountStream()
                + "]";

		StringBuilder data = new StringBuilder(100);
		data.append(marketData);
		data.append("[ bids=");
		for (int i = 0; i < bidNum; i++) {
			int tierOffset = tierOffset(BUY, i);
			data.append("{").append("price=").append(getPrice(tierOffset))
					.append(",").append("totalQty=")
					.append(getTotalQty(tierOffset)).append(",")
					.append("showQty=").append(getShowQty(tierOffset))
					.append(",").append("cnt=").append(getCnt(tierOffset))
					.append("}");
		}
		data.append("]");
		data.append("[ offers=");
		for (int i = 0; i < offerNum; i++) {
			int tierOffset = tierOffset(SELL, i);
			data.append("{").append("price=").append(getPrice(tierOffset))
					.append(",").append("totalQty=")
					.append(getTotalQty(tierOffset)).append(",")
					.append("showQty=").append(getShowQty(tierOffset))
					.append(",").append("cnt=").append(getCnt(tierOffset))
					.append("}");
		}
		data.append("]");
		return data.toString();
	}

//	public int getEstimatedSize() {
//		return BID_TIERS + (TIER_SIZE * getBidTiersNum() + TIER_SIZE * getOfferTiersNum());
//	}

	public void readFrom(UnsafeBuffer incoming) {
		incoming.getBytes(0, buffer.byteBuffer(), incoming.capacity());
	}
	
	public void readFromArray(UnsafeBuffer incoming) {
		incoming.getBytes(0, buffer.byteArray(), 0, incoming.capacity());
	}
	
	public void readFrom(QuoteC rate) {
		readFromArray(rate.buffer());
		this.receivedTime = rate.getReceivedTime();
	}

	public void writeTo(UnsafeBuffer outgoing) {
		buffer.getBytes(0, outgoing.byteBuffer(), buffer.capacity());
	}

	public void stampReceivedTime() {
		this.receivedTime = Instant.now();
	}
	
	public Instant getReceivedTime() {
		return this.receivedTime;
	}

	public static int getQuoteCreatedTimeOffset(final int depth) {
		return BID_TIERS + (TIER_SIZE * depth * 2);
	}

	public int getQCTOffset(){
	    return QUOTE_CREATED_TIME;
    }

	public long getQuoteCreatedTime() {
		return this.buffer().getLong(QUOTE_CREATED_TIME);
	}

	//for testing
	public void setQuoteCreatedTime(long quoteCreatedTime) {
		this.buffer().putLong(QUOTE_CREATED_TIME, quoteCreatedTime);
	}

	public void setFullAmountStream(boolean isFullAmountStream){
		if(isFullAmountStream){
			enableFlag(FULL_AMOUNT_FLAG);
		}else{
			disableFlag(FULL_AMOUNT_FLAG);
		}
	}

	public boolean isFullAmountStream(){
		return getVersion() >= 2 && isFlagEnabled(FULL_AMOUNT_FLAG);
	}
	private void enableFlag( int flag ) {
		this.buffer().putInt(FLAGS,this.buffer().getInt(FLAGS)  | flag);
	}

	private void disableFlag( int flag ) {
		this.buffer().putInt(FLAGS,this.buffer().getInt(FLAGS)  & ~flag);
	}

	private boolean isFlagEnabled( int flag ) {
		return ( this.buffer().getInt(FLAGS)  & flag ) == flag;
	}
	private int getFlagValue() {
		return  this.buffer().getInt(FLAGS)  ;
	}
}