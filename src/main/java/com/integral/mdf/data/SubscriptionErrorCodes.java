package com.integral.mdf.data;

public enum SubscriptionErrorCodes {

        DuplicateRequestId(1001,"Request with given id already exist"),
        DuplicateRequestParameters(1002,"Request with given parameters already exist"),
        UnsupportedOrganization(1003,"Organization is not configured for mdf cluster"),
        InvalidRequestId(1004,"ID is required for this subscription request"),
        OrganizationRequired(1005,"Organization is required for this subscription request"),
        CurrencyPairRequired(1006,"CurrencyPair is required for this subscription request"),
        AggregationTypeRequired(1007,"Aggregation type is required for this subscription request"),
        InvalidCurrencyPair(1008,"CurrencyPair is not valid or supported by this cluster"),
        TierValuesRequired(1009,"Tier values are required for this subscription request"),
        ;

        private int code;
        private String description;

        private SubscriptionErrorCodes(int code,String description){
            this.description = description;
            this.code = code;
        }

        public String getDescription() {
            return description;
        }

        public int getCode(){
            return code;
        }

}
