package com.integral.mdf.data;

import java.util.Arrays;

import com.integral.commons.pool.ObjectPool;
import com.integral.commons.pool.PoolFactory;

public class QuotePoolFactory implements PoolFactory<QuoteC> {

	@Override
	public QuoteC newUnPooledInstance() {
		return new QuoteC();
	}

	@Override
	public QuoteC newPooledInstance(ObjectPool<QuoteC> pool) {
		//both are same as of now.
		return newUnPooledInstance();
	}

	@Override
	public void recycle(QuoteC pooled) {
		Arrays.fill(pooled.buffer().byteArray(), (byte)0);
	}

}
