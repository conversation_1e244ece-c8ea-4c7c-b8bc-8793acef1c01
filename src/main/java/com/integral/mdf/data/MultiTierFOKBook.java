package com.integral.mdf.data;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.util.MathUtil;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Comparator;

/**
 * Created by nagarajap on 8/2/2018.
 */
public class MultiTierFOKBook extends PriceBookV2 {

    PriceTier[] bids = new PriceTier[1000];
    int bidCount = 0;
    PriceTier maxbid = new PriceTier(0.0d,0.0d,-1,0,0);
    PriceTier[] offers = new PriceTier[1000];
    int offerCount = 0;
    PriceTier lowestoffer = new PriceTier(Double.MAX_VALUE,0.0d,-1,0,0);

    static int MAX_TIER = 50;
    Log log = LogFactory.getLog(this.getClass());

    private StringBuilder builder = new StringBuilder();

    public MultiTierFOKBook(int maxDepth, double[] tiers, boolean isTermCcyAgg, long requestId) {
        super(maxDepth,FLAG_MTFOK_BOOK,tiers, isTermCcyAgg, requestId);
        if( maxDepth > MAX_TIER ){
            throw new IllegalArgumentException("Invalid maxDepth. It can't be more then " + MAX_TIER);
        }
    }

    /**
     *
     */
    public void reset() {
        bidCount = 0;
        offerCount = 0;
        maxbid.prc = 0.0d;maxbid.streamidx = 0;maxbid.effectiveTime = 0;
        lowestoffer.prc = Double.MAX_VALUE;lowestoffer.streamidx = 0;lowestoffer.effectiveTime = 0;
        if(log.isDebugEnabled()){
            builder.setLength(0);
        }
    }

    /**
     * @return isInverted
     */
    public boolean addPrices(final ProvisionedQuoteC pq) {
        int tier = 0;
        while(tier < pq.getPBidTiersNum() ){
            int offset = pq.pTierOffset(ProvisionedQuoteC.BUY, tier);
            double prc = pq.getPPrice(offset), d=0.0d;
            if( (d= pq.getPShowQty(offset, isTermCcyAgg) ) > 0.0d ) {
                bids[bidCount++] = new PriceTier(prc, pq.getPShowQty(offset, isTermCcyAgg), pq.getLPIndex(),
                        pq.getStreamIdx(),pq.getReceivedTime().toEpochMilli());
                if (prc > maxbid.prc) {
                    maxbid.prc = prc;
                    maxbid.streamidx = pq.getStreamIdx();
                    maxbid.effectiveTime = pq.getReceivedTime().toEpochMilli();
                }
            }
            tier++;
        }
        tier = 0;
        while(tier < pq.getPOfferTiersNum() ){
            int offset = pq.pTierOffset(ProvisionedQuoteC.SELL, tier);
            double prc = pq.getPPrice(offset), d=0.0d;
            if( (d= pq.getPShowQty(offset, isTermCcyAgg) ) > 0.0d ) {
                offers[offerCount++] = new PriceTier(prc, pq.getPShowQty(offset, isTermCcyAgg), pq.getLPIndex(),
                        pq.getStreamIdx(),pq.getReceivedTime().toEpochMilli());
                if (prc < lowestoffer.prc) { //better offer
                    lowestoffer.prc = prc;
                    lowestoffer.streamidx = pq.getStreamIdx();
                    lowestoffer.effectiveTime = pq.getReceivedTime().toEpochMilli();
                }
            }
            tier++;
        }

//        if(bidCount > 0 && offerCount > 0 ){
//            if(maxbid > lowestoffer ){
//                return true;
//            }
//        }

        //we added some prices successfully. send latest
        if( getValueDate() < pq.getValueDate()){
            setValueDate(pq.getValueDate());
        }

        if(log.isDebugEnabled()){
            builder.append(pq.getProvisionedQuoteString());
            builder.append(" ");
        }

        return false;
    }


    public PostAggregationAction copyPrices(int precision,int roundingType , double tickValue) {

        if(bidCount <= 0 && offerCount <= 0 ){
            //send zero price book. MDG treat it is as
            setNumBids(1);
            this.getBidPrices()[0]=0.0d;
            this.getBidQtys()[0]=0.0d;
            this.getBidNoLP()[0]=0;

            setNumOffers(1);
            this.getOfferPrices()[0]=0.0d;
            this.getOfferQtys()[0]=0.0d;
            this.getOfferNoLP()[0]=0;

            if(log.isDebugEnabled()){
                builder.append(logRate());
                log.info(builder.toString());
            }

            return PostAggregationAction.Send;
        }

        Arrays.sort(bids,0,bidCount, (o1,o2) -> {
            int a = Double.compare(o2.prc, o1.prc);
            return a == 0 ? Double.compare(o2.qty,o1.qty) : a;
        });

        //sortbids and sortoffers
//        Arrays.sort(bids,0,bidCount, new Comparator<PriceTier>() {
//            @Override
//            public int compare(PriceTier o1, PriceTier o2) {
//                int a = Double.compare(o2.prc, o1.prc);
//                return a == 0 ? Double.compare(o2.qty,o1.qty) : a;
//            }
//        });

        Arrays.sort(offers,0,offerCount,(o1,o2)->{
            int a = Double.compare(o1.prc, o2.prc);
            return a == 0 ? Double.compare(o2.qty,o1.qty) : a;
        });

//        Arrays.sort(offers,0,offerCount, new Comparator<PriceTier>() {
//            @Override
//            public int compare(PriceTier o1, PriceTier o2) {
//                int a = Double.compare(o1.prc, o2.prc);
//                return a == 0 ? Double.compare(o2.qty,o1.qty) : a;
//            }
//        });


        //Bids

//
//        for (double tierlmt : tiers){
//            for(int i =0; i<bidCount && c < maxDepth; i++){
//                PriceTier entry = bids[i];
//                if(entry.qty >= tierlmt){
//                    this.getBidPrices()[c] = MathUtil.round(entry.prc,precision, BigDecimal.ROUND_FLOOR);
//                    this.getBidQtys()[c] = MathUtil.round(tierlmt,tickValue,roundingType);
//                    this.getBidNoLP()[c] = entry.lpidx;
//                    c++;
//                    break;
//                }
//            }
//        }

        int maxDepth = getMaxDepth();

        int c=0;
        int t=0;
        int k=0;
        PriceTier lastentry = null;
        double lasttierlmt = 0.0d;
        final double[] tiers = getTiers();
        while(t<tiers.length && c < maxDepth && k < bidCount){
            double tierlmt = tiers[t];
            PriceTier entry = bids[k];
            if(entry.qty >= tierlmt){
                t++; //goto next tierlmt
                lasttierlmt = tierlmt;
                lastentry = entry;
            }else{
                if(lastentry!=null) {
                    if (entry.prc == lastentry.prc && entry.qty > lastentry.qty) {
                        k++; //goto next
                        lastentry = entry;
                    } else {
                        this.getBidPrices()[c] = MathUtil.round(lastentry.prc, precision, BigDecimal.ROUND_FLOOR);
                        this.getBidQtys()[c] = MathUtil.round(lasttierlmt, tickValue, roundingType);
                        this.getBidNoLP()[c] = lastentry.lpidx;
                        //reset temps
                        lastentry = null;
                        lasttierlmt = 0.0d;
                        c++;
                    }
                }
                k++;
            }
        }

        if(lastentry!=null) {
            this.getBidPrices()[c] = MathUtil.round(lastentry.prc, precision, BigDecimal.ROUND_FLOOR);
            this.getBidQtys()[c] = MathUtil.round(lasttierlmt, tickValue, roundingType);
            this.getBidNoLP()[c] = lastentry.lpidx;
            c++;
        }

        setNumBids(c);

        if( c < maxDepth ){
            Arrays.fill(getBidPrices(), c, maxDepth-1, 0.0d);
            Arrays.fill(getBidQtys(), c, maxDepth-1, 0.0d);
        }


        //Offers ---------------
//        c=0;
//        for (double tierlmt : tiers){
//            for(int i =0; i<offerCount && c < maxDepth; i++){
//                PriceTier entry = offers[i];
//                if(entry.qty >= tierlmt){
//                    this.getOfferPrices()[c] = MathUtil.round(entry.prc,precision,BigDecimal.ROUND_CEILING);
//                    this.getOfferQtys()[c] = MathUtil.round(tierlmt,tickValue,roundingType);
//                    this.getOfferNoLP()[c] = entry.lpidx;
//                    c++;
//                    break;
//                }
//            }
//        }

        c=0;
        t=0;
        k=0;
        lastentry = null;
        lasttierlmt = 0.0d;
        while(t<tiers.length && c < maxDepth && k < offerCount){
            double tierlmt = tiers[t];
            PriceTier entry = offers[k];
            if(entry.qty >= tierlmt){
                t++; //goto next tierlmt
                lasttierlmt = tierlmt;
                lastentry = entry;
            }else{
                if(lastentry!=null) {
                    if (entry.prc == lastentry.prc && entry.qty > lastentry.qty) {
                        k++; //goto next
                        lastentry = entry;
                    } else {
                        this.getOfferPrices()[c] = MathUtil.round(lastentry.prc, precision, BigDecimal.ROUND_CEILING);
                        this.getOfferQtys()[c] = MathUtil.round(lasttierlmt, tickValue, roundingType);
                        this.getOfferNoLP()[c] = lastentry.lpidx;
                        //reset temps
                        lastentry = null;
                        lasttierlmt = 0.0d;
                        c++;
                    }
                }
                k++;
            }
        }

        if(lastentry!=null) {
            this.getOfferPrices()[c] = MathUtil.round(lastentry.prc, precision, BigDecimal.ROUND_CEILING);
            this.getOfferQtys()[c] = MathUtil.round(lasttierlmt, tickValue, roundingType);
            this.getOfferNoLP()[c] = lastentry.lpidx;
            c++;
        }


        setNumOffers(c);
        if( c < maxDepth ){
            Arrays.fill(getOfferPrices(), c, maxDepth-1, 0.0d);
            Arrays.fill(getOfferQtys(), c, maxDepth-1, 0.0d);
        }

        if(log.isDebugEnabled()){
            builder.append(logRate());
            log.info(builder.toString());
        }

        return PostAggregationAction.Send;
    }

    @Override
    public long isInverted() {
        if(bidCount > 0 && offerCount > 0 ){
            if(maxbid.prc > lowestoffer.prc) {
                return maxbid.effectiveTime < lowestoffer.effectiveTime ? maxbid.streamidx : lowestoffer.streamidx;
            }
        }
        return 0;
    }

    public String logRate() {
        StringBuilder msg = new StringBuilder("MTF     ");
        int numBids = getNumBids();
        msg.append(getLogKey()).append(getTimeEffective()).append('|').append(numBids);

        for (int i = 0; i < numBids; i++) {
            msg.append('|').append(this.getBidPrices()[i])
                    .append('|').append(this.getBidQtys()[i])
                    .append('|').append(this.getBidNoLP()[i]);
        }

        int numOffers = getNumOffers();
        msg.append("|").append(numOffers);

        for (int i = 0; i < numOffers; i++) {
            msg.append('|').append(this.getOfferPrices()[i])
                    .append('|').append(this.getOfferQtys()[i])
                    .append('|').append(this.getOfferNoLP()[i]);
        }

        msg.append('#');
        msg.append(this.getBookId());
        msg.append('|').append(getQuoteId());
        msg.append('|').append(getQuoteTimeEffective());
        msg.append('|').append(getOldestQId());
        msg.append("|").append(hashCode());

        return msg.toString();
    }

}
