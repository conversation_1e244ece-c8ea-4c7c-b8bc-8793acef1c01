package com.integral.mdf.data;

import java.net.InetAddress;
import java.util.Map;
import java.util.Optional;

import com.integral.provision.CurrencyPairProvision;
import com.integral.provision.CurrencyProvision;
import com.integral.provision.VenueConfiguration;
import com.integral.virtualserver.MDFPartitionType;

public interface ServerProvision {

	public String getVenueName();

	public Optional<InetAddress> getRateMulticastgroup();

	public int getRateMulticastPort();

	public int getPriceBookMulticastPort();

	public Optional<InetAddress> getCreditMulticastGroup();

	public int getCreditMulticastPort();

	public Optional<Integer> getCcyIndex(String name);

    Optional<String> getOrgName(int orgIndex);

    public Optional<String> getCcyName(Integer index);

	public Optional<String> getCcyPairName(Integer index);

	public Optional<Integer> getCcyPairIndex(String name);

	public Optional<CurrencyPairProvision> getCcyPairProvision(String ccyPair);

	public Optional<CurrencyProvision> getCcyProvision(Integer index);

	public int getMaxCurrencyIndex();

	public Optional<InetAddress> getPriceBookMulticastgroup(Integer orgIndex);

	public FIProvision getFIProvision(Integer orgIndex);

	public Optional<Integer> getOrgIndex(String orgName);

	public int getThreadIdleTimeInterval();

	public int getMaxRateProcessorCount();

	public Optional<InetAddress> getLiveRateMulticastgroup();

	public int getLiveRateMulticastPort();

	public int getLiveRateMessageSize();

	public boolean isUseSuperLPStreamIndex();

	public void logDetails(String key);

	Optional<InetAddress> getMEHBMulticastAddress();

	int getMEHBPort();

	long getMEHBInterval();

	int getMaxPriceBookDepth();

	public boolean isValidateValueDate();

	public int getCreditCutoffPercent();

	public int getMulticastTTL();

	int getOpetationMode();

	boolean isClusteredNode();

	String[] getTenants();

	boolean isAggregationTimeSliced();

	boolean isOnDemandAggregation();

	String getVirtralServer();

    boolean isRatesFromMatchingEngine();

    void setVenueConfigurations(Map<String, VenueConfiguration> venueConfigurations);

	Optional<InetAddress> getRateMulticastgroup(String ccyp);

	boolean isUseCcypBasedMCastAddresses();

	void setUseCcypBasedMCastAddresses(boolean useCcypBasedMCastAddresses);

	MDFPartitionType getPartitionType();

	int getMaxAggregationAttempts();

	void setMaxAggregationAttempts(int maxAggregationAttempts);

	String getDistributedCacheHeartbeatAddress();

}