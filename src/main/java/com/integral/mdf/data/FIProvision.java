package com.integral.mdf.data;

import java.net.InetAddress;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import com.integral.provision.LPProvision;
import com.integral.provision.MDFAggregationType;
import com.integral.provision.SpreadRuleParameterProvision;

public interface FIProvision {
	
	public String getName();
	
	public int getIndex();
	
	public Optional<InetAddress> getPriceBookMulticastgroup();

	public Set<Integer> getSupportedCcyPairs();
	
	public Collection<LPProvision> getLPProvisions();
	
	public boolean isSupportedCcyPairForLP(Integer streamIndex,Integer ccyPairIdx);
	
	public List<SpreadRuleParameterProvision> getSpreads(int superBankLpStreamIndex, int baseccyIdx,int varccyIndex);
	
	public long getDefaultLEObjectId();
	
	public int getMarketDepth();
	
	public MDFAggregationType getAggregationType();
	
	public void reset();

	/**
	 * Returns tier-structure definf for MT-FOK aggregation.
	 * @return
	 */
	public double[] getAggregationTiers();

    int getAggregationInterval();
}
