package com.integral.mdf.data;

import java.math.BigDecimal;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.util.MathUtil;

public class BestPriceBook extends PriceBookV2 {
	
	PriceTier bid = new PriceTier(0.0d,0.0d,-1,0,0);
	PriceTier offer = new PriceTier(Double.MAX_VALUE,0.0d,-1,0,0);
	Log log = LogFactory.getLog(this.getClass());
	private StringBuilder builder = new StringBuilder();
	int bidCount =0;
	int offerCount =0;
	double maxbid = 0.0d;
	double lowestoffer = Double.MAX_VALUE;
	
	PriceTier lastbid = new PriceTier(0.0d,0.0d,-1,0,0);
	PriceTier lastoffer = new PriceTier(Double.MAX_VALUE,0.0d,-1,0,0);

	public BestPriceBook(boolean isTermCcyAgg, long requestId) {
		super(1, FLAG_TOB_BOOK, 0, isTermCcyAgg, requestId);
	}
	
	public void reset(){
		bidCount = 0;
		offerCount = 0;
		maxbid = 0.0d;
		lowestoffer = Double.MAX_VALUE;
		bid = new PriceTier(0.0d,0.0d,-1,0,0);
		offer = new PriceTier(Double.MAX_VALUE,0.0d,-1,0,0);
		if(log.isDebugEnabled()){
			builder.setLength(0);
		}
	}

	@Override
	public boolean addPrices(ProvisionedQuoteC pq) {
		if (pq.getPBidTiersNum() > 0 ){
			int offset = pq.pTierOffset(ProvisionedQuoteC.BUY, 0); //TOB
			double prc = pq.getPPrice(offset), d=0.0d;
			if(prc > maxbid && ( d = pq.getPShowQty(offset, isTermCcyAgg)) > 0.0D ){
				bid = new PriceTier(prc,d,pq.getLPIndex(),pq.getStreamIdx(),pq.getReceivedTime().toEpochMilli());
				bidCount = 1;
				maxbid = prc;
			}else if(maxbid != 0.0d && prc == maxbid && pq.getPShowQty(offset, isTermCcyAgg) > bid.qty ){
                bid = new PriceTier(prc,pq.getPShowQty(offset, isTermCcyAgg),pq.getLPIndex(),
						pq.getStreamIdx(),pq.getReceivedTime().toEpochMilli());
                bidCount = 1;
                maxbid = prc;
            }
		}else{
			if(maxbid==0.0d) {
				bid = new PriceTier(0.0d, 0.0d, -1,0,0);
				bidCount = 1;
			}
		}
		
		if(pq.getPOfferTiersNum() > 0 ){
			int offset = pq.pTierOffset(ProvisionedQuoteC.SELL, 0);//TOB
			double prc = pq.getPPrice(offset), d=0.0d;
			if( prc < lowestoffer && (d = pq.getPShowQty(offset, isTermCcyAgg)) > 0.0D ){ //better offer
				offer = new PriceTier(prc,d,pq.getLPIndex(),pq.getStreamIdx(),pq.getReceivedTime().toEpochMilli());
				offerCount = 1;
				lowestoffer = prc;
			}else if(lowestoffer != Double.MAX_VALUE && prc == lowestoffer && pq.getPShowQty(offset, isTermCcyAgg) > offer.qty ){
                offer = new PriceTier(prc,pq.getPShowQty(offset, isTermCcyAgg),pq.getLPIndex(),
						pq.getStreamIdx(),pq.getReceivedTime().toEpochMilli());
                offerCount = 1;
                lowestoffer = prc;
            }
		}else{
			if(lowestoffer == Double.MAX_VALUE ) {
				offer = new PriceTier(0.0d, 0.0d, -1,0,0);
				offerCount = 1;
			}
		}
		
		//book is inverted - start over
//		if(maxbid > lowestoffer ){
//			return true;
//		}

		//we added some prices successfully. send latest
		if( getValueDate() < pq.getValueDate()){
			setValueDate(pq.getValueDate());
		}
		
		if(log.isDebugEnabled() && pq != null){
			builder.append(pq.getProvisionedQuoteString());
			builder.append(" ");
		}
		
		return false;
	}

	@Override
	public PostAggregationAction copyPrices(int precision, int roundingType, double tickValue) {
		
		if( bidCount > 0 ){
			PriceTier entry = bid;
			this.getBidPrices()[0] = MathUtil.round(entry.prc,precision,BigDecimal.ROUND_FLOOR);
			this.getBidQtys()[0] = MathUtil.round(entry.qty,tickValue,roundingType);
			this.getBidNoLP()[0] = entry.lpidx;
		}else{
			this.getBidPrices()[0]=0.0d;
			this.getBidQtys()[0]=0.0d;
			this.getBidNoLP()[0]=0;
		}
		setNumBids(1);
		
		if( offerCount > 0 ) {
			PriceTier entry =  offer;
			this.getOfferPrices()[0] = MathUtil.round(entry.prc,precision,BigDecimal.ROUND_CEILING);
			this.getOfferQtys()[0] = MathUtil.round(entry.qty,tickValue,roundingType);
			this.getOfferNoLP()[0] = entry.lpidx;
		}else{
			this.getOfferPrices()[0]=0.0d;
			this.getOfferQtys()[0]=0.0d;
			this.getOfferNoLP()[0]=0;
		}
		setNumOffers(1);

		if( bid.isSameAs(lastbid) ){
			if(offer.isSameAs(lastoffer)){
				if(log.isDebugEnabled()){
					builder.append(logRate(PostAggregationAction.Drop));
					log.info(builder.toString());
				}
				return PostAggregationAction.Drop;
			}
		}
		
		lastbid = bid;
		lastoffer = offer;
		
		if(log.isDebugEnabled()){
			builder.append(logRate(PostAggregationAction.Send));
			log.info(builder.toString());
		}
		return PostAggregationAction.Send;
	}

	@Override
	public long isInverted() {
		if(bidCount > 0 && offerCount > 0 ){
			if(bid.prc > offer.prc ){
				return bid.effectiveTime < offer.effectiveTime ? bid.streamidx : offer.streamidx;
			}
		}
		return 0;
	}

	public String logRate(PostAggregationAction action) {
		StringBuilder msg = new StringBuilder("BPA ");
		int numBids = getNumBids();
		msg.append(getLogKey()).append(getTimeEffective()).append('|').append(numBids);
		
		for (int i = 0; i < numBids; i++) {
			msg.append('|').append(this.getBidPrices()[i])
			.append('|').append(this.getBidQtys()[i])
			.append('|').append(this.getBidNoLP()[i]);
		}
		
		int numOffers = getNumOffers();
		msg.append("|").append(numOffers);
		
		for (int i = 0; i < numOffers; i++) {
			msg.append('|').append(this.getOfferPrices()[i])
			.append('|').append(this.getOfferQtys()[i])
			.append('|').append(this.getOfferNoLP()[i]);
		}

		msg.append('#');
		msg.append(this.getBookId());
		msg.append('|').append(getQuoteId());
		msg.append('|').append(getQuoteTimeEffective());
		msg.append('|').append(getOldestQId());
		msg.append("|").append(hashCode());
		msg.append("|Action=" ).append(action.toString());
		
		return msg.toString();
	}

}
