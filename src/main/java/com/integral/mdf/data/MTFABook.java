package com.integral.mdf.data;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.util.MathUtil;

import java.math.BigDecimal;
import java.util.Arrays;

/**
 * Created by Rejeev on 12/1/2023.
 * <AUTHOR>
 */
public class MTFABook extends PriceBookV2 {

    PriceElementsHolder vwapHolder = new PriceElementsHolder();
    PriceElementsHolder faHolder = new PriceElementsHolder();

    static int MAX_TIER = 50;
    Log log = LogFactory.getLog(this.getClass());

    private StringBuilder builder = new StringBuilder();

    final double EPSILON = Double.valueOf("0.0000001");//6 digit precision.

    public MTFABook(int maxDepth, double[] tiers, boolean isTermCcyAgg, long requestId) {
        super(maxDepth,FLAG_MT_BOOK_FA, tiers, isTermCcyAgg, requestId);
        if( maxDepth > MAX_TIER ){
            throw new IllegalArgumentException("Invalid maxDepth. It can't be more then " + MAX_TIER);
        }
    }


    /**
     *
     */
    public void reset() {
        vwapHolder.reset();
        faHolder.reset();
        if(log.isDebugEnabled()){
            builder.setLength(0);
        }
    }

    public boolean addPrices(final ProvisionedQuoteC pq) {
        if(pq.isFullAmountStream()) return addPricesFOK(pq,faHolder);
        else return addPricesVwap(pq,vwapHolder);
    }
    public boolean addPricesFOK(final ProvisionedQuoteC pq, PriceElementsHolder holder) {
        int tier = 0;
        while(tier < pq.getPBidTiersNum() ){
            int offset = pq.pTierOffset(ProvisionedQuoteC.BUY, tier);
            double prc = pq.getPPrice(offset), d=0.0d;
            if( (d= pq.getPShowQty(offset, isTermCcyAgg) ) > 0.0d ) {
                holder.bids[holder.bidCount++] = new PriceTier(prc, pq.getPShowQty(offset, isTermCcyAgg), pq.getLPIndex(),
                        pq.getStreamIdx(),pq.getReceivedTime().toEpochMilli());
                if (prc > holder.maxbid.prc) {
                    holder.maxbid.prc = prc;
                    holder.maxbid.streamidx = pq.getStreamIdx();
                    holder.maxbid.effectiveTime = pq.getReceivedTime().toEpochMilli();
                }
            }
            tier++;
        }
        tier = 0;
        while(tier < pq.getPOfferTiersNum() ){
            int offset = pq.pTierOffset(ProvisionedQuoteC.SELL, tier);
            double prc = pq.getPPrice(offset), d=0.0d;
            if( (d= pq.getPShowQty(offset, isTermCcyAgg) ) > 0.0d ) {
                holder.offers[holder.offerCount++] = new PriceTier(prc, pq.getPShowQty(offset, isTermCcyAgg), pq.getLPIndex(),
                        pq.getStreamIdx(),pq.getReceivedTime().toEpochMilli());
                if (prc < holder.lowestoffer.prc) { //better offer
                    holder.lowestoffer.prc = prc;
                    holder.lowestoffer.streamidx = pq.getStreamIdx();
                    holder.lowestoffer.effectiveTime = pq.getReceivedTime().toEpochMilli();
                }
            }
            tier++;
        }

        //we added some prices successfully. send latest
        if( getValueDate() < pq.getValueDate()){
            setValueDate(pq.getValueDate());
        }

        if(log.isDebugEnabled()){
            builder.append(pq.getProvisionedQuoteString());
            builder.append(" ");
        }

        return false;
    }
    public boolean addPricesVwap(final ProvisionedQuoteC pq, PriceElementsHolder holder) {
        int tier = 0; PriceTier prev=null;
        while(tier < pq.getPBidTiersNum() ){
            int offset = pq.pTierOffset(ProvisionedQuoteC.BUY, tier);
            double prc = pq.getPPrice(offset), d=0.0d;
            if( (d= pq.getPShowQty(offset, isTermCcyAgg) ) > 0.0d ) {
                PriceTier ptier = new PriceTier(prc, pq.getPShowQty(offset, isTermCcyAgg), pq.getLPIndex(),
                        pq.getStreamIdx(),pq.getReceivedTime().toEpochMilli());
                ptier.prev = pq.isMultiTier() ? prev:null;
                holder.bids[holder.bidCount++] = ptier;
                if (prc > holder.maxbid.prc) {
                    holder.maxbid.prc = prc;
                    holder.maxbid.streamidx = pq.getStreamIdx();
                    holder.maxbid.effectiveTime = pq.getReceivedTime().toEpochMilli();
                }
                prev = ptier;
            }
            tier++;
        }

        tier = 0; prev=null;
        while(tier < pq.getPOfferTiersNum() ){
            int offset = pq.pTierOffset(ProvisionedQuoteC.SELL, tier);
            double prc = pq.getPPrice(offset), d=0.0d;
            if( (d= pq.getPShowQty(offset, isTermCcyAgg) ) > 0.0d ) {
                PriceTier ptier = new PriceTier(prc, pq.getPShowQty(offset, isTermCcyAgg), pq.getLPIndex(),
                        pq.getStreamIdx(),pq.getReceivedTime().toEpochMilli());
                ptier.prev = pq.isMultiTier() ? prev:null;
                holder.offers[holder.offerCount++] = ptier;
                if (prc < holder.lowestoffer.prc) { //better offer
                    holder.lowestoffer.prc = prc;
                    holder.lowestoffer.streamidx=pq.getStreamIdx();
                    holder.lowestoffer.effectiveTime=pq.getReceivedTime().toEpochMilli();
                }
                prev=ptier;
            }
            tier++;
        }

        //we added some prices successfully. send latest
        if( getValueDate() < pq.getValueDate()){
            setValueDate(pq.getValueDate());
        }

        if(log.isDebugEnabled()){
            builder.append(pq.getProvisionedQuoteString());
            builder.append(" ");
        }

        return false;
    }


    private double getBidForTierFOK(double tier, PriceElementsHolder holder){
        while (holder.bidPosition < holder.bidCount) {
            if (holder.bids[holder.bidPosition].qty >= tier) {
                double prc = holder.bids[holder.bidPosition].prc;
                return prc;
            }
            holder.bidPosition++;
        }
        return 0.0d;
    }
    private double getOfferForTierFOK(double tier, PriceElementsHolder holder){
        while (holder.offerPosition < holder.offerCount) {
            if (holder.offers[holder.offerPosition].qty >= tier) {
                double prc = holder.offers[holder.offerPosition].prc;
                return prc;
            }
            holder.offerPosition++;
        }
        return 0.0d;
    }

    private double getBidForTierVwap(double tier, PriceElementsHolder holder, int precision){
        while (holder.bidPosition < holder.bidCount) {
            if (holder.cumBidQty >= tier) {
                double prc = holder.cumBidVol/holder.cumBidQty;
                return MathUtil.round(prc, precision, BigDecimal.ROUND_FLOOR);
            }
            int pos = holder.bidPosition;
            double prevVol = 0.0, prevQty = 0.0;
            PriceTier priceTier = holder.bids[pos];
            if(priceTier.prev != null){
                prevVol = priceTier.prev.qty * priceTier.prev.prc;
                prevQty = priceTier.prev.qty;
            }
            if((holder.cumBidQty - prevQty ) + priceTier.qty >= tier) {
                double prc = (holder.cumBidVol - prevVol + priceTier.prc * (tier - (holder.cumBidQty - prevQty))) / tier;
                return MathUtil.round(prc, precision, BigDecimal.ROUND_FLOOR);
            }
            holder.cumBidQty = holder.cumBidQty - prevQty + priceTier.qty;
            holder.cumBidVol = holder.cumBidVol - prevVol + priceTier.qty * priceTier.prc;
            holder.bidPosition++;
        }
        return 0.0d;
    }
    private double getOfferForTierVwap(double tier, PriceElementsHolder holder, int precision) {
        while (holder.offerPosition < holder.offerCount) {
            if (holder.cumOfferQty >= tier) {
                double prc = holder.cumOfferVol/holder.cumOfferQty;
                return MathUtil.round(prc, precision, BigDecimal.ROUND_CEILING);
            }
            int pos = holder.offerPosition;
            double prevVol = 0.0, prevQty = 0.0;
            PriceTier priceTier = holder.offers[pos];
            if(priceTier.prev != null){
                prevVol = priceTier.prev.qty * priceTier.prev.prc;
                prevQty = priceTier.prev.qty;
            }
            if((holder.cumOfferQty - prevQty) + priceTier.qty >= tier) {
                double prc = ((holder.cumOfferVol - prevVol) + priceTier.prc * (tier - (holder.cumOfferQty - prevQty))) / tier;
                return MathUtil.round(prc, precision, BigDecimal.ROUND_CEILING);
            }
            holder.cumOfferQty = holder.cumOfferQty - prevQty + priceTier.qty;
            holder.cumOfferVol = holder.cumOfferVol - prevVol + priceTier.qty * priceTier.prc;
            holder.offerPosition++;
        }
        return 0.0d;
    }

    public PostAggregationAction copyPrices(int precision,int roundingType , double tickValue) {
        //logPriceTiers(vwapHolder.bids, vwapHolder.bidCount, "Bids Before Sorting");
        //logPriceTiers(vwapHolder.offers, vwapHolder.offerCount, "Offers Before Sorting");
        if(vwapHolder.bidCount <= 0 && vwapHolder.offerCount <= 0 && faHolder.bidCount <= 0 && faHolder.offerCount <= 0){
            //send zero price book. MDG treat it is as inactive quote.
            setNumBids(1);
            this.getBidPrices()[0]=0.0d;
            this.getBidQtys()[0]=0.0d;
            this.getBidNoLP()[0]=0;

            setNumOffers(1);
            this.getOfferPrices()[0]=0.0d;
            this.getOfferQtys()[0]=0.0d;
            this.getOfferNoLP()[0]=0;

            if(log.isDebugEnabled()){
                builder.append(logRate());
                log.info(builder.toString());
            }

            return PostAggregationAction.Send;
        }

        Arrays.sort(vwapHolder.bids,0,vwapHolder.bidCount, (o1,o2) -> {
            int a = Double.compare(o2.prc, o1.prc);
            return a == 0 ? Double.compare(o2.qty,o1.qty) : a;
        });
        Arrays.sort(vwapHolder.offers,0,vwapHolder.offerCount,(o1,o2)->{
            int a = Double.compare(o1.prc, o2.prc);
            return a == 0 ? Double.compare(o2.qty,o1.qty) : a;
        });
        Arrays.sort(faHolder.bids,0,faHolder.bidCount, (o1,o2) -> {
            int a = Double.compare(o2.prc, o1.prc);
            return a == 0 ? Double.compare(o2.qty,o1.qty) : a;
        });
        Arrays.sort(faHolder.offers,0,faHolder.offerCount,(o1,o2)->{
            int a = Double.compare(o1.prc, o2.prc);
            return a == 0 ? Double.compare(o2.qty,o1.qty) : a;
        });

        //logPriceTiers(vwapHolder.bids, vwapHolder.bidCount, "Bids After Sorting");
        //logPriceTiers(vwapHolder.offers, vwapHolder.offerCount, "Offers After Sorting");
        int maxDepth = getMaxDepth();

        int c=0;
        int t=0;
        double lastvwap = Double.MAX_VALUE, lastTier = 0.0d;
        boolean stopAggregation = false;
        final double[] tiers = getTiers();
        while (t<tiers.length && c < (maxDepth-1) && !stopAggregation) {
            double tierlmt = tiers[t++];
            double vwapBid = getBidForTierVwap(tierlmt, vwapHolder, precision);
            double faBid = getBidForTierFOK(tierlmt, faHolder);
            double bid = Math.max(vwapBid,faBid);
            if(bid == 0.0d) {
                stopAggregation = true;
                //publish available amount
                double vwapQty = vwapHolder.cumBidQty;
                double vwapPrc = vwapQty > 0 ? vwapHolder.cumBidVol / vwapQty : 0.0d;
                vwapPrc = MathUtil.round(vwapPrc, precision, BigDecimal.ROUND_FLOOR);
                PriceTier faMaxTier = getMaxLiquidity(faHolder.bids,faHolder.bidCount,true);
                double faPrc = faMaxTier != null ? faMaxTier.prc : 0.0d;
                double faQty = faMaxTier != null ? faMaxTier.qty : 0.0d;
                //System.out.println("vwapQty: " + vwapQty + " faQty: " + faQty + " vwapPrc: " + vwapPrc + " faPrc: " + faPrc);
                boolean useVwap = vwapQty == faQty ? vwapPrc > faPrc : vwapQty > faQty;
                if(useVwap && vwapQty > lastTier){
                    bid = vwapPrc;
                    tierlmt = vwapQty;
                }else if(!useVwap && faQty > lastTier){
                    bid = faHolder.bids[faHolder.bidCount-1].prc;
                    tierlmt = faQty;
                }
            }
            if(bid == 0.0d || tierlmt == 0.0d) break;
            //System.out.println("Bid: " + bid + " last: " + lastvwap);
            if(lastvwap - bid > EPSILON){
                this.getBidPrices()[c] = bid;
                this.getBidQtys()[c] = tierlmt;
                lastvwap = bid;
                lastTier = tierlmt;
                c++;
            }else {
                this.getBidQtys()[c-1] = tierlmt;
            }
        }
        setNumBids(c);
        if( c < maxDepth ){
            Arrays.fill(getBidPrices(), c, maxDepth-1, 0.0d);
            Arrays.fill(getBidQtys(), c, maxDepth-1, 0.0d);
        }

        //Offers
        t=0;c=0;lastvwap = 0.0d;lastTier = 0.0d;
        stopAggregation = false;
        while (t<tiers.length && c < (maxDepth-1) && !stopAggregation) {
            double tierlmt = tiers[t++];
            double vwapOffer = getOfferForTierVwap(tierlmt, vwapHolder, precision);
            double faOffer = getOfferForTierFOK(tierlmt, faHolder);
            double offer = vwapOffer == 0.0d ? faOffer : faOffer == 0.0d ? vwapOffer : Math.min(vwapOffer,faOffer);

            if(offer == 0.0d) {
                stopAggregation = true;
                //publish available amount
                double vwapQty = vwapHolder.cumOfferQty;
                double vwapPrc = vwapQty > 0 ? vwapHolder.cumOfferVol / vwapQty : 0.0d;
                vwapPrc = MathUtil.round(vwapPrc, precision, BigDecimal.ROUND_CEILING);
                PriceTier faMaxTier = getMaxLiquidity(faHolder.offers,faHolder.offerCount, false);
                double faPrc = faMaxTier != null ? faMaxTier.prc : 0.0d;
                double faQty = faMaxTier != null ? faMaxTier.qty : 0.0d;
                //System.out.println("vwapQty: " + vwapQty + " faQty: " + faQty + " vwapPrc: " + vwapPrc + " faPrc: " + faPrc);
                boolean useVwap = vwapQty == faQty ? vwapPrc < faPrc : vwapQty > faQty;
                if(useVwap && vwapQty > lastTier){
                    offer = vwapPrc;
                    tierlmt = vwapQty;
                }else if(!useVwap && faQty > lastTier){
                    offer = faPrc;
                    tierlmt = faQty;
                }
            }
            if(offer == 0.0d || tierlmt == 0.0d) break;
            //System.out.println("Offer: " + offer + " last: " + lastvwap);
            if(offer - lastvwap > EPSILON){
                this.getOfferPrices()[c] = offer;
                this.getOfferQtys()[c] = tierlmt;
                lastvwap = offer;
                lastTier = tierlmt;
                c++;
            }else {
                this.getOfferQtys()[c-1] = tierlmt;
            }
        }
        setNumOffers(c);
        if( c < maxDepth ){
            Arrays.fill(getOfferPrices(), c, maxDepth-1, 0.0d);
            Arrays.fill(getOfferQtys(), c, maxDepth-1, 0.0d);
        }

        if(log.isDebugEnabled()){
            builder.append(logRate());
            log.info(builder.toString());
        }
        return PostAggregationAction.Send;
    }

    private PriceTier getMaxLiquidity(PriceTier[] tiers, int count, boolean isBid){
        if(count == 0) return null;
        PriceTier max = tiers[0];
        //logPriceTiers(tiers, count, "getPriceForMaxAmt");
        for(int i=1;i<count;i++){
            if(tiers[i].qty > max.qty) max = tiers[i];
            else if(tiers[i].qty == max.qty){
                boolean isBetter = isBid ? tiers[i].prc > max.prc : tiers[i].prc < max.prc;
                if(isBetter) max = tiers[i];
            }
        }
        return max;
    }

    private void logPriceTiers(PriceTier[] tiers, int count, String type){
        StringBuilder sb = new StringBuilder(type + " Tiers:");
        for(int i=0;i<count;i++){
            PriceTier tier = tiers[i];
            sb.append(tier.prc).append(":").append(tier.qty).append(",");
        }
        log.info(sb.toString());
    }

    @Override
    public long isInverted() {
        PriceTier maxbid = null;
        if(vwapHolder.maxbid != null && faHolder.maxbid != null){
            maxbid = vwapHolder.maxbid.prc > faHolder.maxbid.prc ? vwapHolder.maxbid : faHolder.maxbid;
        } else if (vwapHolder.maxbid != null) {
            maxbid = vwapHolder.maxbid;
        } else if (faHolder.maxbid != null) {
            maxbid = faHolder.maxbid;
        }
        PriceTier lowestoffer = null;
        if(vwapHolder.lowestoffer != null && faHolder.lowestoffer != null){
            lowestoffer = vwapHolder.lowestoffer.prc < faHolder.lowestoffer.prc ? vwapHolder.lowestoffer : faHolder.lowestoffer;
        } else if (vwapHolder.lowestoffer != null) {
            lowestoffer = vwapHolder.lowestoffer;
        } else if (faHolder.lowestoffer != null) {
            lowestoffer = faHolder.lowestoffer;
        }
        if(maxbid != null && lowestoffer != null){
            if(maxbid.prc > lowestoffer.prc){
                return maxbid.effectiveTime < lowestoffer.effectiveTime ? maxbid.streamidx : lowestoffer.streamidx;
            }
        }
        return 0;
    }

    public String logRate() {
        StringBuilder msg = new StringBuilder("MTFA  ");
        int numBids = getNumBids();
        msg.append(getLogKey()).append(getTimeEffective()).append('|').append(numBids);

        for (int i = 0; i < numBids; i++) {
            msg.append('|').append(this.getBidPrices()[i])
                    .append('|').append(this.getBidQtys()[i])
                    .append('|').append(this.getBidNoLP()[i]);
        }

        int numOffers = getNumOffers();
        msg.append("|").append(numOffers);

        for (int i = 0; i < numOffers; i++) {
            msg.append('|').append(this.getOfferPrices()[i])
                    .append('|').append(this.getOfferQtys()[i])
                    .append('|').append(this.getOfferNoLP()[i]);
        }

        msg.append('#');
        msg.append(this.getBookId());
        msg.append('|').append(getQuoteId());
        msg.append('|').append(getQuoteTimeEffective());
        msg.append('|').append(getOldestQId());
        msg.append("|").append(hashCode());

        return msg.toString();
    }

}
