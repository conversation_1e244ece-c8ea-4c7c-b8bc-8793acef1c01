package com.integral.mdf.data;

import java.util.Arrays;
import java.util.concurrent.atomic.AtomicReference;

public interface ModifiableTiers {

    final AtomicReference<double[]> tiers = new AtomicReference<>(new double[0]);

    /**
     * Not a thread safe method. Might add same tier twice.
     * @param tier
     * @return
     */
    default boolean addTier(double tier){
        for(double otier:tiers.get()){
            if(Math.abs(otier-tier) < 0.001){
                return false;
            }
        }

        double[] newTiers = new double[tiers.get().length+1];
        System.arraycopy(tiers.get(),0,newTiers,0,tiers.get().length);
        newTiers[newTiers.length-1] = tier;
        Arrays.sort(newTiers);
        tiers.set(newTiers);
        return true;
    }

    default double[] getTiers(){
        return tiers.get();
    }

}
