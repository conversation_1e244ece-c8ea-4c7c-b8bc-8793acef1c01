package com.integral.mdf.data;

import java.net.InetAddress;
import java.util.*;

import com.integral.log.LogFactory;
import com.integral.mdf.Util;
import com.integral.provision.CurrencyPairProvision;
import com.integral.provision.LPProvision;
import com.integral.provision.MDFAggregationType;
import com.integral.provision.OrgProvision;
import com.integral.provision.PPPProvision;
import com.integral.provision.SpreadRuleParameterProvision;

public class FIProvisionImpl implements FIProvision {

	private OrgProvision orgProvision;
	private Optional<InetAddress> priceBookMulticastGroup;

	// All currencies supported by its LP organizations cumulative
	private Map<Integer, CurrencyPairProvision> supportedCcyPairs = new HashMap<Integer, CurrencyPairProvision>();

	// All currencies supported by its LP organizations cumulative
	private Map<Integer, Set<Integer>> lpSupportedCcyPairs = new HashMap<Integer, Set<Integer>>();

	// All currencies supported by FI organization
	private Map<Integer, CurrencyPairProvision> rateConventions = new HashMap<Integer, CurrencyPairProvision>();

	private Map<Integer,LPProvision> provisionedLps = new HashMap<Integer, LPProvision>();

	private Map<Long,PPPProvision> spreadProvisionMap = new HashMap<Long, PPPProvision>();

	private String name;
	
	private long defaultLEObjId;
	
	private int index;
	
	private int marketDepth;
	private MDFAggregationType aggregationType = MDFAggregationType.FULL_BOOK;

	private double[] tiers = new double[0];

	private int aggregationInterval;
	private String tierCurrency = null;

	public FIProvisionImpl(String fiName){
		this.name = fiName;
	}

	@Override
	public String getName(){
		return this.name;
	}

	@Override
	public Optional<InetAddress> getPriceBookMulticastgroup() {
		return priceBookMulticastGroup;
	}

	@Override
	public Collection<LPProvision> getLPProvisions() {
		return provisionedLps.values();
	}

	@Override
	public List<SpreadRuleParameterProvision> getSpreads(int superBankLpStreamIndex, int baseccyIdx,int varccyIndex) {
		Optional<Long> spreadKey = getSpreadKey(superBankLpStreamIndex, baseccyIdx, varccyIndex);

		if(spreadKey.isPresent()){
			PPPProvision pppProvision = spreadProvisionMap.get(spreadKey.get());
			if(null!=pppProvision){
				return pppProvision.getSpreads();
			}
		}

		return Collections.emptyList();
	}
	
	@Override
	public boolean isSupportedCcyPairForLP(Integer streamIndex,Integer ccyPairIdx) {
		Set<Integer> set = lpSupportedCcyPairs.get(streamIndex);
		if(null==set){
			return false;
		}
		return set.contains(ccyPairIdx);
	}

	@Override
	public long getDefaultLEObjectId() {
		return this.defaultLEObjId;
	}
	
	public int getMarketDepth() {
		return marketDepth;
	}

	public void setMarketDepth(int marketDepth) {
		this.marketDepth = marketDepth;
	}

	public Optional<Long> getSpreadKey(int superBankLpStreamIndex, int baseccyIdx,
			int varccyIndex) {

		Long index = Long.valueOf(baseccyIdx);
		index <<= 16;

		index |= varccyIndex & 0xFFFF;
		index <<= 16;

		index |= (superBankLpStreamIndex & 0xFFFF);

		return Optional.of(index);
	}

	public void setOrgProvision(OrgProvision orgProvision) {
		this.orgProvision = orgProvision;
	}

	public OrgProvision getOrgProvision() {
		return orgProvision;
	}

	public void setPriceBookMulticastgroup(
			Optional<InetAddress> rateMulticastgroup) {
		this.priceBookMulticastGroup = rateMulticastgroup;
	}

	public Optional<CurrencyPairProvision> getCcyProvision(String ccyPairName) {
		CurrencyPairProvision currencyPairProvision = supportedCcyPairs
				.get(ccyPairName);
		if (null == currencyPairProvision) {
			return Optional.empty();
		}
		return Optional.of(currencyPairProvision);
	}

	public void setSupportedCcyPairs(
			Map<Integer, CurrencyPairProvision> supportedCcyPairs) {
		this.supportedCcyPairs = supportedCcyPairs;
	}

	public void setLPSupportedCcyPairs(
			Map<Integer, Set<Integer>> supportedCcyPairs) {
		this.lpSupportedCcyPairs = supportedCcyPairs;
	}

	public Set<Integer> getSupportedCcyPairs() {
		return this.supportedCcyPairs.keySet();
	}

	public void add(LPProvision lpProvision,ServerProvision serverProvision) {
		provisionedLps.put(Util.getStreamIndex(serverProvision, lpProvision),lpProvision);
	}

	public void putSpreadProvision(Long key , PPPProvision spreadProvision) {
		this.spreadProvisionMap.put(key,spreadProvision);
	}

	public void setRateConventions(
			Map<Integer, CurrencyPairProvision> ccyPairProvisions) {
		this.rateConventions = ccyPairProvisions;
	}

	public Map<Integer, CurrencyPairProvision> getRateConventions() {
		return rateConventions;
	}

	public Optional<InetAddress> getPriceBookMulticastGroup() {
		return priceBookMulticastGroup;
	}

	public Map<Integer, LPProvision> getProvisionedLps() {
		return provisionedLps;
	}

	public Map<Long, PPPProvision> getSpreadProvisionMap() {
		return spreadProvisionMap;
	}
	
	public void setDefaultLEObjId(long defaultLEObjId) {
		this.defaultLEObjId = defaultLEObjId;
	}

	public String getShortString(){
		StringBuilder str = new StringBuilder("FIProvisionImpl [")
				.append("name=" ).append(name )
				.append(", index=" ).append(index )
				.append(", defaultLEId=" ).append(defaultLEObjId)
				.append(", multicastGroup=" ).append(priceBookMulticastGroup)
				.append(", aggregationType=").append(aggregationType)
				.append(", marketDepth=" ).append(marketDepth)
				.append(", aggregationInterval=" ).append(aggregationInterval)
				.append(", tiers=" ).append(tiers!=null? Arrays.toString(tiers):"[]")
				.append("]");
		return str.toString();
	}

	@Override
	public String toString() {
		StringBuilder str = new StringBuilder("FIProvisionImpl [orgProvision=");
			str.append(orgProvision)
			.append(",\n  priceBookMulticastGroup=" ).append(priceBookMulticastGroup)
			.append(", name=" ).append(name )
			.append(", defaultLEObjId=" ).append(defaultLEObjId)
			.append(", index=" ).append(index )
			.append(", aggregationType=").append(aggregationType)
			.append(", marketDepth=" ).append(marketDepth)
			.append(", aggregationInterval=" ).append(aggregationInterval)
			.append(", tierCurrency=" ).append(tierCurrency)
			.append(", tiers=" ).append(tiers!=null? Arrays.toString(tiers):"[]");

			str.append("],\n supportedCcyPairs={");
			for (Map.Entry<Integer, CurrencyPairProvision> entry : this.supportedCcyPairs.entrySet()) {
				str.append("\n");
				str.append(entry.getKey()).append("=").append(entry.getValue());
			}
				
			str.append("},\n lpSupportedCcyPairs={" );
			for (Map.Entry<Integer, Set<Integer>> entry : this.lpSupportedCcyPairs.entrySet()) {
				str.append("\n");
				str.append(entry.getKey()).append("=").append(entry.getValue());
			}
			
			str.append("},\n rateConventions={" );			
			for (Map.Entry<Integer, CurrencyPairProvision> entry : this.rateConventions.entrySet()) {
				str.append("\n");
				str.append(entry.getKey()).append("=").append(entry.getValue());
			}
					
			str.append("},\n provisionedLps={");
			for (Map.Entry<Integer, LPProvision> entry : this.provisionedLps.entrySet()) {
				str.append("\n");
				str.append(entry.getKey()).append("=").append(entry.getValue());
			}
			str.append("},\n spreadProvisionMap={" );
			for (Map.Entry<Long, PPPProvision> entry : this.spreadProvisionMap.entrySet()) {
				str.append("\n");
				str.append(entry.getKey()).append("=").append(entry.getValue());
			}
			
			str.append("}");
		
		return str.toString();
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public int getIndex() {
		return index;
	}

	@Override
	public void reset() {
		this.orgProvision =null;		
		this.name =null;
		this.defaultLEObjId = 0l;
		this.index = 0;
		this.marketDepth =0;
		this.supportedCcyPairs.clear();
		this.lpSupportedCcyPairs.clear();
		//this.rateConventions.clear();
		this.provisionedLps.clear();
		this.spreadProvisionMap.clear();
		this.priceBookMulticastGroup = Optional.empty();
		this.tierCurrency = null;
	}

	public void setAggregationType(MDFAggregationType mdfAggregationType) {
		this.aggregationType = mdfAggregationType;
	}
	
	public MDFAggregationType getAggregationType(){
		return this.aggregationType;
	}

	public double[] getAggregationTiers(){
		return this.tiers;
	}

	public void setAggregationInterval(int aggregationInterval) {
		this.aggregationInterval = aggregationInterval;
	}

	@Override
	public int getAggregationInterval() {
		return aggregationInterval;
	}

	public void setAggregationTiers(double[] tiers){
		if(tiers!=null){
			this.tiers = tiers;
		}
	}

	public String getTierCurrency() {
		return tierCurrency;
	}

	public void setTierCurrency(String tierCurrency) {
		this.tierCurrency = tierCurrency;
	}
}