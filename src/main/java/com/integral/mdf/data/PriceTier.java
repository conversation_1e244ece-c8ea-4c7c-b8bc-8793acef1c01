package com.integral.mdf.data;

public class PriceTier {
	
	double prc;
	double qty;
	int lpidx;
	long streamidx;
	long effectiveTime;
	PriceTier prev;//multi tier quotes.

	public PriceTier(){}

	public PriceTier(double prc, double pShowQty, int lpidx, long streamIdx, long time) {
		this.prc = prc;
		this.qty = pShowQty;
		this.lpidx = lpidx;
		this.streamidx=streamIdx;
		this.effectiveTime=time;
	}
	
	public boolean isSameAs(final PriceTier in){
		return Double.compare(prc, in.prc) == 0 
			&& Double.compare(qty, in.qty) == 0;
	}
	
}
