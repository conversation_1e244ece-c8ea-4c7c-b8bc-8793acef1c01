package com.integral.mdf.data;

import java.net.InetAddress;
import java.util.*;
import java.util.Map.Entry;

import com.integral.mdf.proivision.builder.RemoteProvisionBuilder;
import com.integral.provision.VenueConfiguration;
import com.integral.virtualserver.MDFEntity;
import com.integral.virtualserver.MDFPartitionType;
import org.jctools.maps.NonBlockingHashMap;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.provision.CurrencyPairProvision;
import com.integral.provision.CurrencyProvision;

public class ServerProvisionImpl implements ServerProvision {

	private Optional<InetAddress> creditMulticastGroup;

	private int creditMulticastPort;

	private Optional<InetAddress> rateMulticastgroup;

	private int rateMulticastPort;

	private int priceBookMulticastPort;

	private String venueName;

	private int maxCurrencyIdx;
	
	private int maxRateProcessorCount;
	
	private int threadIdleTimeInterval;
	
	private Optional<InetAddress> liveRateMulticastgroup;

	private int liveRateMulticastPort;
	
	private int liveRateMessageSize;
	
	private boolean useSuperLPStreamIdx;
	
	//Only brought in during server init 
	private Map<Integer, String> ccyIdxVsNames = new HashMap<Integer, String>();

	private Map<String, Integer> ccyNamesVsIdx = new HashMap<String, Integer>();

	//Below data structures are updated for every FI  provisioned so using concurrent data structure 
	private NonBlockingHashMap<Integer,String> orgIdxVsNames = new NonBlockingHashMap<Integer,String>(1024);

	private NonBlockingHashMap<String, Integer> orgNamesVsIdx = new NonBlockingHashMap<String, Integer>(1024);
	
	private NonBlockingHashMap<Integer,String> ccyPairIdxVsNames = new NonBlockingHashMap<Integer,String>(2048);

	private NonBlockingHashMap<String, Integer> ccyPairNamesVsIdx = new NonBlockingHashMap<String, Integer>(2048);
	
	private NonBlockingHashMap<String, CurrencyPairProvision> ccyPairIdxVsProvision = new NonBlockingHashMap<String, CurrencyPairProvision>(2048);
	
	private NonBlockingHashMap<Integer,FIProvision> fiProvisions = new NonBlockingHashMap<Integer,FIProvision>(256);

	private NonBlockingHashMap<Integer, CurrencyProvision> ccyIdxVsProvision = new NonBlockingHashMap<Integer, CurrencyProvision>(1024);

	private Log log = LogFactory.getLog(ServerProvisionImpl.class);
	
	private int meHBPort;
	
	private Optional<InetAddress> meHBMulticastAddress;

	private long mEHBInterval;

	private int maxPriceBookDepth;
	
	private boolean validateValueDate;

	private int creditCutoffPercent;

	private int multicastTTL;
	private int opmode = RemoteProvisionBuilder.OPMODE_CLUSTERED;
	private String[] tenants = new String[0];
	private String aggregationFrequency = "TICK";//TICK is default
	private String nodeType = "PREDEFINED";//PREDEFINED is default
	private String virtralServer;
	private boolean isRatesFromMatchingEngine = true;
	private Map<String, VenueConfiguration> venueConfigurations;

	private boolean useCcypBasedMCastAddresses;
	private MDFPartitionType partitionType = MDFPartitionType.ORG;
	private int maxAggregationAttempts = 10;

	private String distributedCacheHeartbeatAddress;

	@Override
	public Optional<InetAddress> getRateMulticastgroup() {
		return rateMulticastgroup;
	}

	@Override
	public int getRateMulticastPort() {
		return rateMulticastPort;
	}

	@Override
	public int getPriceBookMulticastPort() {
		return priceBookMulticastPort;
	}

	@Override
	public int getMaxCurrencyIndex() {
		return maxCurrencyIdx;
	}

	@Override
	public FIProvision getFIProvision(Integer orgIndex) {
		return fiProvisions.get(orgIndex);
	}

	@Override
	public String getVenueName() {
		return venueName;
	}

	@Override
	public Optional<InetAddress> getCreditMulticastGroup() {
		return creditMulticastGroup;
	}
	
	@Override
	public int getCreditMulticastPort() {
		return creditMulticastPort;
	}
	
	@Override
	public int getThreadIdleTimeInterval() {
		return threadIdleTimeInterval;
	}

	@Override
	public int getMaxRateProcessorCount() {
		return maxRateProcessorCount;
	}
	
	@Override
	public int getLiveRateMessageSize() {
		return liveRateMessageSize;
	}
	
	@Override
	public boolean isUseSuperLPStreamIndex() {
		return useSuperLPStreamIdx;
	}

	@Override
	public Optional<Integer> getOrgIndex(String orgName) {
		Integer value = orgNamesVsIdx.get(orgName);
		if(null==value){
			return Optional.empty(); 
		}
		return Optional.of(value);
	}
	
	@Override
	public Optional<String> getOrgName(int orgIndex) {
		String value = orgIdxVsNames.get(orgIndex);
		if(null==value ){
			return Optional.empty(); 
		}
		return Optional.of(value); 
	}

	@Override
	public Optional<String> getCcyName(Integer index) {
		String name = ccyIdxVsNames.get(index);
		if (null != name) {
			return Optional.of(name);
		}
		return Optional.empty();
	}
	
	@Override
	public Optional<Integer> getCcyIndex(String name) {
		Integer index = ccyNamesVsIdx.get(name);
		if (null != index) {
			return Optional.of(index);
		}
		return Optional.empty();
	}
	
	public Optional<CurrencyPairProvision> getCcyPairProvision(String ccyPairName){
		CurrencyPairProvision ccyPairProvision = ccyPairIdxVsProvision.get(ccyPairName);
		if (null != ccyPairProvision) {
			return Optional.of(ccyPairProvision);
		}
		return Optional.empty();
	}

	public Map getCcyPairIdxVsProvisionMap(){
		return Collections.unmodifiableMap(ccyPairIdxVsProvision);
	}

	@Override
	public Optional<String> getCcyPairName(Integer index) {
		String name = ccyPairIdxVsNames.get(index);
		if (null != name) {
			return Optional.of(name);
		}
		return Optional.empty();
	}
	
	@Override
	public Optional<Integer> getCcyPairIndex(String name) {
		Integer index = ccyPairNamesVsIdx.get(name);
		if (null != index) {
			return Optional.of(index);
		}
		return Optional.empty();
	}
	
	@Override
	public Optional<InetAddress> getPriceBookMulticastgroup(Integer orgIndex) {
		FIProvision fiProvision = fiProvisions.get(orgIndex);
		if (null != fiProvision) {
			return fiProvision.getPriceBookMulticastgroup();
		}
		return Optional.empty();
	}
	
	public Optional<CurrencyProvision> getCcyProvision(Integer index){
		CurrencyProvision currencyProvision = ccyIdxVsProvision.get(index);
		if (null != currencyProvision) {
			return Optional.of(currencyProvision);
		}
		return Optional.empty();
	}
	
	@Override
	public Optional<InetAddress> getLiveRateMulticastgroup() {
		return liveRateMulticastgroup;
	}

	@Override
	public int getLiveRateMulticastPort() {
		return liveRateMulticastPort;
	}
	
	public void setRateMulticastgroup(Optional<InetAddress> rateMulticastgroup) {
		this.rateMulticastgroup = rateMulticastgroup;
	}

	public void setRateMulticastPort(int rateMulticastPort) {
		this.rateMulticastPort = rateMulticastPort;
	}

	public void setPriceBookMulticastPort(int priceBookMulticastPort) {
		this.priceBookMulticastPort = priceBookMulticastPort;
	}

	public void setVenueName(String tradingVenue) {
		this.venueName = tradingVenue;
	}

	public void setMaxCcyIndex(int maxCurrencyIndex) {
		this.maxCurrencyIdx = maxCurrencyIndex;
	}


	public void setCcyIndexVsNames(Map<Integer, String> ccyIndexVsNames) {
		this.ccyIdxVsNames = ccyIndexVsNames;
	}

	public void setCcyNamesVsIndex(Map<String, Integer> ccyNamesVsIndex) {
		this.ccyNamesVsIdx = ccyNamesVsIndex;
	}

	public void setCreditMulticastGroup(
			Optional<InetAddress> creditMulticastGroup) {
		this.creditMulticastGroup = creditMulticastGroup;
	}

	public void setCreditMulticastPort(int creditMulticastPort) {
		this.creditMulticastPort = creditMulticastPort;
	}

	public void addOrgMapping(String orgName, Integer orgIndex,
			FIProvision provision) {
		orgNamesVsIdx.put(orgName, orgIndex);
		orgIdxVsNames.put(orgIndex, orgName);
		if( provision != null ){
			fiProvisions.put(orgIndex, provision);
		}
	}

	public void addCcypPairMapping(String ccyPairName, Integer ccyPairIndex) {
		ccyPairNamesVsIdx.put(ccyPairName, ccyPairIndex);
		ccyPairIdxVsNames.put(ccyPairIndex, ccyPairName);
	}
	
	public void addCcypPairProvision(String ccyPairName, CurrencyPairProvision provision) {
		ccyPairIdxVsProvision.put(ccyPairName, provision);
	}

	public Map<Integer, String> getCcyIndexVsNames() {
		return ccyIdxVsNames;
	}

	public Map<String, Integer> getCcyNamesVsIndex() {
		return ccyNamesVsIdx;
	}
	
	public void addCcyPairMapping(String ccyPairName,Integer ccyPairIndex) {
		ccyPairIdxVsNames.put(ccyPairIndex, ccyPairName);
		ccyPairNamesVsIdx.put(ccyPairName, ccyPairIndex);
	}
	
	public void addCcyProvision(int index, CurrencyProvision ccyProvision) {
		ccyIdxVsProvision.put(index, ccyProvision);
	}

	public void setMaxRateProcessorCount(int maxRateProcessorCount) {
		this.maxRateProcessorCount = maxRateProcessorCount;
	}

	
	public void setThreadIdleTimeInterval(int threadIdleTimeInterval) {
		this.threadIdleTimeInterval = threadIdleTimeInterval;
	}

	public void setLiveRateMulticastgroup(
			Optional<InetAddress> liveRateMulticastgroup) {
		this.liveRateMulticastgroup = liveRateMulticastgroup;
	}

	public void setLiveRateMulticastPort(int liveRateMulticastPort) {
		this.liveRateMulticastPort = liveRateMulticastPort;
	}
	
	public void setRateMessageSize(int rateMessageSize) {
		this.liveRateMessageSize = rateMessageSize;
	}
	
	public void setUseSuperLPStreamIndex(boolean useSuperLPStreamIndex) {
		this.useSuperLPStreamIdx = useSuperLPStreamIndex;
	}

	@Override
	public String toString() {
		StringBuilder str = new StringBuilder("ServerProvisionImpl [");
		str.append("venueName=" ).append( venueName).append( ", useSuperLPStreamIndex=" ).append( useSuperLPStreamIdx ).append( ", maxCurrencyIndex=" ).append( maxCurrencyIdx)
				.append( ",\n creditMulticastGroup=").append(creditMulticastGroup).append(", creditMulticastPort=").append( creditMulticastPort )
				.append( ",\n rateMulticastgroup=").append( rateMulticastgroup ).append( ", rateMulticastPort=").append( rateMulticastPort )
				.append( ",\n priceBookMulticastPort=").append( priceBookMulticastPort )
				.append( ",\n maxRateProcessorCount=" ).append( maxRateProcessorCount ).append( ", threadIdleTimeInterval=" ).append( threadIdleTimeInterval)
				.append( ",\n liveRateMulticastgroup=" ).append( liveRateMulticastgroup ).append( ", liveRateMulticastPort=" ).append( liveRateMulticastPort )
				.append( ",\n rateMessageSize=" ).append( liveRateMessageSize ).append( "]");
		str.append( ", maxAggregationAttempts=" ).append( getMaxAggregationAttempts() ).append( "]");

				str.append(",\n ccyIndexVsNames={");  
				for (Map.Entry<Integer, String> entry : this.ccyIdxVsNames.entrySet()) {
					str.append("\n");
					str.append(entry.getKey()).append("=").append(entry.getValue());
				}
				
				str.append("},\n orgIndexVsNames=");  
				for (Map.Entry<Integer, String> entry : this.orgIdxVsNames.entrySet()) {
					str.append("\n");
					str.append(entry.getKey()).append("=").append(entry.getValue());
				}
				
				str.append("},\n ccyPairIndexVsNames={");  
				for (Map.Entry<Integer, String> entry : this.ccyPairIdxVsNames.entrySet()) {
					str.append("\n");
					str.append(entry.getKey()).append("=").append(entry.getValue());
				}
			
				str.append("},\n fiProvisions={");  
				for (Entry<Integer, FIProvision> entry : this.fiProvisions.entrySet()) {
					str.append("\n");
					str.append(entry.getKey()).append("=").append(entry.getValue());
				}
				str.append("}");
		return str.toString();
	}

	@Override
	public void logDetails(String key) {
		StringBuilder str = new StringBuilder(key);
		log.info(str.toString() + " start");
		log.info("ServerProvisionImpl [venueName="+venueName+", useSuperLPStreamIndex="+useSuperLPStreamIdx+", maxCurrencyIndex="+maxCurrencyIdx+"]");			
		log.info("ServerProvisionImpl [creditMulticastGroup="+creditMulticastGroup+", creditMulticastPort="+creditMulticastPort +"]");			
		log.info("ServerProvisionImpl [rateMulticastgroup=" + rateMulticastgroup +
				", rateMulticastPort="+  rateMulticastPort +  ", useCcypBasedMCastAddresses="+ useCcypBasedMCastAddresses + "]");
		log.info("ServerProvisionImpl [priceBookMulticastPort="+priceBookMulticastPort +"]");
		log.info("ServerProvisionImpl [meHBMulticastgroup="+meHBMulticastAddress+", meHBMulticastPort="+meHBPort+", meHBInterval="+mEHBInterval+"]");
		log.info("ServerProvisionImpl [liveRateMulticastgroup="+liveRateMulticastgroup.get()+", liveRateMulticastPort="+liveRateMulticastPort +"]");
		log.info("ServerProvisionImpl [maxRateProcessorCount="+maxRateProcessorCount+", threadIdleTimeInterval="+threadIdleTimeInterval+",rateMessageSize="+liveRateMessageSize +"]");
		log.info("ServerProvisionImpl [validateValueDate="+validateValueDate + ", creditDeductPercent="+  creditCutoffPercent + "]");
		log.info("ServerProvisionImpl [multicastTTL="+multicastTTL+", opmode="+opmode+"]");
		log.info("ServerProvisionImpl [aggregationFrequency="+aggregationFrequency+", nodeType="+nodeType+ ", partitionType="+partitionType+ "]");
		log.info("ServerProvisionImpl [maxAggregationAttempts="+maxAggregationAttempts);
		log.info("ServerProvisionImpl [Tenants="+(tenants!=null? Arrays.toString(tenants):"")+"]");


		str.setLength(0);
		str.append(key);
		str.append("ccyIndexVsNames={");
		int count = 0;
		int maxCount = 10;
		for (Map.Entry<Integer, String> entry : this.ccyIdxVsNames.entrySet()) {
			str.append(entry.getKey()).append("=").append(entry.getValue()).append(",");
			count++; 
			if(count ==maxCount){
				str.append("\n");
				count=0;
			}
		}
		str.append("}");
		log.info(str.toString());
		
	}
	
	public void logFIUpdates(String key) {
		StringBuilder str = new StringBuilder(key);
		str.append("{orgIndexVsNames=");
		int count = 0;
		int maxCount = 5;
		for (Map.Entry<Integer, String> entry : this.orgIdxVsNames.entrySet()) {
			str.append(entry.getKey()).append("=").append(entry.getValue()).append(",");
			count++; 
			if(count ==maxCount){
				str.append("\n");
				count=0;
			}
		}
		log.info(str.toString());
		
		str.setLength(0);
		str.append("},\n ccyPairIndexVsNames={");
		count = 0;
		maxCount = 10;
		for (Map.Entry<Integer, String> entry : this.ccyPairIdxVsNames.entrySet()) {
			str.append(entry.getKey()).append("=").append(entry.getValue()).append(",");
			count++; 
			if(count == maxCount){
				str.append("\n");
				count=0;
			}
		}

		log.info(str.toString());		
		str.setLength(0);
		str.append("},\n fiProvisions={");  
		for (Entry<Integer, FIProvision> entry : this.fiProvisions.entrySet()) {
			str.append("\n");
			str.append(entry.getKey()).append("=").append(entry.getValue());
		}
		str.append("}");
		log.info(str.toString());
	}

	@Override
	public int getMEHBPort() {
		return meHBPort;
	}

	public void setMEHBPort(int meHBPort) {
		this.meHBPort = meHBPort;
	}

	@Override
	public Optional<InetAddress> getMEHBMulticastAddress() {
		return meHBMulticastAddress;
	}

	public void setMEHBMulticastGroup(Optional<InetAddress> optional) {
		this.meHBMulticastAddress = optional;
	}

	public void setMEHBInterval(long val) {
		this.mEHBInterval = val;
	}

	@Override
	public long getMEHBInterval() {
		return this.mEHBInterval;
	}

	@Override
	public int getMaxPriceBookDepth() {
		return this.maxPriceBookDepth;
	}
	
	public void setMaxPriceBookDepth(int value) {
		this.maxPriceBookDepth = value;
	}

	public boolean isValidateValueDate() {
		return validateValueDate;
	}

	public void setValidateValueDate(boolean validateValueDate) {
		this.validateValueDate = validateValueDate;
	}

	public void setCreditCutoffPercent(int mvCreditCutOffPercent) {
		this.creditCutoffPercent = mvCreditCutOffPercent;		
	}
	
	public int getCreditCutoffPercent(){
		return this.creditCutoffPercent;
	}

	@Override
	public int getMulticastTTL() {
		return this.multicastTTL;
	}

	public void setMulticastTTL(int multicastTTL) {
		this.multicastTTL = multicastTTL;
	}

	public void setOperationMode(int opmode){
		this.opmode = opmode;
	}

	@Override
    public int getOpetationMode(){
		return this.opmode;
	}

	@Override
    public boolean isClusteredNode(){
		return this.opmode == RemoteProvisionBuilder.OPMODE_CLUSTERED;
	}

	public void setTenants(String[] val){
		this.tenants = val;
	}

	@Override
    public String[] getTenants(){
		return this.tenants;
	}

	public String getAggregationFrequency() {
		return aggregationFrequency;
	}

	public String getNodeType() {
		return nodeType;
	}

	public void setNodeType(String nodeType) {
		this.nodeType = nodeType;
	}

	public void setAggregationFrequency(String aggregationFrequency) {
		this.aggregationFrequency = aggregationFrequency;
	}

	public boolean isAggregationTimeSliced(){
		//TODO convert to enum or use global constants.
		return MDFEntity.AGGREGATION_FREQ_TIME_SLICED.equals(getAggregationFrequency());
	}

	public boolean isOnDemandAggregation(){
		return MDFEntity.NODE_ONDEMAND.equals(getNodeType());
	}

	@Override
	public String getVirtralServer() {
		return virtralServer;
	}

	public void setVirtralServer(String virtralServer) {
		this.virtralServer = virtralServer;
	}

	@Override
	public boolean isRatesFromMatchingEngine(){
		return isRatesFromMatchingEngine;
	}

	@Override
	public void setVenueConfigurations(Map<String, VenueConfiguration> venueConfigurations) {
		this.venueConfigurations = venueConfigurations;
	}

	@Override
	public Optional<InetAddress> getRateMulticastgroup(String ccyp) {
		Optional<VenueConfiguration> venueConfiguration = getVenueConfiguration(ccyp);
		if(venueConfiguration.isPresent()){
			Map<String,String> ccyps = venueConfiguration.get().getCcypMcastGroups();
			String mcast = ccyps.get(ccyp);
			try {
				return Optional.of(InetAddress.getByName(mcast));
			} catch (Exception e) {
				log.warn("SPI.getRateMulticastgroup():Unable to lookup inet address for:"+ mcast + ", for the ccyp=" + ccyp);
			}
		}
		log.info("using venue specific mcast group for ccyp="+ccyp + " group="+getRateMulticastgroup());
		return Optional.empty();
	}




	public Optional<VenueConfiguration> getVenueConfiguration(String currencyPair){
		VenueConfiguration venueConfiguration = this.venueConfigurations.get(currencyPair);
		if(venueConfiguration == null){
			log.warn("unexpected , venue configuration not found for ccyp="+currencyPair);
			return Optional.empty();
		}
		return Optional.of(venueConfiguration);
	}

	public void setRatesFromMatchingEngine(boolean value){
		this.isRatesFromMatchingEngine = value;
	}


	@Override
	public boolean isUseCcypBasedMCastAddresses() {
		return useCcypBasedMCastAddresses;
	}

	@Override
	public void setUseCcypBasedMCastAddresses(boolean useCcypBasedMCastAddresses) {
		this.useCcypBasedMCastAddresses = useCcypBasedMCastAddresses;
	}

	@Override
	public MDFPartitionType getPartitionType() {
		return this.partitionType;
	}

	public void setPartitionType(MDFPartitionType value){
		this.partitionType = value;
	}


	public int getMaxAggregationAttempts() {
		return maxAggregationAttempts;
	}

	public void setMaxAggregationAttempts(int maxAggregationAttempts) {
		this.maxAggregationAttempts = maxAggregationAttempts;
	}

	@Override
	public String getDistributedCacheHeartbeatAddress(){
		return distributedCacheHeartbeatAddress;
	}

	public void setDistributedCacheHeartbeatAddress(String distributedCacheHeartbeatAddress){
		this.distributedCacheHeartbeatAddress = distributedCacheHeartbeatAddress;
	}
}