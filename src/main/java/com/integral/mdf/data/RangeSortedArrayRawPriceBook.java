package com.integral.mdf.data;

import java.math.BigDecimal;
import java.util.Arrays;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.rate.sortedbook.EMSIterator;
import com.integral.mdf.rate.sortedbook.RangeSortedArray;
import com.integral.util.MathUtil;
import com.integral.util.Tuple;

public class RangeSortedArrayRawPriceBook extends PriceBookV2 {

	//TreeMultimap<Double, Double> bids = TreeMultimap.create(new BidPriceComparator(),Ordering.natural());
	//TreeMultimap<Double, Double> offers = TreeMultimap.create(new OfferPriceComparator(),Ordering.natural());
	
	RangeSortedArray<Tuple<Double,Double>> bids = new RangeSortedArray<Tuple<Double,Double>>(100);
	RangeSortedArray<Tuple<Double,Double>> offers = new RangeSortedArray<Tuple<Double,Double>>(100);

	
	static int MAX_TIER = 10;
	Log log = LogFactory.getLog(this.getClass());
    int precision = 5;
    final int multiplier = (int) Math.pow(10, precision + 1);
	
	private StringBuilder builder = new StringBuilder();
	
	public RangeSortedArrayRawPriceBook() {
		this(5,5);
	}

	public RangeSortedArrayRawPriceBook(int maxDepth,int precision) {
		super(maxDepth,FLAG_FULL_BOOK);
		this.precision = precision;
		if( maxDepth > MAX_TIER ){
			throw new IllegalArgumentException("Invalid maxDepth. It can't be more then " + MAX_TIER);
		}
	}

	/**
	 * 
	 */
	public void reset() {
		bids.reset();
		offers.reset();
		
		if(log.isDebugEnabled()){
			builder.setLength(0);
		}
	}
	
	public boolean addPrices(final ProvisionedQuoteC pq) {
		int tier = 0; 
		int maxtiers = pq.getQuoteType() == ProvisionedQuoteC.MULTI_QUOTE ? pq.getPBidTiersNum() : 1;
		while(tier < maxtiers ){
			int offset = pq.pTierOffset(ProvisionedQuoteC.BUY, tier);
			double prc = pq.getPPrice(offset);
			bids.add(getKey(prc), new Tuple<Double,Double>(  prc, pq.getPShowQty(offset, isTermCcyAgg) ) );
			tier++;
		}
		tier = 0;
		maxtiers = pq.getQuoteType() == ProvisionedQuoteC.MULTI_QUOTE ? pq.getPOfferTiersNum() : 1;
		while(tier < maxtiers ){
			int offset = pq.pTierOffset(ProvisionedQuoteC.SELL, tier);
			double prc = pq.getPPrice(offset);
			offers.add(getKey(prc), new Tuple<Double,Double>(  prc, pq.getPShowQty(offset, isTermCcyAgg) ) );
			tier++;
		}
		
		if(bids.getCount() > 0 && offers.getCount() > 0 ){
			if(bids.getMaximum().first >=  offers.getMinimum().first ){
				return true;
			}
		}
		
		if(log.isDebugEnabled() && pq != null){
			builder.append(pq.getProvisionedQuoteString());
			builder.append(" ");
		}
		
		return false;
	}
	

	public PostAggregationAction copyPrices(int precision,int roundingType , double tickValue) {
		
		if( bids.getCount() <0  && offers.getCount() < 0 ){
			return PostAggregationAction.Send;
		}

		int c=0;
		int maxDepth = getMaxDepth();
		
		// START CODE HERE //
		EMSIterator<Tuple<Double,Double>> values = bids.createNewBestPriceIterator(0);
		Tuple<Double,Double> entry = values.getNext();
		while(entry!=null){
			if( c >= maxDepth ){
				break;
			}
			this.getBidPrices()[c] = MathUtil.round(entry.first,precision,BigDecimal.ROUND_FLOOR);
			this.getBidQtys()[c] = MathUtil.round(entry.second,tickValue,roundingType);
			this.getBidNoLP()[c] = 1; // its raw book.
			entry = values.getNext();
			c++;
		}
		// END CODE HERE //
		
		setNumBids(c);
		if( c < maxDepth ){
			Arrays.fill(getBidPrices(), c, maxDepth-1, 0.0d);
			Arrays.fill(getBidQtys(), c, maxDepth-1, 0.0d);
		}

		c=0;
		// START CODE HERE //
		values = offers.createNewWorstPriceIterator(Integer.MAX_VALUE);
		entry = values.getNext();
		while(entry!=null){
			if( c >= maxDepth ){
				break;
			}
			this.getOfferPrices()[c] = MathUtil.round(entry.first,precision,BigDecimal.ROUND_CEILING);
			this.getOfferQtys()[c] = MathUtil.round(entry.second,tickValue,roundingType);
			this.getOfferNoLP()[c] = 1;
			entry = values.getNext();
			c++;
		}
		// END CODE HERE //
		
		
		
		setNumOffers(c);
		if( c < maxDepth ){
			Arrays.fill(getOfferPrices(), c, maxDepth-1, 0.0d);
			Arrays.fill(getOfferQtys(), c, maxDepth-1, 0.0d);
		}

		if(log.isDebugEnabled()){
			builder.append(logRate());
			log.info(builder.toString());
		}
		
		return PostAggregationAction.Send;
	}

	@Override
	public long isInverted() {
		return 0;
	}

	public String logRate() {
		StringBuilder msg = new StringBuilder("RBA ");
		int numBids = getNumBids();
		msg.append(getLogKey()).append(getTimeEffective()).append('|').append(numBids);
		
		for (int i = 0; i < numBids; i++) {
			msg.append('|').append(this.getBidPrices()[i])
			.append('|').append(this.getBidQtys()[i]);
		}
		
		int numOffers = getNumOffers();
		msg.append("|").append(numOffers);
		
		for (int i = 0; i < numOffers; i++) {
			msg.append('|').append(this.getOfferPrices()[i])
			.append('|').append(this.getOfferQtys()[i]);
		}
		
		return msg.toString();
	}
	
	protected int getKey(double rate)
    {
        if (rate < 0.0d) {
            return 0;
        }
        else if (Math.abs(rate - Double.MAX_VALUE) < 0.0001d) {
            return Integer.MAX_VALUE;
        }

        return (int) (Math.round(rate * multiplier) / 10);
    }

}
