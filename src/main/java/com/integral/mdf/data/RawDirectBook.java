package com.integral.mdf.data;

import com.integral.log.Log;
import com.integral.log.LogFactory;

import java.util.Arrays;

public class RawDirectBook extends PriceBookV2 {
    int bidCount = 0;
    int offerCount = 0;
    long quoteTime = 0;
    ProvisionedQuoteC provisionedQuote = null;
    private final StringBuilder builder = new StringBuilder();

    Log log = LogFactory.getLog(this.getClass());

    public RawDirectBook(int maxDepth, long requestId) {
        super(maxDepth, FLAG_RAW_DIRECT_BOOK, 0.0, false, requestId);
    }
    public void reset() {
        bidCount = 0;
        offerCount = 0;
        quoteTime = 0;
        provisionedQuote = null;
        builder.setLength(0);
    }

    @Override
    public boolean addPrices(ProvisionedQuoteC pq) {
        long createdTime = pq.getQuoteCreatedTime(); //TODO: or getReceivedTime()
        if(createdTime > quoteTime){
            quoteTime = createdTime;
            provisionedQuote = pq;
        }
        if(log.isDebugEnabled()) builder.append(pq.getProvisionedQuoteString()).append(" ");
        return false;
    }

    @Override
    public PostAggregationAction copyPrices(int precision, int roundingType, double tickValue) {
        if(provisionedQuote == null) return PostAggregationAction.Drop;
        int maxDepth = getMaxDepth();
        int numOfBidTiers = Math.min(maxDepth, provisionedQuote.getPBidTiersNum());
        for(int tier=0;tier<numOfBidTiers;tier++){
            int offset = provisionedQuote.pTierOffset(ProvisionedQuoteC.BUY, tier);
            double prc = provisionedQuote.getPPrice(offset);
            double d = provisionedQuote.getPShowQty(offset, isTermCcyAgg);
            this.getBidPrices()[tier] = prc;
            this.getBidQtys()[tier] = d;
            this.getBidNoLP()[tier] = provisionedQuote.getLPIndex();
            bidCount++;
        }
        setNumBids(bidCount);
        if( bidCount < maxDepth ){
            Arrays.fill(getBidPrices(), bidCount, maxDepth-1, 0.0d);
            Arrays.fill(getBidQtys(), bidCount, maxDepth-1, 0.0d);
        }

        int numOfOfferTiers = Math.min(maxDepth, provisionedQuote.getPOfferTiersNum());
        for(int tier=0;tier<numOfOfferTiers;tier++){
            int offset = provisionedQuote.pTierOffset(ProvisionedQuoteC.SELL, tier);
            double prc = provisionedQuote.getPPrice(offset);
            double d = provisionedQuote.getPShowQty(offset, isTermCcyAgg);
            this.getOfferPrices()[tier] = prc;
            this.getOfferQtys()[tier] = d;
            this.getOfferNoLP()[tier] = provisionedQuote.getLPIndex();
            offerCount++;
        }
        setNumOffers(offerCount);
        if( offerCount < maxDepth ){
            Arrays.fill(getOfferPrices(), offerCount, maxDepth-1, 0.0d);
            Arrays.fill(getOfferQtys(), offerCount, maxDepth-1, 0.0d);
        }
        if(log.isDebugEnabled()){
            logRate(builder);
            log.info(builder.toString());
        }
        return PostAggregationAction.Send;
    }

    @Override
    public long isInverted() {
        return 0;
    }
    @Override
    public String getName(){
        return "RDA";
    }
}
