package com.integral.mdf.data;

import com.integral.commons.buffers.UnSafeBuffer;

public class TVMarketStatus {

  public final static short MSG_TYPE = 0;
  public final static byte VERSION = 0;

  public final static int VENUE_TYPE_REX = 0;
  public final static int VENUE_TYPE_CLOB = 1;  

  
  private final int venueType;
  
  private int venueCode;

  private int ccyPairIndex;

  private long nextMatchingTime;

  private MarketStatus marketStatus;

  public TVMarketStatus(int venueType) {
    this.venueType = venueType;
  }

  public int getVenueCode() {
    return venueCode;
  }

  public void setVenueCode(int venueCode) {
    this.venueCode = venueCode;
  }

  public int getCcyPairIndex() {
    return ccyPairIndex;
  }

  public void setCcyPairIndex(int ccyPairIndex) {
    this.ccyPairIndex = ccyPairIndex;
  }

  public long getNextMatchingTime() {
    return nextMatchingTime;
  }

  public void setNextMatchingTime(long nextMatchingTime) {
    this.nextMatchingTime = nextMatchingTime;
  }

  public MarketStatus getMarketStatus() {
    return marketStatus;
  }

  public void setMarketStatus(MarketStatus marketStatus) {
    this.marketStatus = marketStatus;
  }

  public void writeTo(UnSafeBuffer safeBuf) {
    switch (venueType) {
    case VENUE_TYPE_CLOB:
      safeBuf.putInt(this.venueCode);
      safeBuf.putInt(this.ccyPairIndex);
      safeBuf.put(this.marketStatus.getValue());
      break;
    case VENUE_TYPE_REX:
      safeBuf.putInt(this.venueCode);
      safeBuf.putInt(this.ccyPairIndex);
      safeBuf.put(this.marketStatus.getValue());
      safeBuf.putLong(this.getNextMatchingTime());
      break;
    }
  }

  public void readFrom(UnSafeBuffer safeBuf) {
    switch (venueType) {
    case VENUE_TYPE_CLOB:
      this.venueCode = safeBuf.getInt();
      this.ccyPairIndex = safeBuf.getInt();
      this.marketStatus = MarketStatus.getMarketStatus(safeBuf.get());
      break;
    case VENUE_TYPE_REX
    :
      this.venueCode = safeBuf.getInt();
      this.ccyPairIndex = safeBuf.getInt();
      this.marketStatus = MarketStatus.getMarketStatus(safeBuf.get());
      this.nextMatchingTime = safeBuf.getLong();
      break;
    }

  }

  @Override
  public String toString() {
    return "RiskNetInfo [venueCode=" + venueCode + ", ccyPairIndex=" + ccyPairIndex + ", nextMatchingTime=" + nextMatchingTime
        + ", marketStatus=" + marketStatus + "]";
  }

  public int getEstimatedSize() {
    return 32;
  }
}