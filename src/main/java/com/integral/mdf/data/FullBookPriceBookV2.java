package com.integral.mdf.data;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Map;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.rate.treeset.*;
import com.integral.util.MathUtil;
import com.integral.util.Tuple;

public class FullBookPriceBookV2 extends PriceBookV2 {

	MergeOnConflictTreeMap<Double, PriceTier> bids = new MergeOnConflictTreeMap<>(new BidPriceComparator(),new PriceTierMergeHandler());
	MergeOnConflictTreeMap<Double, PriceTier> offers = new MergeOnConflictTreeMap<>(new OfferPriceComparator(),new PriceTierMergeHandler());
	
	static int MAX_TIER = 50;
	Log log = LogFactory.getLog(this.getClass());
	
	private StringBuilder builder = new StringBuilder();
	
	public FullBookPriceBookV2() {
		this(MAX_TIER, 0.0d, false, 0);
	}

	public FullBookPriceBookV2(int maxDepth, double requestedSize, boolean isTermCcyAgg, long requestId) {
		super(maxDepth,FLAG_FULL_BOOK, requestedSize, isTermCcyAgg, requestId);
		if( maxDepth > MAX_TIER ){
			throw new IllegalArgumentException("Invalid maxDepth. It can't be more then " + MAX_TIER);
		}
	}

	/**
	 * 
	 */
	public void reset() {
		bids.clear();
		offers.clear();
		
		if(log.isDebugEnabled()){
			builder.setLength(0);
		}
	}
	
	public boolean addPrices(final ProvisionedQuoteC pq) {
		int tier = 0;
		double requestedSize = pq.getQuoteType() == ProvisionedQuoteC.MULTI_TIER ? this.requestedSize  : 0.0d;
		boolean isMQ = pq.getQuoteType() == ProvisionedQuoteC.MULTI_QUOTE;
		while(tier < pq.getPBidTiersNum() ){
			int offset = pq.pTierOffset(ProvisionedQuoteC.BUY, tier);
			double d = pq.getPShowQty(offset, isTermCcyAgg);
            if(d >= requestedSize && d > 0.0d) {
                bids.put(pq.getPPrice(offset), new PriceTier(pq.getPPrice(offset),pq.getPShowQty(offset, isTermCcyAgg),
						1, pq.getStreamIdx(),pq.getReceivedTime().toEpochMilli()));
				if(!isMQ) break;
            }
            tier++;
		}
		tier = 0;
		while(tier < pq.getPOfferTiersNum() ){
			int offset = pq.pTierOffset(ProvisionedQuoteC.SELL, tier);
			double d = pq.getPShowQty(offset, isTermCcyAgg);
			if(d >= requestedSize && d > 0.0d) {
                offers.put(pq.getPPrice(offset), new PriceTier(pq.getPPrice(offset),pq.getPShowQty(offset, isTermCcyAgg),1, pq.getStreamIdx(),pq.getReceivedTime().toEpochMilli()));
				if(!isMQ) break;
            }
            tier++;
		}
		
		if(!bids.isEmpty() && !offers.isEmpty()){
			if(bids.firstKey() >  offers.firstKey() ){
				return true;
			}
		}
		
		//we added some prices successfully. send latest
		if( getValueDate() < pq.getValueDate()){
			setValueDate(pq.getValueDate());
		}
		
		
		if(log.isDebugEnabled()){
			builder.append(pq.getProvisionedQuoteString());
			builder.append(" ");
		}
		
		return false;
	}
	

	public PostAggregationAction copyPrices(int precision,int roundingType , double tickValue) {
		
		if(bids.isEmpty() && offers.isEmpty()){
			//send zero price book. MDG treat it is as 
			setNumBids(1);
			this.getBidPrices()[0]=0.0d;
			this.getBidQtys()[0]=0.0d;
			this.getBidNoLP()[0]=0;
			
			setNumOffers(1);
			this.getOfferPrices()[0]=0.0d;
			this.getOfferQtys()[0]=0.0d;
			this.getOfferNoLP()[0]=0;
			
			if(log.isDebugEnabled()){
				builder.append(logRate());
				log.info(builder.toString());
			}
		
			return PostAggregationAction.Send;
		}

		Map.Entry<Double,PriceTier> entry= bids.pollFirstEntry();
		int c=0;
		int maxDepth = getMaxDepth();
		while(entry!=null && c < maxDepth ){
			this.getBidPrices()[c] = MathUtil.round(entry.getKey(),precision,BigDecimal.ROUND_FLOOR);
			this.getBidQtys()[c] = MathUtil.round(entry.getValue().qty,tickValue,roundingType);
			this.getBidNoLP()[c] = entry.getValue().lpidx;
			c++;
			entry= bids.pollFirstEntry();
		}
		
		setNumBids(c);
		
		if( c < maxDepth ){
			Arrays.fill(getBidPrices(), c, maxDepth-1, 0.0d);
			Arrays.fill(getBidQtys(), c, maxDepth-1, 0.0d);
		}

		entry= offers.pollFirstEntry();
		c=0;
		while(entry!=null && c < maxDepth){
			this.getOfferPrices()[c] = MathUtil.round(entry.getKey(),precision,BigDecimal.ROUND_CEILING);
			this.getOfferQtys()[c] = MathUtil.round(entry.getValue().qty,tickValue,roundingType);
			this.getOfferNoLP()[c] = entry.getValue().lpidx;
			c++;
			entry= offers.pollFirstEntry();
		}
		
		setNumOffers(c);
		if( c < maxDepth ){
			Arrays.fill(getOfferPrices(), c, maxDepth-1, 0.0d);
			Arrays.fill(getOfferQtys(), c, maxDepth-1, 0.0d);
		}

		if(log.isDebugEnabled()){
			builder.append(logRate());
			log.info(builder.toString());
		}
		
		return PostAggregationAction.Send;
	}

	@Override
	public long isInverted() {
		if(!bids.isEmpty() && !offers.isEmpty()){
			if(bids.firstKey() >  offers.firstKey() ){
				PriceTier bid = bids.firstEntry().getValue();
				PriceTier offer = offers.firstEntry().getValue();
				long streamidx =  bid.effectiveTime < offer.effectiveTime?bid.streamidx:offer.streamidx;
//				log.info("bid stream id= " + bid.streamidx + ", time="+bid.effectiveTime);
//				log.info("offer stream id= " + offer.streamidx + ", time="+offer.effectiveTime);
//				log.info("offending stream = " + streamidx);
				return bid.effectiveTime < offer.effectiveTime?bid.streamidx:offer.streamidx;
			}
		}
		return 0;
	}

	public String logRate() {
		StringBuilder msg = new StringBuilder("FBA ");
		msg.append(getLogKey()).append(getTimeEffective()).append('|');
		
		msg.append(getValueDate()).append('|');
		
		int numBids = getNumBids();
		msg.append(numBids);
		for (int i = 0; i < numBids; i++) {
			msg.append('|').append(this.getBidPrices()[i])
			.append('|').append(this.getBidQtys()[i]);
		}
		
		int numOffers = getNumOffers();
		msg.append("|").append(numOffers);
		
		for (int i = 0; i < numOffers; i++) {
			msg.append('|').append(this.getOfferPrices()[i])
			.append('|').append(this.getOfferQtys()[i]);
		}

		msg.append('#');
		msg.append(this.getBookId());
		msg.append('|').append(getQuoteId());
		msg.append('|').append(getQuoteTimeEffective());
		msg.append('|').append(getOldestQId());
		msg.append("|").append(hashCode());

		return msg.toString();
	}

}

