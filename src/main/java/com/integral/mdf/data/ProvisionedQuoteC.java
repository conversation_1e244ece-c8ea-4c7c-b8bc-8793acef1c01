package com.integral.mdf.data;

import java.time.Instant;
import java.util.Arrays;
import java.util.concurrent.atomic.AtomicBoolean;

import org.agrona.concurrent.UnsafeBuffer;

public class ProvisionedQuoteC {

	public static final  int FULL_AMOUNT_FLAG =1;
	private static final int SIZE_OF_BYTE = 1;
	private static final int SIZE_OF_INT = 4;
	private static final int SIZE_OF_LONG = 8;
	private static final int SIZE_OF_SHORT = 2;
	private static final int SIZE_OF_DOUBLE = 8;
	public static final byte BUY = 0;
	public static final byte SELL = 1;
	public static final byte MULTI_QUOTE = 0;
	public static final byte MULTI_TIER = 1;
	public static final int MAX_DEPTH = 5;

	private static final int MSG_TYPE = 0;
	private static final int VERSION = SIZE_OF_BYTE;
	private static final int VENUE_IDX = VERSION + SIZE_OF_BYTE;
	private static final int STREAM_IDX = VENUE_IDX + SIZE_OF_INT;
	private static final int CCY_PAIR_IDX = STREAM_IDX + SIZE_OF_INT;
	private static final int QUOTE_ID = CCY_PAIR_IDX + SIZE_OF_INT;
	private static final int SESSION_ID = QUOTE_ID + SIZE_OF_LONG; 
	private static final int PROVIDER_IDX = SESSION_ID + SIZE_OF_INT;
	private static final int QUOTE_TYPE = PROVIDER_IDX + SIZE_OF_INT;
	private static final int QUOTE_MIN_QTY = QUOTE_TYPE + SIZE_OF_BYTE;
	private static final int QUOTE_CATEGORY = QUOTE_MIN_QTY + SIZE_OF_DOUBLE;
	private static final int VALUE_DATE = QUOTE_CATEGORY + SIZE_OF_INT;
	private static final int LEGAL_ENTITYID = VALUE_DATE + SIZE_OF_SHORT;
	private static final int BID_TIERS_NUM = LEGAL_ENTITYID + SIZE_OF_INT;
	private static final int OFFER_TIERS_NUM = BID_TIERS_NUM + SIZE_OF_INT;
	private static final int BID_TIERS = OFFER_TIERS_NUM + SIZE_OF_INT;

	private static final int TIER_PRICE_OFFSET = 0;
	private static final int TIER_SHOW_QTY_OFFSET = TIER_PRICE_OFFSET
			+ SIZE_OF_DOUBLE;
	private static final int TIER_TOTAL_QTY_OFFSET = TIER_SHOW_QTY_OFFSET
			+ SIZE_OF_DOUBLE;
	private static final int TIER_CNT_OFFSET = TIER_TOTAL_QTY_OFFSET
			+ SIZE_OF_DOUBLE;
	private static final int TIER_SIZE = TIER_CNT_OFFSET + SIZE_OF_INT;

    private static int QUOTE_CREATED_TIME = getQuoteCreatedTimeOffset(7) ;
	private static final int FLAGS = QUOTE_CREATED_TIME + SIZE_OF_LONG;
	private static int LENGTH = FLAGS + SIZE_OF_INT;

	private final UnsafeBuffer buffer;
	
	private static final int MAX_LENGTH = 1024;

	private volatile boolean isActive = false;
	
	private Instant receivedTime;
	
	private String lKey;
	private int lPIndex = -1; 
	
	public ProvisionedQuoteC() {
		this.buffer = new UnsafeBuffer(new byte[MAX_LENGTH]);
		//this.buffer.putByte(MSG_TYPE,MsgType.QUOTE);
		this.buffer.putByte(MSG_TYPE,(byte)24);
		this.buffer.putDouble(QUOTE_MIN_QTY, 0.0);
	}

	public void resetBuffer(){
		Arrays.fill(this.buffer.byteArray(),0,MAX_LENGTH-1,(byte)0);
		this.buffer.putByte(MSG_TYPE,(byte)24);
		this.buffer.putDouble(QUOTE_MIN_QTY, 0.0);
	}


	public static int size(final int depth) {
		return BID_TIERS + (TIER_SIZE * depth * 2);
	}
	
	public int getVersion() {
		return this.buffer.getByte(VERSION);
	}

	public void setVersion(final byte version) {
		this.buffer.putByte(VERSION, version);
	}
	
	public int getMessageType() {
		return this.buffer.getByte(MSG_TYPE);
	}

	public int getVenueIdx() {
		return this.buffer.getInt(VENUE_IDX);
	}

	public void setVenueIdx(final int venueIdx) {
		this.buffer.putInt(VENUE_IDX, venueIdx);
	}
	
	public short getValueDate() {
		return this.buffer.getShort(VALUE_DATE);
	}

	public void setValueDate(final short valueDate) {
		this.buffer.putShort(VALUE_DATE, valueDate);
	}
	
	public int getProviderIdx() {
		return this.buffer.getInt(PROVIDER_IDX);
	}

	public void setProviderIdx(final int providerIdx) {
		this.buffer.putInt(PROVIDER_IDX, providerIdx);
	}
	
	public int getLegalEntityID() {
		return this.buffer.getInt(LEGAL_ENTITYID);
	}

	public void setLegalEntityID(final int legalEntityID) {
		this.buffer.putInt(LEGAL_ENTITYID, legalEntityID);
	}

	public void setStreamIdx(final int streamIdx) {
		this.buffer.putInt(STREAM_IDX, streamIdx);
	}

	public int getStreamIdx() {
		return this.buffer.getInt(STREAM_IDX);
	}

	public byte getQuoteType() {
		return this.buffer.getByte(QUOTE_TYPE);
	}

	public void setQuoteType(byte aType) {
		this.buffer.putByte(QUOTE_TYPE, aType);
	}

	public int getCategory() {
		return this.buffer.getInt(QUOTE_CATEGORY);
	}

	public void setCategory(final int category) {
		this.buffer.putInt(QUOTE_CATEGORY, category);
	}

	public int getCcyPairIdx() {
		return this.buffer.getInt(CCY_PAIR_IDX);
	}

	public void setCcyPairIdx(final int ccyPairIdx) {
		this.buffer.putInt(CCY_PAIR_IDX, ccyPairIdx);
	}

	public long getQuoteId() {
		return this.buffer.getLong(QUOTE_ID);
	}

	public void setQuoteId(final long quoteId) {
		this.buffer.putLong(QUOTE_ID, quoteId);
	}
	
	public int getSessionId() {
		return this.buffer.getInt(SESSION_ID);
	}

	public void setSessionId(final int sessionId) {
		this.buffer.putInt(SESSION_ID, sessionId);
	}

	public final int bidTiersOffset() {
		return BID_TIERS;
	}

	public final int offerTiersOffset() {
		return bidTiersOffset() + getBidTiersNum() * TIER_SIZE;
	}

	public void setBidTiersNum(int bidNum) {
		this.buffer.putInt(BID_TIERS_NUM, bidNum);
	}

	public void setOfferTiersNum(int offerNum) {
		this.buffer.putInt(OFFER_TIERS_NUM, offerNum);
	}

	public int getBidTiersNum() {
		return this.buffer.getInt(BID_TIERS_NUM);
	}

	public int getOfferTiersNum() {
		return this.buffer.getInt(OFFER_TIERS_NUM);
	}

	public int tierOffset(final byte side, final int tierNo) {
		return (side == BUY ? bidTiersOffset() : offerTiersOffset()) + tierNo
				* TIER_SIZE;
	}

	public double getPrice(final int tierOffset) {
		return this.buffer.getDouble(tierOffset + TIER_PRICE_OFFSET);
	}

	public void setPrice(final int tierOffset, final double price) {
		this.buffer.putDouble(tierOffset + TIER_PRICE_OFFSET, price);
	}

	public void setShowQty(final int tierOffset, final double showQty) {
		this.buffer.putDouble(tierOffset + TIER_SHOW_QTY_OFFSET, showQty);
	}

	public double getShowQty(final int tierOffset) {
		return this.buffer.getDouble(tierOffset + TIER_SHOW_QTY_OFFSET);
	}

	public void setTotalQty(final int tierOffset, final double totalQty) {
		this.buffer.putDouble(tierOffset + TIER_TOTAL_QTY_OFFSET, totalQty);
	}

	public double getTotalQty(final int tierOffset) {
		return this.buffer.getDouble(tierOffset + TIER_TOTAL_QTY_OFFSET);
	}

	public void setCnt(final int tierOffset, final int cnt) {
		this.buffer.putInt(tierOffset + TIER_CNT_OFFSET, cnt);
	}

	public int getCnt(final int tierOffset) {
		return this.buffer.getInt(tierOffset + TIER_CNT_OFFSET);
	}

	public UnsafeBuffer buffer() {
		return buffer;
	}
	
//	public final int pOffset() {
//		return bidTiersOffset() + getBidTiersNum() * TIER_SIZE + getOfferTiersNum() * TIER_SIZE;
//	}

    public final int pOffset() {
        return 512; //size of quoteC.
    }
	
	public void setPBidTiersNum(int pBidNum) {
		this.buffer.putInt(pOffset(), pBidNum);
	}

	public void setPOfferTiersNum(int pOfferNum) {
		this.buffer.putInt(pOffset()+SIZE_OF_INT, pOfferNum);
	}
	
	public int getPBidTiersNum() {
		return this.buffer.getInt(pOffset());
	}

	public int getPOfferTiersNum() {
		return this.buffer.getInt(pOffset()+SIZE_OF_INT);
	}
	
	private int pBidTiersOffset() {
		return pOffset()+ 2 * SIZE_OF_INT;
	}
	
	private int pOfferTiersOffset() {
		return pBidTiersOffset() + getPBidTiersNum() * TIER_SIZE;
	}
	
	public int pTierOffset(final byte side, final int tierNo) {
		return (side == BUY ? pBidTiersOffset() : pOfferTiersOffset()) + tierNo * TIER_SIZE;
	}
	
	public void copyTierToProvisionTier(final byte side,int i) {
		int bTierOffset = tierOffset(side,i);		
		int pBTierOffset = pTierOffset(side,i);		
		buffer.getBytes(bTierOffset, buffer, pBTierOffset, TIER_SIZE);
	}
	
	public void copyAllTiersToProvisionTiers() {
		
		int noOfBidTiers = getBidTiersNum();
		int noOfOfferTiers = getOfferTiersNum();
		setPBidTiersNum(noOfBidTiers);
		setPOfferTiersNum(noOfOfferTiers);
		
		int bTierOffset = tierOffset(BUY,0);
		int pBTierOffset = pTierOffset(BUY,0);
		
		int bytesToCopy = noOfBidTiers * TIER_SIZE + noOfOfferTiers * TIER_SIZE;
		
		buffer.getBytes(bTierOffset, buffer, pBTierOffset , bytesToCopy);
	}

	public double getPPrice(final int pTierOffset) {
		return this.buffer.getDouble(pTierOffset + TIER_PRICE_OFFSET);
	}

	public void setPPrice(final int pTierOffset, final double price) {
		this.buffer.putDouble(pTierOffset + TIER_PRICE_OFFSET, price);
	}

	public void setPShowQty(final int pTierOffset, final double showQty) {
		this.buffer.putDouble(pTierOffset + TIER_SHOW_QTY_OFFSET, showQty);
	}

	public double getPShowQty(final int pTierOffset) {
		return this.buffer.getDouble(pTierOffset + TIER_SHOW_QTY_OFFSET);
	}
	public double getPShowQty(final int pTierOffset, boolean isTerm) {
		double qtyInBase = getPShowQty(pTierOffset);
		return isTerm ? qtyInBase * getPPrice(pTierOffset) : qtyInBase;
	}

	public void setPTotalQty(final int pTierOffset, final double totalQty) {
		this.buffer.putDouble(pTierOffset + TIER_TOTAL_QTY_OFFSET, totalQty);
	}

	public double getPTotalQty(final int pTierOffset) {
		return this.buffer.getDouble(pTierOffset + TIER_TOTAL_QTY_OFFSET);
	}

	public void setPCnt(final int pTierOffset, final int cnt) {
		this.buffer.putInt(pTierOffset + TIER_CNT_OFFSET, cnt);
	}

	public int getPCnt(final int pTierOffset) {
		return this.buffer.getInt(pTierOffset + TIER_CNT_OFFSET);
	}
	

	@Override
	public String toString() {
		int bidNum = getBidTiersNum();
		int offerNum = getOfferTiersNum();

		int pBidNum = getPBidTiersNum();
		int pOfferNum = getPOfferTiersNum();
		
		String marketData = "ProvisionedQuoteC [hashcode="+ hashCode() + "getVersion()=" + getVersion() + ", getMessageType()="
				+ getMessageType() + ", getVenueIdx()=" + getVenueIdx()
				+ ", getProviderIdx()=" + getProviderIdx()
				+ ", getStreamIdx()=" + getStreamIdx() + ", getQuoteType()="
				+ getQuoteType() + ", getCategory()=" + getCategory()
				+ ", getCcyPairIdx()=" + getCcyPairIdx() + ", getQuoteId()="
				+ getQuoteId() + ", getSessionId()=" + getSessionId() +",valuedate=" + getValueDate()
				+ ", bidTiersOffset()=" + bidTiersOffset()
				+ ", offerTiersOffset()=" + offerTiersOffset()
				+ ", getBidTiersNum()=" + bidNum
				+ ", getOfferTiersNum()=" + offerNum 
				+ ", getPBidTiersNum()=" + pBidNum
				+ ", getPOfferTiersNum()=" + pOfferNum
				+ ", isFAStream()=" + isFullAmountStream()
				+ "]";

		StringBuilder data = new StringBuilder(100);
		data.append(marketData);
		data.append("[ bids=");
		for (int i = 0; i < bidNum; i++) {
			int tierOffset = tierOffset(BUY, i);
			data.append("{").append("price=").append(getPrice(tierOffset))
					.append(",").append("totalQty=")
					.append(getTotalQty(tierOffset)).append(",")
					.append("showQty=").append(getShowQty(tierOffset))
					.append(",").append("cnt=").append(getCnt(tierOffset))
					.append("}");
		}
		data.append("]");
		data.append("[ offers=");
		for (int i = 0; i < offerNum; i++) {
			int tierOffset = tierOffset(SELL, i);
			data.append("{").append("price=").append(getPrice(tierOffset))
					.append(",").append("totalQty=")
					.append(getTotalQty(tierOffset)).append(",")
					.append("showQty=").append(getShowQty(tierOffset))
					.append(",").append("cnt=").append(getCnt(tierOffset))
					.append("}");
		}
		data.append("]");
		
		data.append("[ pbids=");
		for (int i = 0; i < pBidNum; i++) {
			int tierOffset = pTierOffset(BUY, i);
			data.append("{").append("price=").append(getPPrice(tierOffset))
					.append(",").append("totalQty=")
					.append(getPTotalQty(tierOffset)).append(",")
					.append("showQty=").append(getPShowQty(tierOffset))
					.append(",").append("cnt=").append(getCnt(tierOffset))
					.append(",").append("offset=").append(tierOffset)
					.append("}");
		}
		data.append("]");
		data.append("[ poffers=");
		for (int i = 0; i < pOfferNum; i++) {
			int tierOffset = pTierOffset(SELL, i);
			data.append("{").append("price=").append(getPPrice(tierOffset))
					.append(",").append("totalQty=")
					.append(getPTotalQty(tierOffset)).append(",")
					.append("showQty=").append(getPShowQty(tierOffset))
					.append(",").append("cnt=").append(getCnt(tierOffset))
					.append(",").append("offset=").append(tierOffset)
					.append("}");
		}
		data.append("]");
		
		return data.toString();
	}

	public int getEstimatedSize() {
		return BID_TIERS + (TIER_SIZE * getBidTiersNum() + TIER_SIZE * getOfferTiersNum());
	}

	public void readFrom(UnsafeBuffer incoming) {
		//We use array as underlying store not byte buffer 
		incoming.getBytes(0, buffer.byteArray(), 0,incoming.capacity());
	}
	
	public void readFrom(QuoteC rate) {
		//We use array as underlying store not byte buffer 
		readFrom(rate.buffer());
		this.receivedTime = rate.getReceivedTime();
	}

	public void writeTo(UnsafeBuffer outgoing) {
		buffer.getBytes(0, outgoing.byteBuffer(), buffer.capacity());
	}
	
	
	public boolean isMultiTier(){
		return (ProvisionedQuoteC.MULTI_TIER == getQuoteType());
	}
	
	public boolean isMultiQuote(){
		return (ProvisionedQuoteC.MULTI_QUOTE == getQuoteType());
	}
	

	public static void main(String[] args) {
		System.out.println(ProvisionedQuoteC.size(5));
	}

	public boolean isActive() {
		return isActive;
	}

	public void setActive(boolean value) {
		this.isActive = value ;
	}

	public Instant getReceivedTime() {
		return receivedTime;
	}

//	public void setReceivedTime(long receivedTime) {
//		this.receivedTime = receivedTime;
//	}

	public void setKey(String logKey) {
		lKey = logKey;
	}
	
	public String getKey() {
		return lKey;
	}
	
	public String getProvisionedQuoteString(){
		int pBidNum = getPBidTiersNum();
		int pOfferNum = getPOfferTiersNum();
		
		StringBuilder msg = new StringBuilder();
		msg.append(lKey)
		.append(getValueDate()).append('|')		
		.append(getQuoteType()).append('|')
		.append(isFullAmountStream()).append('|')
		.append(getQuoteId()).append('|').append(pBidNum);
		
		for (int i = 0; i < pBidNum; i++) {
			int tierOffset = pTierOffset(ProvisionedQuoteC.BUY, i);
			msg.append('|').append(getPPrice(tierOffset))
			.append('|').append(getPTotalQty(tierOffset))
			.append('|').append(getPShowQty(tierOffset));
		}
		
		msg.append("|").append(pOfferNum);
		
		for (int i = 0; i < pOfferNum; i++) {
			int tierOffset = pTierOffset(ProvisionedQuoteC.SELL, i);
			msg.append('|').append(getPPrice(tierOffset))
			.append('|').append(getPTotalQty(tierOffset))
			.append('|').append(getPShowQty(tierOffset));
		}
		
		return msg.toString();
	}

	public void setLPIndex(int lpIdx) {
		this.lPIndex = lpIdx;
	}
	
	public int getLPIndex(){
		return this.lPIndex;
	}

    public static int getQuoteCreatedTimeOffset(final int depth) {
        return BID_TIERS + (TIER_SIZE * depth * 2);
    }

    public long getQuoteCreatedTime() {
        return this.buffer().getLong(QUOTE_CREATED_TIME);
    }

	public void setFullAmountStream(boolean isFullAmountStream){
		if(isFullAmountStream){
			enableFlag(FULL_AMOUNT_FLAG);
		}else{
			disableFlag(FULL_AMOUNT_FLAG);
		}
	}

	public boolean isFullAmountStream(){
		return getVersion() >= 2 && isFlagEnabled(FULL_AMOUNT_FLAG);
	}
	private void enableFlag( int flag ) {
		this.buffer().putInt(FLAGS,this.buffer().getInt(FLAGS)  | flag);
	}

	private void disableFlag( int flag ) {
		this.buffer().putInt(FLAGS,this.buffer().getInt(FLAGS)  & ~flag);
	}

	private boolean isFlagEnabled( int flag ) {
		return ( this.buffer().getInt(FLAGS)  & flag ) == flag;
	}
	private int getFlagValue() {
		return  this.buffer().getInt(FLAGS)  ;
	}
}
