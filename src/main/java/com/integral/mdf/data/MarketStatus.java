package com.integral.mdf.data;

public enum MarketStatus {

  PRE_OPEN((byte) 0), OPEN((byte) 1), PAUSE_MATCHING((byte) 2), PAUSE((byte) 3), CLOSED((byte) 4);

  private final byte value;

  private MarketStatus(byte value) { 
    this.value = value;
  }

  public static MarketStatus getMarketStatus(byte status) {
    switch (status) {
    case 0:
      return PRE_OPEN;
    case 1:
      return OPEN;
    case 2:
      return PAUSE_MATCHING;
    case 3:
      return PAUSE;
    case 4:
      return CLOSED;
    default:
      return null;
    }
  }

  public byte getValue() {
    return value;
  }

  public boolean isNewOrderAllowed() {
    return this == OPEN || this == PRE_OPEN || this == PAUSE_MATCHING;
  }

  public boolean isCancelAllowed() {
    return this == OPEN || this == PRE_OPEN || this == PAUSE_MATCHING;
  }

  public boolean isOrderStatusQueryAllowed() {
    return this == OPEN || this == PRE_OPEN || this == PAUSE_MATCHING;
  }

  public boolean isMatchingAllowed() {
    return this == OPEN || this == CLOSED;
  }

}
