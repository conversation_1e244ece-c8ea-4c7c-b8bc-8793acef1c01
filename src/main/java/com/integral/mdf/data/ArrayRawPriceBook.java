package com.integral.mdf.data;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Comparator;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.util.MathUtil;

/** 
 * This class is not Thread Safe.
 * <AUTHOR>
 *
 */
public class ArrayRawPriceBook extends PriceBookV2 {

	PriceTier[] bids = new PriceTier[1000];
	int bidCount = 0;
	PriceTier maxbid = new PriceTier(0.0d,0.0d,-1,0,0);
	PriceTier[] offers = new PriceTier[1000];
	int offerCount = 0;
	PriceTier lowestoffer = new PriceTier(Double.MAX_VALUE,0.0d,-1,0,0);
	static int MAX_TIER = 50;
	Log log = LogFactory.getLog(this.getClass());
	
	private StringBuilder builder = new StringBuilder();
	
	public ArrayRawPriceBook() {
		this(MAX_TIER, false, 0);
	}

	public ArrayRawPriceBook(int maxDepth, boolean isTermCcyAgg, long requestId) {
		super(maxDepth,FLAG_RAW_BOOK, 0.0, isTermCcyAgg, requestId);
		if( maxDepth > MAX_TIER ){
			throw new IllegalArgumentException("Invalid maxDepth. It can't be more then " + MAX_TIER);
		}
		//init PriceTiers
		for(int i=0;i<1000;i++){
			bids[i] = new PriceTier();
			offers[i] = new PriceTier();
		}
	}

	/**
	 * 
	 */
	public void reset() {
		bidCount = 0;
		offerCount = 0;
		maxbid.prc = 0.0d;maxbid.streamidx = 0;maxbid.effectiveTime = 0;
		lowestoffer.prc = Double.MAX_VALUE;lowestoffer.streamidx = 0;lowestoffer.effectiveTime = 0;
		if(log.isDebugEnabled()){
			builder.setLength(0);
		}
	}
	
	/**
	 * @return isInverted
	 */
	public boolean addPrices(final ProvisionedQuoteC pq) {
		int tier = 0; 
		int maxtiers = pq.getQuoteType() == ProvisionedQuoteC.MULTI_QUOTE ? pq.getPBidTiersNum() : Math.min(pq.getPBidTiersNum(),1);
        //int maxtiers = pq.getQuoteType() == ProvisionedQuoteC.MULTI_QUOTE ? pq.getPBidTiersNum() : 1;
		while(tier < maxtiers ){
			int offset = pq.pTierOffset(ProvisionedQuoteC.BUY, tier);
			double prc = pq.getPPrice(offset);
			double d = pq.getPShowQty(offset, isTermCcyAgg);
			if(d>0.0) {
				PriceTier ptier = bids[bidCount++];
				ptier.lpidx = pq.getLPIndex();
				ptier.prc = prc;
				ptier.qty = d;
				if (prc > maxbid.prc) {
					maxbid.prc = prc;
					maxbid.streamidx = pq.getStreamIdx();
					maxbid.effectiveTime = pq.getReceivedTime().toEpochMilli();
				}
			}
			tier++;
		}
		tier = 0;
		maxtiers = pq.getQuoteType() == ProvisionedQuoteC.MULTI_QUOTE ? pq.getPOfferTiersNum() : Math.min(pq.getPOfferTiersNum(),1);
        //maxtiers = pq.getQuoteType() == ProvisionedQuoteC.MULTI_QUOTE ? pq.getPOfferTiersNum() : 1;
		while(tier < maxtiers ){
			int offset = pq.pTierOffset(ProvisionedQuoteC.SELL, tier);
			double prc = pq.getPPrice(offset);
			double d = pq.getPShowQty(offset, isTermCcyAgg);
			if (d > 0.0) {
				PriceTier ptier = offers[offerCount++];
				ptier.lpidx = pq.getLPIndex();
				ptier.prc = prc;
				ptier.qty = d;
				if (prc < lowestoffer.prc) { //better offer
					lowestoffer.prc = prc;
					lowestoffer.streamidx = pq.getStreamIdx();
					lowestoffer.effectiveTime = pq.getReceivedTime().toEpochMilli();
				}
			}
			tier++;
		}
		
/*
		if(bidCount > 0 && offerCount > 0 ){
			if(maxbid > lowestoffer ){
				return true;
			}
		}
*/

		//we added some prices successfully. send latest
		if( getValueDate() < pq.getValueDate()){
			setValueDate(pq.getValueDate());
		}
		
		if(log.isDebugEnabled()){
			builder.append(pq.getProvisionedQuoteString());
			builder.append(" ");
		}
		
		return false;
	}

	@Override
	public long isInverted() {
		if(bidCount > 0 && offerCount > 0 ){
			if(maxbid.prc > lowestoffer.prc ){
				return maxbid.effectiveTime < lowestoffer.effectiveTime ? maxbid.streamidx : lowestoffer.streamidx;
			}
		}
		return 0;
	}

	public PostAggregationAction copyPrices(int precision,int roundingType , double tickValue) {
		
		if(bidCount <= 0 && offerCount <= 0 ){
			//send zero price book. MDG treat it is as 
			setNumBids(1);
			this.getBidPrices()[0]=0.0d;
			this.getBidQtys()[0]=0.0d;
			this.getBidNoLP()[0]=0;
			
			setNumOffers(1);
			this.getOfferPrices()[0]=0.0d;
			this.getOfferQtys()[0]=0.0d;
			this.getOfferNoLP()[0]=0;
			
			if(log.isDebugEnabled()){
				builder.append(logRate());
				log.info(builder.toString());
			}
			
			return PostAggregationAction.Send;
		}
		
		//sortbids and sortoffers
		Arrays.sort(bids,0,bidCount, (o1, o2) -> {
			int a = Double.compare(o2.prc, o1.prc);
			return a == 0 ? Double.compare(o2.qty,o1.qty) : a;
		});

		
		Arrays.sort(offers,0,offerCount, (o1, o2) -> {
			int a = Double.compare(o1.prc, o2.prc);
			return a == 0 ? Double.compare(o2.qty,o1.qty) : a;
		});
		
		int c=0;
		int maxDepth = getMaxDepth();
		for(;c < bidCount ; c++ ){
			PriceTier entry = bids[c];
			if( c >= maxDepth ){
				break;
			}
			this.getBidPrices()[c] = MathUtil.round(entry.prc,precision,BigDecimal.ROUND_FLOOR);
			this.getBidQtys()[c] = MathUtil.round(entry.qty,tickValue,roundingType);
			this.getBidNoLP()[c] = entry.lpidx;
		}
		
		setNumBids(c);
		
		if( c < maxDepth ){
			Arrays.fill(getBidPrices(), c, maxDepth-1, 0.0d);
			Arrays.fill(getBidQtys(), c, maxDepth-1, 0.0d);
		}

		c=0;
		for(;c < offerCount ; c++ ){
			PriceTier entry =  offers[c];
				if( c >= maxDepth ){
					break;
				}
				this.getOfferPrices()[c] = MathUtil.round(entry.prc,precision,BigDecimal.ROUND_CEILING);
				this.getOfferQtys()[c] = MathUtil.round(entry.qty,tickValue,roundingType);
				this.getOfferNoLP()[c] = entry.lpidx;
		}
		
		setNumOffers(c);
		if( c < maxDepth ){
			Arrays.fill(getOfferPrices(), c, maxDepth-1, 0.0d);
			Arrays.fill(getOfferQtys(), c, maxDepth-1, 0.0d);
		}

		if(log.isDebugEnabled()){
			builder.append(logRate());
			log.info(builder.toString());
		}
		
		return PostAggregationAction.Send;
	}

	public String logRate() {
		StringBuilder msg = new StringBuilder("RBA ");
		int numBids = getNumBids();
		msg.append(getLogKey()).append(getTimeEffective()).append('|').append(numBids);
		
		for (int i = 0; i < numBids; i++) {
			msg.append('|').append(this.getBidPrices()[i])
			.append('|').append(this.getBidQtys()[i])
			.append('|').append(this.getBidNoLP()[i]);
		}
		
		int numOffers = getNumOffers();
		msg.append("|").append(numOffers);
		
		for (int i = 0; i < numOffers; i++) {
			msg.append('|').append(this.getOfferPrices()[i])
			.append('|').append(this.getOfferQtys()[i])
			.append('|').append(this.getOfferNoLP()[i]);
		}
		
		msg.append('#');
		msg.append(this.getBookId());
		msg.append('|').append(getQuoteId());
		msg.append('|').append(getQuoteTimeEffective());
		msg.append('|').append(getOldestQId());
		msg.append("|").append(hashCode());

		return msg.toString();
	}

}