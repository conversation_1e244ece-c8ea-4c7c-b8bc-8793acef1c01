package com.integral.mdf.data;

//Copyright (c) 2015 Integral Development Corp.  All rights reserved.

import com.integral.commons.buffers.UnSafeBuffer;

import java.util.Arrays;

public class CreditLimitInfo
{
	private static final int SIZE_OF_BYTE = 1;
	private static final int SIZE_OF_SHORT = 2;
	
	public final static short MSG_TYPE = 0;
	public final static byte VERSION = 0;
	public final static long NO_LIMIT = -1;

	public static int MSG_SIZE = 53;

	private long fiLe, lpLe;
	
	//NO_CHECK((byte) 0), ACTIVE((byte) 1), SUSPEND((byte) 2);
	private byte creditStatus;

	private short limitCcy;
	private long aggregateLimit,aggregateAvailable, dailyLimit, dailyAvailable;

	private short valueDate;

	private int hashcode;
	
	private CreditLimitInfo ()
	{
	}

	public CreditLimitInfo (final long fiLe, final long lpLe, final short valDate )
	{
		this();
		this.fiLe = fiLe;
		this.lpLe = lpLe;
		this.valueDate = valDate;
		this.hashcode = generateHashcode();
	}

	public long getFiLe()
	{
		return fiLe;
	}

	public long getLpLe()
	{
	  return lpLe;
	}

	public short getLimitCcy()
	{
		return limitCcy;
	}

	public void setLimitCcy(short limitCcy)
	{
		this.limitCcy = limitCcy;
	}

	public long getAggregateLimit()
	{
		return aggregateLimit;
	}

	public void setAggregateLimit(long aggregateLimit)
	{
		this.aggregateLimit = aggregateLimit;
	}

	public long getDailyLimit()
	{
		return dailyLimit;
	}

	public void setDailyLimit(long dailyLimit)
	{
		this.dailyLimit = dailyLimit;
	}

	public long getAggregateAvailable()
	{
		return aggregateAvailable;
	}

	public void setAggregateAvailable(long aggregateAvailable)
	{
		this.aggregateAvailable = aggregateAvailable;
	}

	public long getDailyAvailable()
	{
		return dailyAvailable;
	}

	public void setDailyAvailable( long dailyAvailable)
	{
		this.dailyAvailable = dailyAvailable;
	}

	public short getValueDate()
	{
		return valueDate;
	}

	public byte getCreditStatus()
	{
		return creditStatus;
	}

	public void setCreditStatus(byte creditStatus)
	{
		this.creditStatus = creditStatus;
	}
	
	public boolean isPartiallyRead(UnSafeBuffer safeBuf){
		//Since the buffer start's at 0 safeBuf.position() + 1 = number of bytes read
		return  0 != (safeBuf.position()- (SIZE_OF_BYTE +SIZE_OF_SHORT)) % MSG_SIZE;
	}
	
	public void readFully(UnSafeBuffer safeBuf){
		if(!isPartiallyRead(safeBuf)){
			return;
		}else{
			int reminder = (safeBuf.position()- (SIZE_OF_BYTE +SIZE_OF_SHORT)) % MSG_SIZE;
			int nextLineStartPos = safeBuf.position() + (MSG_SIZE-reminder);
			safeBuf.position(nextLineStartPos);
		}
	}

	public void reset()
	{
		this.creditStatus = 0;
		this.limitCcy = 0;
		this.aggregateLimit = 0;
		this.aggregateAvailable = 0;
		this.dailyLimit = 0;
		this.dailyAvailable = 0;
	}

	public void writeTo(UnSafeBuffer safeBuf)
	{
		safeBuf.putLong(this.fiLe);
		safeBuf.putLong(this.lpLe);
		safeBuf.put(creditStatus);
		safeBuf.putShort(this.limitCcy);
		safeBuf.putLong(this.aggregateLimit);
		safeBuf.putLong(this.aggregateAvailable);
		safeBuf.putLong(this.dailyLimit);
		safeBuf.putLong(this.dailyAvailable);
		safeBuf.putShort(this.valueDate);
	}

	public void readFrom(UnSafeBuffer safeBuf)
	{
		this.fiLe = safeBuf.getLong();
		this.lpLe = safeBuf.getLong();
		this.creditStatus = safeBuf.get();
		this.limitCcy = safeBuf.getShort();
		this.aggregateLimit = safeBuf.getLong();
		this.aggregateAvailable = safeBuf.getLong();
		this.dailyLimit = safeBuf.getLong();
		this.dailyAvailable = safeBuf.getLong();
		this.valueDate = safeBuf.getShort();
	}
	
	public void readOnlyLEInfo(UnSafeBuffer safeBuf)
	{
		this.fiLe = safeBuf.getLong();
		this.lpLe = safeBuf.getLong();
	}
	
	public void readAfterLEInfo(UnSafeBuffer safeBuf)
	{
		this.creditStatus = safeBuf.get();
		this.limitCcy = safeBuf.getShort();
		this.aggregateLimit = safeBuf.getLong();
		this.aggregateAvailable = safeBuf.getLong();
		this.dailyLimit = safeBuf.getLong();
		this.dailyAvailable = safeBuf.getLong();
		this.valueDate = safeBuf.getShort();
	}
	

	public String toString()
	{
		return new StringBuilder( 200 ).append( "cli={" ).append( fiLe ).append( ':' ).append( lpLe ).append( ':' )
				.append( valueDate ).append( ':' ).append( creditStatus ).append( ",limitCcy=" ).append (limitCcy )
				.append( ",agg=" ).append( aggregateAvailable ).append( '(' ).append( aggregateLimit ).append( "),daily=" )
				.append( dailyAvailable ).append( '(' ).append( dailyLimit ).append( "),valDate=" ).append( valueDate )
				.append( '}' ).toString();
	}

	public boolean equals ( Object object )
	{
		if ( object instanceof CreditLimitInfo )
		{
			CreditLimitInfo other = (CreditLimitInfo) object;
			return other.getFiLe() == fiLe && other.getLpLe() == lpLe && other.getValueDate() == valueDate;
		}
		return false;
	}

	public int hashCode()
	{
		return hashcode;
	}

	public int getEstimatedSize()
	{
		return MSG_SIZE;
	}

	private int generateHashcode()
	{
		long[] arr = new long[] { fiLe, lpLe, (long) valueDate };
		return Arrays.hashCode( arr );
	}
}

