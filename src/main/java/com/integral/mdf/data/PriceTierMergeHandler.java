package com.integral.mdf.data;

import com.integral.mdf.data.PriceTier;
import com.integral.mdf.rate.treeset.MergeOnConflictTreeMap.ValueMergeHandler;

public class PriceTierMergeHandler implements ValueMergeHandler<PriceTier> {
    @Override
    public PriceTier merge(PriceTier tier1, PriceTier tier2) {
        PriceTier t = tier1.effectiveTime < tier2.effectiveTime ? tier1 : tier2;
        t.qty = tier1.qty + tier2.qty;      // total qty at this price.
        t.lpidx = tier1.lpidx + tier2.lpidx;//count num of lp merged
        return t;
    }
}
