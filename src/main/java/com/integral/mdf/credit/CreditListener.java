package com.integral.mdf.credit;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.util.Optional;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.data.CreditLimitInfo;
import com.integral.mdf.data.ServerProvision;
import com.integral.mdf.rate.RateDistributionManager;
import com.integral.notifications.MaxCreditInfo;
import com.integral.notifications.MsgTypes;
import com.integral.notifications.StreamUpdate;
import com.integral.pipeline.metrics.Metrics;
import com.integral.pipeline.metrics.MetricsManager;

public class CreditListener {

    private static final String CRDLMT_MC_READER = "crdlmt-mc-reader";
    private final static int MTU = 1400;

    Log log = LogFactory.getLog(CreditListener.class);
    final int port;
    final MulticastSocket socket;
    ServerProvision provision;
    volatile boolean shutdown = false;
    private RateDistributionManager dm;
    CLMetrics clm = new CLMetrics();

    public CreditListener(ServerProvision sp, RateDistributionManager dm) throws IOException {
        this.provision = sp;
        this.dm = dm;
        this.port = provision.getCreditMulticastPort();

        socket = new MulticastSocket(port);
        socket.setReuseAddress(true);

        Optional<InetAddress> multicastgroup = provision.getCreditMulticastGroup();
        if (!multicastgroup.isPresent()) {
            log.info("Not joining the group since there's no multicast group defined");
            return;
        }

        MetricsManager.instance().register(clm);

        InetAddress mcastaddress = multicastgroup.get();
        socket.joinGroup(mcastaddress);
        new Thread(new Worker(), CRDLMT_MC_READER).start();
        log.info("CreditListener.init():Started listening for credit on port:" + port + ", Multicast group:" +
                multicastgroup.toString());
    }

    class Worker implements Runnable {

        byte[] buffer;
        UnSafeBuffer safeBuf;

        public Worker() {
            buffer = new byte[MTU];
            safeBuf = new UnSafeBuffer();
        }

        @Override
        public void run() {
            CreditLimitInfo creditLimitInfo = new CreditLimitInfo(0, 0, (short) 0);
            MaxCreditInfo maxCreditInfo = new MaxCreditInfo();
            StreamUpdate streamUpdate = new StreamUpdate();
            DatagramPacket dp = new DatagramPacket(buffer, buffer.length);
            do {
                try {
                    socket.receive(dp);
                    safeBuf.init(buffer);
                    //read a byte - version
                    byte vesion = safeBuf.get();
                    // read a Short - msg type
                    MsgTypes msgType = MsgTypes.get(safeBuf.getShort());
                    if(msgType==null)
                        return;
                    switch (msgType) {
                        case CreditLimitInfo:
                            //dm.handleCreditUpdate(safeBuf, creditLimitInfo);
                            clm.cli++;
                            break;
                        case MaxCreditInfo:
                            boolean isSameVenue = dm.handleMaxCredit(safeBuf,maxCreditInfo);
                            maxCreditInfo.reset();
                            clm.mci++;
                            if(isSameVenue)
                                clm.dv++;
                            break;
                        case StreamUpdate:
                            try {
                                dm.handleStreamUpdate(safeBuf, streamUpdate);
                            }catch (Throwable e) {
                                log.warn("CreditListener.Worker.run():Unable to receive and process Stream update multicast datagram " +
                                        "packet", e);
                            } finally {
                                streamUpdate.reset();
                            }
                            clm.su++;
                            break;
                        default:
                            clm.uk++;
                    }
                } catch (Throwable e) {
                    log.warn("CreditListener.Worker.run():Unable to receive and process " +
                            "CreditInfo/StreamUpdate/MaxCredit multicast datagram " +
                            "packet", e);
                } finally {
                    safeBuf.resetMemoryBuffer();
                }
            } while (!shutdown);
        }
    }

    class CLMetrics implements Metrics  {

        long su;
        long cli;
        long mci;
        long uk;
        long dv;

        StringBuilder message = new StringBuilder(100).append("k=clm,");

        @Override
        public StringBuilder report() {
            message.setLength(6);
            message.append("su=").append(su);
            message.append(",cli=").append(cli);
            message.append(",mci=").append(mci);
            message.append(",uk=").append(uk);
            su=0;cli=0;mci=0;uk=0; //reset metrics
            return message;
        }
    }

}
