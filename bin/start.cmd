@echo off

set INST_HOME=C:\svn_views\marketdata\mdf

set JAVA_HOME=C:\Java\ojdk1101

set CLASSPATH=%INST_HOME%\conf
set CLASSPATH=%CLASSPATH%;%INST_HOME%\bin\mdf-1.0.jar
set CLASSPATH=%CLASSPATH%;%INST_HOME%\lib\*;

set MEM=2048

set JAVA_DEBUG=-Xdebug -Xnoagent -Xrunjdwp:transport=dt_socket,server=y,address=8006,suspend=n

set JAVA_OPTION=-server -Xms%MEM%m -Xmx%MEM%m -Xlog:gc -XX:MaxMetaspaceSize=500m -XX:+UseG1GC -XX:MaxGCPauseMillis=50 -XX:+UseCompressedClassPointers -XX:+UnlockExperimentalVMOptions -XX:-OmitStackTraceInFastThrow -XX:+ParallelRefProcEnabled -Dprogram.name= -Duser.timezone=GMT

echo Using JAVA_DEBUG: 	"%JAVA_DEBUG%"
echo Using JAVA_OPTION: 	"%JAVA_OPTION%"
echo Using JAVA_HOME:       "%JAVA_HOME%"
echo Using CLASSPATH:       "%CLASSPATH%"

echo Starting server.............

%JAVA_HOME%/bin/java -cp %CLASSPATH% %JAVA_OPTION%  %JAVA_DEBUG% -Dlogback.configurationFile=%INST_HOME%/conf/logback.xml com.integral.mdf.Main mdf4 http://localhost:9080
