#!/bin/sh
#
# This script is under INST_HOME/bin folder, where the 
# INST_HOME=/integral/app/<AppName>
#
AppName=mdf-MDFServerCCX

INST_HOME=${INST_HOME:-"/integral/app/$AppName"}
LOG_HOME=${LOG_HOME:-"/integral/logs/$AppName"}
CRONOLOG_HOME=${CRONOLOG_HOME:-"/integral/opt/cronolog"}

PROG=marketDataFeed
PROGPIDFILE=$INST_HOME/.$PROG.pid
PROGNAME="com.integral.mdf.Main"

if [ ! -d $INST_HOME ]; then
   echo "This package has to be installed under /integral/app/<appName>"
   exit 1
fi

case $1 in
start)
    `ps -ef | grep java | grep $PROGNAME | grep "$INST_HOME/"` > /dev/null
     if [ $? -eq 0 ]; then
         echo "$INST_HOME is already running, you need to kill it before start again"
         exit 1
     fi

    echo "starting... "

    if [ -L $INST_HOME/integral.out ]; then
        unlink $INST_HOME/integral.out
    elif [ -f $INST_HOME/integral.out ]; then
        rm -rf $INST_HOME/integral.out
    fi

    if [ "$USER" = "root" ]; then
         (su jboss -c "cd $INST_HOME/bin; sh $INST_HOME/bin/start.sh 2>&1 | $CRONOLOG_HOME/cronolog $LOG_HOME/integral.out.%Y-%m-%d-%H -S $INST_HOME/integral.out 2>&1 ") &
    elif [ "$USER" = "jboss" ]; then
          (cd $INST_HOME/bin; sh $INST_HOME/bin/start.sh 2>&1 | $CRONOLOG_HOME/cronolog $LOG_HOME/integral.out.%Y-%m-%d-%H -S $INST_HOME/integral.out 2>&1 ) &
    else
         echo "$USER: don't have permission to run this propgram"
         exit
    fi

    sleep 2
    pid=`ps -ef | grep "$PROGNAME" | grep "$INST_HOME/" | awk -F " " '{print $2}'`
    if [ "x$pid" = "x" ]
    then
       echo SERVER DID NOT START
       exit -1
    else
       echo STARTED SUCCESFULLY
       echo $pid > $PROGPIDFILE
    fi
    ;;
stop)
    pid=`ps -ef | grep "$INST_HOME/" | grep "$PROGNAME" | awk -F " " '{print $2}' `
    if [ "x$pid" = "x" ]; then
        echo "$INST_HOME is not running"
        exit 0
    fi 
    if [ "$USER" = "root" ]; then
            (su jboss -c "cd $INST_HOME/bin; sh ./shutdown.sh 2>&1 | $CRONOLOG_HOME/cronolog $LOG_HOME/integral.out.%Y-%m-%d-%H -S $INST_HOME/integral.out &>/dev/null &")
    elif [ "$USER" = "jboss" ]; then
         (cd $INST_HOME/bin; sh ./shutdown.sh)
    else
         echo "$USER: don't have permission to stop this program"
         exit
    fi

    if [ $? -eq 0 ]; then
         echo "Stopping $INST_HOME app .....    "
    else
        echo "Not able to stop, please check ... "
    fi
    exit 0
    ;;
status)
    pid=`ps -ef | grep java | grep "$PROGNAME" | grep "$INST_HOME/" | awk -F " " '{print $2}'`
    if [ "x$pid" != "x" ]; then
         echo "$INST_HOME is running: $pid"
    else
         echo "$INST_HOME is NOT running"
    fi
    exit 0
    ;;
restart)
    shift
    "$0" stop ${@}
    if [ -f $PROGPIDFILE ]; then
       if [ -s $PROGPIDFILE ]; then
           pid=`cat $PROGPIDFILE`
       fi
    fi
    if [ "x$pid"="x" ]; then
         pid=`ps -ef | grep $PROGNAME | grep "$INST_HOME/" |  grep java | awk -F" " '{print $2}'`
    fi
    STOPPED=0
    if [ "x$pid" != "x" ]; then
         echo "Waiting for the app to be fully stopped.... "
         status=0
         j=0
         while [[ $status -eq 0 ]]
         do
            ps -p $pid > /dev/null 2>&1
            status=$?
            sleep 2
            let j++
            if [[ $j -gt 20 ]]; then
                STOPPED=1
                break
            fi
         done
    fi
    if [[ $STOPPED -eq 1 ]]; then
        echo "Not able to stop $INST_HOME, please check"
        exit -1
    else
        echo "$INST_HOME is not running"
    fi

    "$0" start ${@}
    ;;
*)
    echo "Usage: $0 {start|stop|restart|status}" >&2
    ;;
esac

exit    

