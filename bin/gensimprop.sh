#!/bin/sh

AppName=mdf
INST_HOME=${INST_HOME:-"/integral/app/$AppName"}

JAVA_HOME=/integral/opt/jdk18
MEM=512

JAVA_OPTS = -Xms${MEM}m -Xmx${MEM}m -server -Xms${MEM}m -Xmx${MEM}m -XX:MaxMetaspaceSize=500m -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+PrintAdaptiveSizePolicy -XX:+UseG1GC -XX:MaxGCPauseMillis=50
echo ===============================================================================
echo .
echo   SimulatorPropertyFileGenerator
echo .
echo   JAVA: "$JAVA_HOME\bin\java"
echo .
echo   JAVA_OPTS: $JAVA_OPTS
echo .
echo   CLASSPATH: $CLASSPATH
echo .
echo ===============================================================================
echo .

$JAVA_HOME/bin/java -cp "$INST_HOME/conf/:$INST_HOME/bin/*:$INST_HOME/lib/*" $JAVA_OPTS -Dprogram.name= -Duser.timezone=GMT -Dlogback.configurationFile=$INST_HOME/conf/logback.xml com.integral.mdf.simulator.SimulatorPropertyFileGenerator $1 $2 $3 $INST_HOME/conf/mdfsimulator.properties
