#!/bin/bash

AppName=mdf
INST_HOME=${INST_HOME:-"/integral/app/$AppName"}
LOG_HOME=${LOG_HOME:-"/integral/logs/$AppName"}
CRONOLOG_HOME=/usr/bin
LOGFILE=${LOG_HOME}/integral.out.`date +%Y-%m-%d-%H`

JAVA_HOME=/integral/opt/jdk17

Version=`grep Integral.releaseNumber $INST_HOME/conf/Version.properties | cut -d"=" -f2`
BuildNum=`grep Integral.buildNumber $INST_HOME/conf/Version.properties | cut -d"=" -f2`

# The following get from docker-compose file
#MEM
#VSNAME
#RDS_URL

echo =============================================================================== >> $LOGFILE
echo . >> $LOGFILE
echo   Start MarketDataFeed Server - mdf-$Version-$BuildNum   >> $LOGFILE
echo . >> $LOGFILE
echo   MEM: $MEM >> $LOGFILE
echo . >> $LOGFILE
echo   JAVA_HOME: $JAVA_HOME >> $LOGFILE
echo . >> $LOGFILE
echo =============================================================================== >> $LOGFILE
echo . >> $LOGFILE

cd $LOG_HOME
if [ -L integral.out ]; then
    unlink integral.out
elif [ -f integral.out ]; then
    rm -f integral.out
fi

$JAVA_HOME/bin/java -cp "$INST_HOME/conf/:$INST_HOME/bin/*:$INST_HOME/lib/*" -Xms${MEM}m -Xmx${MEM}m -server -XX:+UseZGC -Xlog:gc -XX:MaxMetaspaceSize=500m -XX:+UseCompressedClassPointers -XX:+UnlockExperimentalVMOptions -XX:-OmitStackTraceInFastThrow -XX:+ParallelRefProcEnabled -XX:MaxGCPauseMillis=50 -Djava.net.preferIPv4Stack=true -Dprogram.name= -Duser.timezone=GMT -Dlogback.configurationFile=$INST_HOME/conf/logback.xml --add-opens java.base/sun.nio.ch=ALL-UNNAMED --add-opens java.base/java.nio=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED com.integral.mdf.Main $VSNAME $RDS_URL 2>&1 | $CRONOLOG_HOME/cronolog integral.out.%Y-%m-%d-%H -S integral.out

