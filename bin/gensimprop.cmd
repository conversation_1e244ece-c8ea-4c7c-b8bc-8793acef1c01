@echo off

set INST_HOME=C:\svn_views\marketdata\mdf

set JAVA_HOME=C:/sunjava/jdk1.8.0_121

set CLASSPATH=%INST_HOME%\conf
set CLASSPATH=%CLASSPATH%;%INST_HOME%\bin\mdf-1.0.jar
set CLASSPATH=%CLASSPATH%;%INST_HOME%\lib\*;

set MEM=512

set JAVA_DEBUG=-Xdebug -Xnoagent -Xrunjdwp:transport=dt_socket,server=y,address=8006,suspend=n

set JAVA_OPTION=-server -Xms%MEM%m -Xmx%MEM%m -XX:MaxMetaspaceSize=500m -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+PrintAdaptiveSizePolicy -XX:+UseG1GC -XX:MaxGCPauseMillis=50 -Dprogram.name= -Duser.timezone=GMT

echo Using JAVA_DEBUG: 	"%JAVA_DEBUG%"
echo Using JAVA_OPTION: 	"%JAVA_OPTION%"
echo Using JAVA_HOME:       "%JAVA_HOME%"
echo Using CLASSPATH:       "%CLASSPATH%"

echo Starting SimulatorPropertyFileGenerator.............

%JAVA_HOME%/bin/java -cp %CLASSPATH% %JAVA_OPTION%  %JAVA_DEBUG% -Dlogback.configurationFile=%INST_HOME%/conf/logback.xml com.integral.mdf.simulator.SimulatorPropertyFileGenerator %1 %2 %3 %INST_HOME%/conf/mdfsimulator.properties
