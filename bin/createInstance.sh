#!/bin/sh
#
# Create if the instance does not exist, or update the existing instancea
#
# It will call "change_app_home.sh" and "updateProdLink.sh"
# Usage:  ./createOrUpdateInstance.sh <app> <newInstanceName>
#     where:  app:             name of the app, such rex, tvmaster, usrv....
#             newInstanceName: the instance you want to use, such as rex-01, tvmaster-02, orders-adap-OA...  
#

SCRIPTDIR=$(dirname $(readlink -f $0))
appHome=`dirname $SCRIPTDIR`
appBase=`basename $appHome`
prod=`echo $appBase | awk -F"-" '{gsub($NF,"");sub(".$", "");print}'`
rpmSvn=`echo $appBase | cut -d"-" -f3`

shopt -s nocasematch

name=$1
link=$2
if [ "x$name" == "x" ]; then
   echo "Usage: $0 <new instance name> <prod>"
   echo "Please priovide a new instance name"
   exit 1
fi

newAppDir="/integral/app/$name"
LOG=/tmp/${name}_log

echo `date` > $LOG

if [ "$name" == "" ]; then
    echo "Please provide new instance name"
    exit
fi

if [ -L $newAppDir ]; then
    unlink $newAppDir
elif [ -d $newAppDir ]; then
    rm -rf $newAppDir
fi

mkdir -p $newAppDir
cp -r $appHome/bin $newAppDir/
cp -r $appHome/conf $newAppDir/
ln -s $appHome/lib $newAppDir/lib

cd $newAppDir/bin
sed -i s#^AppName=\.*#AppName=$name# S99integral
sed -i s#^AppName=\.*#AppName=$name# start.sh
sed -i s#^AppName=\.*#AppName=$name# shutdown.sh

cd $newAppDir/conf
sed -i s#$prod#$name#g logback.xml

mkdir -p /integral/logs/$name
chown -R jboss:jboss $newAppDir
chown -R jboss:jboss /integral/logs/$name

# create folder link
if [ "x$link" != "x" ]; then
    echo "create app link "
    cd /integral/app
    if [[ "$rpmSvn" = "-" ]]; then
        if [ -d ${name}_${prod} ]; then
            mv ${name}_${prod} ${name}_${prod}.$$
        fi
        mv $name ${name}_${prod}
        ln -s ${name}_${prod} $name
    else
        if [ -d ${name}_${prod}-${rpmSvn} ]; then
            mv ${name}_${prod}-${rpmSvn} ${name}_${prod}-${rpmSvn}.$$
        fi
        mv $name ${name}_${prod}-${rpmSvn}
        ln -s ${name}_${prod}-${rpmSvn} $name
    fi
    echo "New instance has been created as following: /integral/app/${name}"
    echo `/bin/ls -l ${name}`
else
    echo "New instance has been created: /integral/app/${name}"
fi

