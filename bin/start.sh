#!/bin/bash

AppName=marketDataFeed
INST_HOME=${INST_HOME:-"/integral/app/$AppName"}

JAVA_HOME=/integral/opt/jdk17
MEM=2048

echo ===============================================================================
echo .
echo   MarketDataFeed
echo .
echo   JAVA: "$JAVA_HOME\bin\java"
echo .
echo   CLASSPATH: $CLASSPATH
echo .
echo ===============================================================================
echo .


$JAVA_HOME/bin/java -cp "$INST_HOME/conf/:$INST_HOME/bin/*:$INST_HOME/lib/*" -Xms${MEM}m -Xmx${MEM}m -server -XX:+UseZGC -Xlog:gc -XX:MaxMetaspaceSize=500m -XX:+UseCompressedClassPointers -XX:+UnlockExperimentalVMOptions -XX:-OmitStackTraceInFastThrow -XX:+ParallelRefProcEnabled -XX:MaxGCPauseMillis=50 -Djava.net.preferIPv4Stack=true -Dprogram.name= -Duser.timezone=GMT -Dlogback.configurationFile=$INST_HOME/conf/logback.xml --add-opens java.base/sun.nio.ch=ALL-UNNAMED --add-opens java.base/java.nio=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED com.integral.mdf.Main @VIRTALSERVERNAME@ @RDSURL@ 
